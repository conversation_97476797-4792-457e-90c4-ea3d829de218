{"version": 3, "file": "userAgentPlatform-react-native.mjs", "sourceRoot": "", "sources": ["../../../src/util/userAgentPlatform-react-native.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC;;GAEG;AACH,MAAM,UAAU,aAAa;IAC3B,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAAC,GAAwB;;IACpE,IAAI,MAAA,QAAQ,CAAC,SAAS,0CAAE,kBAAkB,EAAE,CAAC;QAC3C,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,kBAAkB,CAAC;QACtE,GAAG,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC,CAAC;IACxD,CAAC;IACD,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;AACtD,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { Platform } from \"react-native\";\n\n/**\n * @internal\n */\nexport function getHeaderName(): string {\n  return \"x-ms-useragent\";\n}\n\n/**\n * @internal\n */\nexport async function setPlatformSpecificData(map: Map<string, string>): Promise<void> {\n  if (Platform.constants?.reactNativeVersion) {\n    const { major, minor, patch } = Platform.constants.reactNativeVersion;\n    map.set(\"react-native\", `${major}.${minor}.${patch}`);\n  }\n  map.set(\"OS\", `${Platform.OS}-${Platform.Version}`);\n}\n"]}
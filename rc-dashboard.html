<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RC Dashboard - Route Card Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 30px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #4a90e2, #357abd);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .logo-text h1 {
            color: #2c3e50;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .logo-text p {
            color: #7f8c8d;
            font-size: 14px;
        }

        .controls {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .location-filter {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .location-filter select {
            padding: 8px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            color: #2c3e50;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .location-filter select:focus {
            outline: none;
            border-color: #4a90e2;
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }

        .auto-indicators {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 12px;
            color: #7f8c8d;
        }

        .indicator {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .indicator-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #27ae60;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .fullscreen-btn {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 8px 12px;
            margin-left: 15px;
            cursor: pointer;
            font-size: 16px;
            color: #2c3e50;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .fullscreen-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .stat-card.total { --accent-color: #3498db; }
        .stat-card.average { --accent-color: #9b59b6; }
        .stat-card.aging-15 { --accent-color: #2ecc71; }
        .stat-card.aging-45 { --accent-color: #f39c12; }
        .stat-card.aging-90 { --accent-color: #e67e22; }
        .stat-card.aging-over { --accent-color: #c0392b; }

        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .stat-value {
            font-size: 36px;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 5px;
        }

        .stat-sublabel {
            font-size: 12px;
            color: #95a5a6;
        }

        .dashboard-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .section-header {
            background: linear-gradient(135deg, #4a90e2, #357abd);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
        }

        .slide-indicator {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
        }

        .table-container {
            height: 600px;
            overflow: hidden;
            position: relative;
        }

        .table-wrapper {
            height: 100%;
            overflow-y: auto;
            transition: all 0.5s ease;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }

        th {
            background: #f8f9fa;
            color: #2c3e50;
            font-weight: 600;
            padding: 12px 8px;
            text-align: left;
            border-bottom: 2px solid #e9ecef;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        td {
            padding: 10px 8px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .aging-cell {
            text-align: center;
            font-weight: 700;
            padding: 6px 10px;
            border-radius: 6px;
            color: #ffffff !important;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.8);
            font-size: 14px;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .aging-cell.aging-15 {
            background: #27ae60 !important;
            color: #ffffff !important;
        }
        .aging-cell.aging-45 {
            background: #f39c12 !important;
            color: #ffffff !important;
        }
        .aging-cell.aging-90 {
            background: #e67e22 !important;
            color: #ffffff !important;
        }
        .aging-cell.aging-over {
            background: #c0392b !important;
            color: #ffffff !important;
        }

        .qty-cell {
            text-align: right;
            font-weight: 500;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #7f8c8d;
        }

        .error-message {
            text-align: center;
            padding: 40px;
            color: #e74c3c;
        }

        .retry-btn {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 15px;
        }

        .retry-btn:hover {
            background: #357abd;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .main-container {
                padding: 15px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .stat-value {
                font-size: 28px;
            }

            table {
                font-size: 10px;
            }

            th, td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">RC</div>
                <div class="logo-text">
                    <h1>RC DASHBOARD</h1>
                    <p>Route Card Management System</p>
                </div>
            </div>
            
            <div class="controls">
                <div class="location-filter">
                    <label for="locationFilter">Location:</label>
                    <select id="locationFilter">
                        <option value="">All Locations</option>
                    </select>
                </div>
                
                <div class="auto-indicators">
                    <div class="indicator">
                        <div class="indicator-dot"></div>
                        <span>Auto-refresh: 15 min</span>
                    </div>
                    <div class="indicator">
                        <div class="indicator-dot"></div>
                        <span>Auto-slide: After scroll</span>
                    </div>
                    <button id="fullscreenBtn" class="fullscreen-btn" onclick="toggleFullscreen()" title="Toggle Fullscreen">
                        <span id="fullscreenIcon">⛶</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="main-container">
        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-label">Pending RCs</div>
                <div class="stat-value" id="totalPending">-</div>
                <div class="stat-sublabel">Total Count</div>
            </div>
            <div class="stat-card average">
                <div class="stat-label">Average Aging</div>
                <div class="stat-value" id="avgAging">-</div>
                <div class="stat-sublabel">Days</div>
            </div>
            <div class="stat-card aging-15">
                <div class="stat-label">≤ 15 Days</div>
                <div class="stat-value" id="aging15">-</div>
                <div class="stat-sublabel">Recent</div>
            </div>
            <div class="stat-card aging-45">
                <div class="stat-label">16-45 Days</div>
                <div class="stat-value" id="aging45">-</div>
                <div class="stat-sublabel">Moderate</div>
            </div>
            <div class="stat-card aging-90">
                <div class="stat-label">46-90 Days</div>
                <div class="stat-value" id="aging90">-</div>
                <div class="stat-sublabel">Critical</div>
            </div>
            <div class="stat-card aging-over">
                <div class="stat-label">> 90 Days</div>
                <div class="stat-value" id="agingOver90">-</div>
                <div class="stat-sublabel">Overdue</div>
            </div>
        </div>

        <!-- Recent Route Cards Table -->
        <div class="dashboard-section">
            <div class="section-header">
                <div class="section-title">Recent Route Cards</div>
                <div class="slide-indicator" id="slideIndicator">Page 1 of 1</div>
            </div>
            <div class="table-container" id="tableContainer">
                <div class="loading">Loading data...</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        let currentLocationId = '';
        let currentTableData = [];
        let currentPage = 0;
        let totalPages = 1;
        const RECORDS_PER_PAGE = 50;
        let autoSlideInterval;
        let autoScrollInterval;

        // Initialize dashboard
        async function initDashboard() {
            try {
                await loadLocations();
                await loadData();
                startAutoRefresh();
                startAutoSlide();
            } catch (error) {
                console.error('Dashboard initialization failed:', error);
            }
        }

        // Load locations for filter
        async function loadLocations() {
            try {
                const response = await fetch(`${API_BASE}/locations`);
                const data = await response.json();
                
                if (data.success) {
                    const select = document.getElementById('locationFilter');
                    select.innerHTML = '<option value="">All Locations</option>';
                    
                    data.data.forEach(location => {
                        const option = document.createElement('option');
                        option.value = location.CompanyID;
                        option.textContent = location.Location;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading locations:', error);
            }
        }

        // Load all data
        async function loadData() {
            await Promise.all([loadStats(), loadTableData()]);
        }

        // Load statistics
        async function loadStats() {
            try {
                const url = `${API_BASE}/dashboard-stats${currentLocationId ? `?locationId=${currentLocationId}` : ''}`;
                console.log('Loading stats from URL:', url);
                const response = await fetch(url);
                const data = await response.json();

                if (data.success) {
                    document.getElementById('totalPending').textContent = (data.data.PendingRCs || 0).toLocaleString();
                    document.getElementById('avgAging').textContent = Math.round(data.data.AvgAgingDays || 0);
                    document.getElementById('aging15').textContent = (data.data.RCsWithin15Days || 0).toLocaleString();
                    document.getElementById('aging45').textContent = (data.data.RCsBetween16And45Days || 0).toLocaleString();
                    document.getElementById('aging90').textContent = (data.data.RCsBetween46And90Days || 0).toLocaleString();
                    document.getElementById('agingOver90').textContent = (data.data.RCsOver90Days || 0).toLocaleString();
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Load table data
        async function loadTableData() {
            try {
                const url = `${API_BASE}/rc-pending${currentLocationId ? `?locationId=${currentLocationId}` : ''}`;
                console.log('Loading table data from URL:', url);
                console.log('Current location ID:', currentLocationId);
                const response = await fetch(url);
                const data = await response.json();

                if (data.success && data.data) {
                    currentTableData = data.data;
                    totalPages = Math.ceil(currentTableData.length / RECORDS_PER_PAGE);
                    currentPage = 0;
                    displayTablePage(0);
                } else {
                    throw new Error('No data received');
                }
            } catch (error) {
                console.error('Error loading table data:', error);
                document.getElementById('tableContainer').innerHTML = `
                    <div class="error-message">
                        <h3>❌ Error Loading Data</h3>
                        <p>${error.message}</p>
                        <button onclick="loadTableData()" class="retry-btn">Retry</button>
                    </div>
                `;
            }
        }

        // Display table page
        function displayTablePage(pageIndex) {
            if (!currentTableData || currentTableData.length === 0) {
                document.getElementById('tableContainer').innerHTML = '<div class="loading">No data available</div>';
                return;
            }

            const startIndex = pageIndex * RECORDS_PER_PAGE;
            const endIndex = Math.min(startIndex + RECORDS_PER_PAGE, currentTableData.length);
            const pageData = currentTableData.slice(startIndex, endIndex);

            let tableHTML = `
                <div class="table-wrapper" id="tableWrapper">
                    <table>
                        <thead>
                            <tr>
                                <th>Work Order</th>
                                <th>Product Name</th>
                                <th>WO Date</th>
                                <th>WO Qty</th>
                                <th>Rej Qty</th>
                                <th>Desp Qty</th>
                                <th>Aging</th>
                                <th>Location</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            pageData.forEach(record => {
                const productName = (record.PartNoAndProductName || 'Unknown').split(' | ')[1] || 'Unknown';
                const agingClass = getAgingClass(record.AgingInDays);

                tableHTML += `
                    <tr>
                        <td>${record.WoNo || 'N/A'}</td>
                        <td title="${record.PartNoAndProductName || 'Unknown'}">${productName}</td>
                        <td>${record.WODate || 'N/A'}</td>
                        <td class="qty-cell">${(record.WO_Qty || 0).toLocaleString()}</td>
                        <td class="qty-cell">${(record.TotalRejQty || 0).toLocaleString()}</td>
                        <td class="qty-cell">${(record.DespQty || 0).toLocaleString()}</td>
                        <td class="aging-cell ${agingClass}">${record.AgingInDays || 0}</td>
                        <td>${record.Location || 'Unknown'}</td>
                    </tr>
                `;
            });

            tableHTML += `
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('tableContainer').innerHTML = tableHTML;
            document.getElementById('slideIndicator').textContent = `Page ${pageIndex + 1} of ${totalPages}`;

            // Start auto-scroll for current page
            startAutoScroll();
        }

        // Get aging CSS class
        function getAgingClass(days) {
            if (days <= 15) return 'aging-15';
            if (days <= 45) return 'aging-45';
            if (days <= 90) return 'aging-90';
            return 'aging-over';
        }

        // Auto-scroll within current page - COMPLETELY REWRITTEN
        function startAutoScroll() {
            clearInterval(autoScrollInterval);

            const tableWrapper = document.getElementById('tableWrapper');
            if (!tableWrapper) return;

            let currentScroll = 0;
            let targetScroll = 0;
            let maxScroll = 0;
            let phase = 'scrolling'; // 'scrolling', 'pausing', 'resetting'
            let pauseCounter = 0;
            const PAUSE_TIME = 150; // 3 seconds pause at bottom (150 * 20ms)
            const SCROLL_SPEED = 1;
            const INTERVAL_DELAY = 20;

            function updateScrollInfo() {
                maxScroll = tableWrapper.scrollHeight - tableWrapper.clientHeight;
                if (maxScroll <= 0) maxScroll = 0;
            }

            autoScrollInterval = setInterval(() => {
                updateScrollInfo();

                if (phase === 'scrolling') {
                    // Scroll down gradually
                    if (currentScroll < maxScroll) {
                        currentScroll += SCROLL_SPEED;
                        if (currentScroll > maxScroll) {
                            currentScroll = maxScroll;
                        }
                        tableWrapper.scrollTop = currentScroll;
                    } else {
                        // Reached bottom - force to exact bottom and start pausing
                        currentScroll = maxScroll;
                        tableWrapper.scrollTop = maxScroll;
                        phase = 'pausing';
                        pauseCounter = 0;
                        console.log('Reached bottom, starting pause...');
                    }
                } else if (phase === 'pausing') {
                    // Stay at bottom for pause duration
                    tableWrapper.scrollTop = maxScroll; // Keep forcing to bottom
                    pauseCounter++;

                    if (pauseCounter >= PAUSE_TIME) {
                        phase = 'resetting';
                        console.log('Pause complete, resetting to top...');
                    }
                } else if (phase === 'resetting') {
                    // Reset to top and slide to next page
                    currentScroll = 0;
                    tableWrapper.scrollTop = 0;
                    phase = 'scrolling';
                    pauseCounter = 0;
                    console.log('Reset to top, sliding to next page...');

                    // Slide to next page after scroll completion
                    slideToNextPage();
                }
            }, INTERVAL_DELAY);
        }

        // Slide to next page (called by auto-scroll when complete)
        function slideToNextPage() {
            if (totalPages > 1) {
                currentPage = (currentPage + 1) % totalPages;
                console.log(`Sliding to page ${currentPage + 1} of ${totalPages}`);
                displayTablePage(currentPage);
            }
        }

        // Auto-slide between pages (now disabled - controlled by scroll completion)
        function startAutoSlide() {
            // Auto-slide is now controlled by scroll completion
            // No timer-based sliding anymore
            console.log('Auto-slide initialized - will slide after each scroll completion');
        }

        // Auto-refresh data
        function startAutoRefresh() {
            setInterval(() => {
                console.log('Auto-refreshing data...');
                loadData();
            }, 15 * 60 * 1000); // 15 minutes
        }

        // Fullscreen functionality
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().then(() => {
                    document.getElementById('fullscreenIcon').textContent = '⛷';
                }).catch(err => {
                    console.log('Error attempting to enable fullscreen:', err);
                });
            } else {
                document.exitFullscreen().then(() => {
                    document.getElementById('fullscreenIcon').textContent = '⛶';
                }).catch(err => {
                    console.log('Error attempting to exit fullscreen:', err);
                });
            }
        }

        // Listen for fullscreen changes
        document.addEventListener('fullscreenchange', () => {
            if (document.fullscreenElement) {
                document.getElementById('fullscreenIcon').textContent = '⛷';
            } else {
                document.getElementById('fullscreenIcon').textContent = '⛶';
            }
        });

        // Event listeners
        document.getElementById('locationFilter').addEventListener('change', (e) => {
            currentLocationId = e.target.value;
            console.log('Location filter changed to:', e.target.value);
            loadData();
        });

        // Initialize on page load
        window.addEventListener('load', initDashboard);
    </script>
</body>
</html>

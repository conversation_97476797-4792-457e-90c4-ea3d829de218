<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RC Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
            margin: 0;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 5px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .header h1 {
            color: #1f2937;
            font-size: 2rem;
            margin-bottom: 5px;
        }
        
        .header p {
            color: #6b7280;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        select, button {
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
            font-size: 14px;
        }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            cursor: pointer;
        }
        
        button:hover {
            background: #2563eb;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        @media (max-width: 768px) {
            .stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }
        }

        @media (max-width: 480px) {
            .stats {
                grid-template-columns: 1fr;
                gap: 8px;
            }
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            margin-bottom: 10px;
            font-size: 1rem;
        }
        
        .stat-card .value {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
        }
        
        .data-table {
            background: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow-x: auto;
            height: calc(100vh - 300px);
            overflow-y: hidden;
            transition: all 0.3s ease;
        }

        @media (max-width: 768px) {
            .data-table {
                padding: 10px;
                height: calc(100vh - 260px);
            }
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            table {
                font-size: 12px;
            }
        }
        
        th, td {
            padding: 6px 8px;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
            font-size: 13px;
        }

        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 11px;
            letter-spacing: 0.5px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        tr:hover {
            background-color: #f8fafc;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }

        @media (max-width: 768px) {
            th, td {
                padding: 4px 6px;
                font-size: 12px;
            }
        }
        
        th {
            background: #f8fafc;
            font-weight: bold;
            color: #374151;
        }
        
        .status-pending {
            background: #fef2f2;
            color: #ef4444;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-closed {
            background: #f0fdf4;
            color: #22c55e;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }
        
        .error {
            background: #fef2f2;
            color: #ef4444;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        /* Smooth transition for table content */
        .table-content {
            opacity: 1;
            transition: opacity 0.5s ease-in-out;
        }

        .table-content.fade-out {
            opacity: 0;
        }

        /* Responsive header */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div>
                <h1>🏭 RC Dashboard</h1>
                <p>Route Card Management System</p>
            </div>
            <div class="controls">
                <select id="locationFilter">
                    <option value="">All Locations</option>
                </select>
                <button onclick="loadData()">🔄 Refresh</button>
                <button onclick="testAPI()">🔗 Test API</button>
                <button onclick="toggleDebug()">🐛 Debug</button>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3 style="color: #ef4444;">Pending RCs</h3>
                <div class="value" id="pendingRCs">0</div>
            </div>
            <div class="stat-card">
                <h3 style="color: #22c55e;">≤ 15 Days</h3>
                <div class="value" id="within15Days">0</div>
            </div>
            <div class="stat-card">
                <h3 style="color: #f59e0b;">16-45 Days</h3>
                <div class="value" id="between16And45Days">0</div>
            </div>
            <div class="stat-card">
                <h3 style="color: #dc2626;">46-90 Days</h3>
                <div class="value" id="between46And90Days">0</div>
            </div>
            <div class="stat-card">
                <h3 style="color: #7f1d1d;">> 90 Days</h3>
                <div class="value" id="over90Days">0</div>
            </div>
        </div>
        
        <!-- Debug Panel -->
        <div id="debugPanel" style="display: none; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
            <h3 style="margin-top: 0; color: #495057;">🐛 Debug Information</h3>
            <div id="debugContent">
                <p><strong>API Base:</strong> <span id="debugApiBase"></span></p>
                <p><strong>Last Error:</strong> <span id="debugLastError">None</span></p>
                <p><strong>Console:</strong> <span style="font-size: 12px; color: #6c757d;">Check browser console (F12) for detailed logs</span></p>
                <button onclick="testAllAPIs()" style="background: #28a745; color: white; border: none; padding: 6px 12px; border-radius: 4px; margin-top: 10px;">
                    Test All APIs
                </button>
            </div>
        </div>

        <div class="data-table">
            <h2>Recent Route Cards</h2>
            <div id="tableContent" class="table-content">
                <div class="loading">Loading data...</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        let currentPage = 0;
        let totalPages = 1;
        let autoSlideInterval;

        // Location name mapping
        function getLocationName(locationId) {
            const locationMap = {
                '2': 'PRK Sunkudkatte',
                '3': 'PRK Machohalli'
            };
            return locationMap[locationId] || `Location ${locationId}`;
        }

        async function testAPI() {
            try {
                console.log('Testing API connection...');
                const response = await fetch(`${API_BASE}/health`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                alert('API Connection: SUCCESS\n' + JSON.stringify(data, null, 2));
                console.log('API test successful:', data);
            } catch (error) {
                console.error('API test failed:', error);
                alert('API Connection: FAILED\n' + error.message + '\n\nCheck console for details.');
            }
        }
        
        async function loadLocations() {
            try {
                console.log('Loading locations...');
                const url = `${API_BASE}/locations`;
                console.log('Locations URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Locations data received:', data);

                if (data.success) {
                    const select = document.getElementById('locationFilter');
                    select.innerHTML = '<option value="">All Locations</option>';

                    data.data.forEach(location => {
                        const option = document.createElement('option');
                        option.value = location.LocationID;
                        option.textContent = location.LocationName || location.LocationID;
                        select.appendChild(option);
                    });
                    console.log('Locations loaded successfully');
                } else {
                    throw new Error(data.error || 'Failed to load locations');
                }
            } catch (error) {
                console.error('Error loading locations:', error);
                // Add error option to dropdown
                const select = document.getElementById('locationFilter');
                select.innerHTML = '<option value="">Error loading locations</option>';
            }
        }
        
        async function loadStats() {
            try {
                console.log('Loading stats...');
                const locationId = document.getElementById('locationFilter').value;
                const url = `${API_BASE}/dashboard-stats?locationId=${locationId || '%'}`;
                console.log('Stats URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Stats data received:', data);

                if (data.success) {
                    document.getElementById('pendingRCs').textContent = data.data.PendingRCs || 0;
                    document.getElementById('within15Days').textContent = data.data.RCsWithin15Days || 0;
                    document.getElementById('between16And45Days').textContent = data.data.RCsBetween16And45Days || 0;
                    document.getElementById('between46And90Days').textContent = data.data.RCsBetween46And90Days || 0;
                    document.getElementById('over90Days').textContent = data.data.RCsOver90Days || 0;
                    console.log('Stats updated successfully');
                } else {
                    throw new Error(data.error || 'Failed to load stats');
                }
            } catch (error) {
                console.error('Error loading stats:', error);
                updateDebugError(error);
                // Show error in the UI
                document.getElementById('pendingRCs').textContent = 'Error';
                document.getElementById('within15Days').textContent = 'Error';
                document.getElementById('between16And45Days').textContent = 'Error';
                document.getElementById('between46And90Days').textContent = 'Error';
                document.getElementById('over90Days').textContent = 'Error';
            }
        }
        
        async function loadTableData() {
            try {
                console.log('Loading table data...');
                const locationId = document.getElementById('locationFilter').value;
                const url = `${API_BASE}/rc-pending?locationId=${locationId || '%'}`;
                console.log('Table data URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Table data received:', data);

                if (data.success) {
                    const tableContent = document.getElementById('tableContent');

                    if (!data.data || data.data.length === 0) {
                        console.log('No data found');
                        tableContent.innerHTML = '<div class="loading">No data found</div>';
                        return;
                    }

                    console.log(`Found ${data.data.length} records`);

                    // Add fade out effect
                    tableContent.classList.add('fade-out');

                    // Wait for fade out, then update content
                    setTimeout(() => {
                        // Calculate pagination - fit more items to avoid scrolling
                        const screenHeight = window.innerHeight;
                        const itemsPerPage = screenHeight > 900 ? 30 : screenHeight > 700 ? 25 : 20;

                        totalPages = Math.ceil(data.data.length / itemsPerPage);
                        const startIndex = currentPage * itemsPerPage;
                        const endIndex = startIndex + itemsPerPage;
                        const currentData = data.data.slice(startIndex, endIndex);

                        const table = `
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; flex-wrap: wrap; gap: 10px;">
                                <h2 style="margin: 0; font-size: 1.2rem;">Recent Route Cards</h2>
                                <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
                                    <span style="font-size: 12px; color: #6b7280;">
                                        Page ${currentPage + 1} of ${totalPages} (${data.data.length} total)
                                    </span>
                                    <button onclick="toggleAutoSlide()" id="autoSlideBtn" style="
                                        background: #22c55e; color: white; border: none; padding: 6px 12px;
                                        border-radius: 4px; font-size: 12px; cursor: pointer; white-space: nowrap;">
                                        ▶️ Auto Slide
                                    </button>
                                </div>
                            </div>
                        <table>
                            <thead>
                                <tr>
                                    <th>Work Order</th>
                                    <th>Part & Product</th>
                                    <th>WO Date</th>
                                    <th>Status</th>
                                    <th>WO Qty</th>
                                    <th>Rej Qty</th>
                                    <th>Desp Qty</th>
                                    <th>Close Date</th>
                                    <th>Aging</th>
                                    <th>Location</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${currentData.map(item => `
                                    <tr>
                                        <td><strong style="color: #3b82f6;">${item.WoNo}</strong></td>
                                        <td>
                                            <div style="font-weight: bold; font-size: 14px;">
                                                ${item.PartNoAndProductName?.split(' | ')[1] || 'Unknown'}
                                            </div>
                                            <div style="font-size: 12px; color: #6b7280;">
                                                ${item.PartNoAndProductName?.split(' | ')[0] || 'Unknown'}
                                            </div>
                                        </td>
                                        <td>${item.WODate}</td>
                                        <td>
                                            <span class="status-pending">
                                                ${item.Status || 'RC Pending'}
                                            </span>
                                        </td>
                                        <td style="font-weight: bold;">${item.WO_Qty?.toLocaleString() || 0}</td>
                                        <td style="color: #ef4444; font-weight: bold;">
                                            ${item.TotalRejQty?.toLocaleString() || 0}
                                        </td>
                                        <td style="color: #f59e0b; font-weight: bold;">
                                            ${item.DespQty?.toLocaleString() || 0}
                                        </td>
                                        <td style="font-size: 12px;">
                                            ${item.CloseDate || 'In Progress'}
                                        </td>
                                        <td style="color: ${getAgingColor(item.AgingInDays)}; font-weight: bold;">
                                            ${item.AgingInDays || 0}d
                                        </td>
                                        <td>
                                            <div>
                                                <div style="font-weight: bold; font-size: 12px;">
                                                    ${item.Location || getLocationName(item.LocationID)}
                                                </div>
                                                <div style="font-size: 10px; color: #6b7280;">
                                                    ID: ${item.LocationID}
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;

                        tableContent.innerHTML = table;
                        console.log('Table HTML updated successfully');

                        // Add fade in effect
                        tableContent.classList.remove('fade-out');
                    }, 250); // Wait for fade out to complete
                } else {
                    throw new Error(data.error || 'Failed to load data');
                }
            } catch (error) {
                console.error('Error loading table data:', error);
                updateDebugError(error);
                const tableContent = document.getElementById('tableContent');
                tableContent.innerHTML = `<div class="error">Error loading data: ${error.message}<br><small>Check console (F12) for details</small></div>`;
                tableContent.classList.remove('fade-out');
            }
        }
        
        function getAgingColor(days) {
            if (days <= 15) return '#22c55e';
            if (days <= 45) return '#f59e0b';
            if (days <= 90) return '#dc2626';
            return '#7f1d1d';
        }
        
        // Auto-slide functionality
        function toggleAutoSlide() {
            const btn = document.getElementById('autoSlideBtn');
            if (autoSlideInterval) {
                clearInterval(autoSlideInterval);
                autoSlideInterval = null;
                btn.textContent = '▶️ Auto Slide';
                btn.style.background = '#22c55e';
            } else {
                autoSlideInterval = setInterval(() => {
                    currentPage = (currentPage + 1) % totalPages;
                    loadTableData();
                }, 8000); // Change page every 8 seconds for better readability
                btn.textContent = '⏸️ Stop Auto';
                btn.style.background = '#ef4444';
            }
        }

        async function loadData() {
            document.getElementById('tableContent').innerHTML = '<div class="loading">Loading data...</div>';
            currentPage = 0; // Reset to first page when filtering
            await Promise.all([loadStats(), loadTableData()]);
        }

        // Event listeners
        document.getElementById('locationFilter').addEventListener('change', () => {
            // Stop auto-slide when changing location
            if (autoSlideInterval) {
                clearInterval(autoSlideInterval);
                autoSlideInterval = null;
                const btn = document.getElementById('autoSlideBtn');
                if (btn) {
                    btn.textContent = '▶️ Auto Slide';
                    btn.style.background = '#22c55e';
                }
            }
            loadData();
        });

        // Debug functions
        function toggleDebug() {
            const panel = document.getElementById('debugPanel');
            if (panel.style.display === 'none') {
                panel.style.display = 'block';
                document.getElementById('debugApiBase').textContent = API_BASE;
            } else {
                panel.style.display = 'none';
            }
        }

        function updateDebugError(error) {
            document.getElementById('debugLastError').textContent = error.message || error;
        }

        async function testAllAPIs() {
            const debugContent = document.getElementById('debugContent');
            debugContent.innerHTML = '<p>Testing APIs...</p>';

            const tests = [
                { name: 'Health Check', url: `${API_BASE}/health` },
                { name: 'Dashboard Stats', url: `${API_BASE}/dashboard-stats` },
                { name: 'Locations', url: `${API_BASE}/locations` },
                { name: 'RC Pending', url: `${API_BASE}/rc-pending` }
            ];

            let results = '<h4>API Test Results:</h4>';

            for (const test of tests) {
                try {
                    const response = await fetch(test.url);
                    const data = await response.json();
                    results += `<p>✅ <strong>${test.name}:</strong> OK (${response.status})</p>`;
                } catch (error) {
                    results += `<p>❌ <strong>${test.name}:</strong> ${error.message}</p>`;
                }
            }

            debugContent.innerHTML = results;
        }

        // Initial load
        window.addEventListener('load', async () => {
            console.log('Dashboard loading...');
            console.log('API Base URL:', API_BASE);

            try {
                await loadLocations();
                await loadData();
                console.log('Dashboard loaded successfully');
            } catch (error) {
                console.error('Dashboard loading failed:', error);
                updateDebugError(error);
            }
        });
    </script>
</body>
</html>

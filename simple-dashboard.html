<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RC Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .header h1 {
            color: #1f2937;
            font-size: 2rem;
            margin-bottom: 5px;
        }
        
        .header p {
            color: #6b7280;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        select, button {
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
            font-size: 14px;
        }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            cursor: pointer;
        }
        
        button:hover {
            background: #2563eb;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            margin-bottom: 10px;
            font-size: 1rem;
        }
        
        .stat-card .value {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
        }
        
        .data-table {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
        }
        
        th {
            background: #f8fafc;
            font-weight: bold;
            color: #374151;
        }
        
        .status-pending {
            background: #fef2f2;
            color: #ef4444;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-closed {
            background: #f0fdf4;
            color: #22c55e;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }
        
        .error {
            background: #fef2f2;
            color: #ef4444;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div>
                <h1>🏭 RC Dashboard</h1>
                <p>Route Card Management System</p>
            </div>
            <div class="controls">
                <select id="locationFilter">
                    <option value="">All Locations</option>
                </select>
                <button onclick="loadData()">🔄 Refresh</button>
                <button onclick="testAPI()">🔗 Test API</button>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3 style="color: #ef4444;">Pending RCs</h3>
                <div class="value" id="pendingRCs">0</div>
            </div>
            <div class="stat-card">
                <h3 style="color: #22c55e;">≤ 7 Days</h3>
                <div class="value" id="within7Days">0</div>
            </div>
            <div class="stat-card">
                <h3 style="color: #f59e0b;">8-15 Days</h3>
                <div class="value" id="between8And15Days">0</div>
            </div>
            <div class="stat-card">
                <h3 style="color: #dc2626;">> 15 Days</h3>
                <div class="value" id="over15Days">0</div>
            </div>
        </div>
        
        <div class="data-table">
            <h2>Recent Route Cards</h2>
            <div id="tableContent">
                <div class="loading">Loading data...</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        let currentPage = 0;
        let totalPages = 1;
        let autoSlideInterval;

        // Location name mapping
        function getLocationName(locationId) {
            const locationMap = {
                '2': 'PRK Sunkudkatte',
                '3': 'PRK Machohalli'
            };
            return locationMap[locationId] || `Location ${locationId}`;
        }

        async function testAPI() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                alert('API Connection: SUCCESS\n' + JSON.stringify(data, null, 2));
            } catch (error) {
                alert('API Connection: FAILED\n' + error.message);
            }
        }
        
        async function loadLocations() {
            try {
                const response = await fetch(`${API_BASE}/locations`);
                const data = await response.json();
                
                if (data.success) {
                    const select = document.getElementById('locationFilter');
                    select.innerHTML = '<option value="">All Locations</option>';
                    
                    data.data.forEach(location => {
                        const option = document.createElement('option');
                        option.value = location.LocationID;
                        option.textContent = location.LocationName || location.LocationID;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading locations:', error);
            }
        }
        
        async function loadStats() {
            try {
                const locationId = document.getElementById('locationFilter').value;
                const response = await fetch(`${API_BASE}/dashboard-stats?locationId=${locationId || '%'}`);
                const data = await response.json();

                if (data.success) {
                    document.getElementById('pendingRCs').textContent = data.data.PendingRCs || 0;
                    document.getElementById('within7Days').textContent = data.data.RCsWithin7Days || 0;
                    document.getElementById('between8And15Days').textContent = data.data.RCsBetween8And15Days || 0;
                    document.getElementById('over15Days').textContent = data.data.RCsOver15Days || 0;
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }
        
        async function loadTableData() {
            try {
                const locationId = document.getElementById('locationFilter').value;
                const response = await fetch(`${API_BASE}/rc-pending?locationId=${locationId || '%'}`);
                const data = await response.json();

                if (data.success) {
                    const tableContent = document.getElementById('tableContent');

                    if (data.data.length === 0) {
                        tableContent.innerHTML = '<div class="loading">No data found</div>';
                        return;
                    }

                    // Calculate pagination
                    const itemsPerPage = 15;
                    totalPages = Math.ceil(data.data.length / itemsPerPage);
                    const startIndex = currentPage * itemsPerPage;
                    const endIndex = startIndex + itemsPerPage;
                    const currentData = data.data.slice(startIndex, endIndex);

                    const table = `
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                            <h2 style="margin: 0;">Recent Route Cards</h2>
                            <div style="display: flex; align-items: center; gap: 1rem;">
                                <span style="font-size: 14px; color: #6b7280;">
                                    Page ${currentPage + 1} of ${totalPages} (${data.data.length} total records)
                                </span>
                                <button onclick="toggleAutoSlide()" id="autoSlideBtn" style="
                                    background: #22c55e; color: white; border: none; padding: 6px 12px;
                                    border-radius: 4px; font-size: 12px; cursor: pointer;">
                                    ▶️ Auto Slide
                                </button>
                            </div>
                        </div>
                        <table>
                            <thead>
                                <tr>
                                    <th>Work Order</th>
                                    <th>Part & Product</th>
                                    <th>WO Date</th>
                                    <th>Status</th>
                                    <th>WO Qty</th>
                                    <th>OK Qty</th>
                                    <th>Aging</th>
                                    <th>Location</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${currentData.map(item => `
                                    <tr>
                                        <td><strong style="color: #3b82f6;">${item.WoNo}</strong></td>
                                        <td>
                                            <div style="font-weight: bold; font-size: 14px;">
                                                ${item.PartNoAndProductName?.split(' | ')[1] || 'Unknown'}
                                            </div>
                                            <div style="font-size: 12px; color: #6b7280;">
                                                ${item.PartNoAndProductName?.split(' | ')[0] || 'Unknown'}
                                            </div>
                                        </td>
                                        <td>${item.WODate}</td>
                                        <td>
                                            <span class="status-pending">
                                                RC Pending
                                            </span>
                                        </td>
                                        <td>${item.WO_Qty?.toLocaleString()}</td>
                                        <td style="color: #22c55e; font-weight: bold;">
                                            ${item.TotalOkQty?.toLocaleString()}
                                        </td>
                                        <td style="color: ${getAgingColor(item.AgingInDays)}; font-weight: bold;">
                                            ${item.AgingInDays}d
                                        </td>
                                        <td>
                                            <div>
                                                <div style="font-weight: bold; font-size: 12px;">
                                                    ${item.LocationName || getLocationName(item.LocationID)}
                                                </div>
                                                <div style="font-size: 10px; color: #6b7280;">
                                                    ID: ${item.LocationID}
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;

                    tableContent.innerHTML = table;
                } else {
                    throw new Error(data.error || 'Failed to load data');
                }
            } catch (error) {
                console.error('Error loading table data:', error);
                document.getElementById('tableContent').innerHTML =
                    `<div class="error">Error loading data: ${error.message}</div>`;
            }
        }
        
        function getAgingColor(days) {
            if (days <= 7) return '#22c55e';
            if (days <= 15) return '#f59e0b';
            if (days <= 30) return '#ef4444';
            return '#dc2626';
        }
        
        // Auto-slide functionality
        function toggleAutoSlide() {
            const btn = document.getElementById('autoSlideBtn');
            if (autoSlideInterval) {
                clearInterval(autoSlideInterval);
                autoSlideInterval = null;
                btn.textContent = '▶️ Auto Slide';
                btn.style.background = '#22c55e';
            } else {
                autoSlideInterval = setInterval(() => {
                    currentPage = (currentPage + 1) % totalPages;
                    loadTableData();
                }, 5000); // Change page every 5 seconds
                btn.textContent = '⏸️ Stop Auto';
                btn.style.background = '#ef4444';
            }
        }

        async function loadData() {
            document.getElementById('tableContent').innerHTML = '<div class="loading">Loading data...</div>';
            currentPage = 0; // Reset to first page when filtering
            await Promise.all([loadStats(), loadTableData()]);
        }

        // Event listeners
        document.getElementById('locationFilter').addEventListener('change', () => {
            // Stop auto-slide when changing location
            if (autoSlideInterval) {
                clearInterval(autoSlideInterval);
                autoSlideInterval = null;
                const btn = document.getElementById('autoSlideBtn');
                if (btn) {
                    btn.textContent = '▶️ Auto Slide';
                    btn.style.background = '#22c55e';
                }
            }
            loadData();
        });

        // Initial load
        window.addEventListener('load', async () => {
            await loadLocations();
            await loadData();
        });
    </script>
</body>
</html>

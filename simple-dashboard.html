<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RC Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            padding: 10px;
            margin: 0;
            overflow-x: hidden;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            margin: 0;
            padding: 1vh 1vw;
            display: flex;
            flex-direction: column;
            gap: 1vh;
            overflow: hidden;
            box-sizing: border-box;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .header {
            background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
            padding: 3vh 2vw;
            border-radius: 2vh;
            box-shadow: 0 1vh 3vh rgba(0,0,0,0.3);
            margin-bottom: 2vh;
            text-align: center;
            position: relative;
            overflow: hidden;
            flex-shrink: 0;
            border: 1px solid #34495e;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        select, button {
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
            font-size: 14px;
        }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            cursor: pointer;
        }
        
        button:hover {
            background: #2563eb;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(15vw, 1fr));
            gap: 1vw;
            margin-bottom: 2vh;
            flex-shrink: 0;
        }

        @media (max-width: 768px) {
            .stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }
        }

        @media (max-width: 480px) {
            .stats {
                grid-template-columns: 1fr;
                gap: 8px;
            }
        }
        
        .stat-card {
            background: linear-gradient(145deg, #ecf0f1 0%, #bdc3c7 100%);
            padding: 2rem 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid #95a5a6;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.25);
        }
        
        .stat-card h3 {
            margin-bottom: 10px;
            font-size: 1rem;
        }
        
        .stat-card .value {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
        }
        
        .data-table {
            background: linear-gradient(145deg, #ecf0f1 0%, #bdc3c7 100%);
            border-radius: 2vh;
            padding: 2vh 2vw;
            box-shadow: 0 1vh 3vh rgba(0,0,0,0.2);
            flex: 1;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid #95a5a6;
            min-height: 0;
        }

        .table-content {
            height: 100%;
            overflow-x: auto;
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        @media (max-width: 768px) {
            .data-table {
                padding: 10px;
                height: calc(100vh - 260px);
            }
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            table {
                font-size: 12px;
            }
        }
        
        th, td {
            padding: 0.6vh 0.8vw;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
            font-size: clamp(0.7rem, 1.2vw, 0.9rem);
            white-space: nowrap;
        }

        th {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 11px;
            letter-spacing: 0.5px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        tr:hover {
            background: rgba(52, 73, 94, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            transition: all 0.2s ease;
        }

        tbody tr {
            transition: all 0.2s ease;
        }

        tbody tr:nth-child(even) {
            background-color: rgba(236, 240, 241, 0.5);
        }

        @media (max-width: 768px) {
            th, td {
                padding: 4px 6px;
                font-size: 12px;
            }
        }
        
        th {
            background: #f8fafc;
            font-weight: bold;
            color: #374151;
        }
        
        .status-pending {
            background: #fef2f2;
            color: #ef4444;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-closed {
            background: #f0fdf4;
            color: #22c55e;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }
        
        .error {
            background: #fef2f2;
            color: #ef4444;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        /* Smooth transition for table content */
        .table-content {
            opacity: 1;
            transition: opacity 0.5s ease-in-out;
        }

        .table-content.fade-out {
            opacity: 0;
        }

        /* Responsive header */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1 style="font-size: clamp(2rem, 6vw, 4rem); color: white; margin-bottom: 1vh; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); font-weight: 800;">
                    🏭 RC DASHBOARD
                </h1>
                <p style="font-size: clamp(0.9rem, 2vw, 1.4rem); color: rgba(255,255,255,0.9); margin-bottom: 2vh; text-shadow: 1px 1px 2px rgba(0,0,0,0.2);">
                    Route Card Management System
                </p>
                <div class="controls" style="justify-content: center; gap: 2vw; flex-wrap: wrap;">
                    <select id="locationFilter" style="padding: 1.2vh 1.6vw; font-size: clamp(0.8rem, 1.5vw, 1.1rem); border-radius: 1vh; border: none; box-shadow: 0 0.4vh 0.8vh rgba(0,0,0,0.1);">
                        <option value="">All Locations</option>
                    </select>
                    <div style="font-size: clamp(0.7rem, 1.2vw, 1rem); color: rgba(255,255,255,0.8); padding: 1.2vh 1.6vw; background: rgba(255,255,255,0.1); border-radius: 1vh; backdrop-filter: blur(10px); white-space: nowrap;">
                        🔄 Auto-refresh: 5 min | ⏱️ Auto-slide: 5 sec | 📊 Dynamic pages
                    </div>
                </div>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3 style="color: #ef4444;">Pending RCs</h3>
                <div class="value" id="pendingRCs">0</div>
            </div>
            <div class="stat-card">
                <h3 style="color: #22c55e;">≤ 15 Days</h3>
                <div class="value" id="within15Days">0</div>
            </div>
            <div class="stat-card">
                <h3 style="color: #f59e0b;">16-45 Days</h3>
                <div class="value" id="between16And45Days">0</div>
            </div>
            <div class="stat-card">
                <h3 style="color: #dc2626;">46-90 Days</h3>
                <div class="value" id="between46And90Days">0</div>
            </div>
            <div class="stat-card">
                <h3 style="color: #7f1d1d;">> 90 Days</h3>
                <div class="value" id="over90Days">0</div>
            </div>
        </div>
        


        <div class="data-table">
            <h2>Recent Route Cards</h2>
            <div id="tableContent" class="table-content">
                <div class="loading">Loading data...</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        let currentPage = 0;
        let totalPages = 1;
        let autoSlideInterval;
        let autoRefreshInterval;
        let scrollPosition = 0;
        let isScrolling = false;

        // Location name mapping
        function getLocationName(locationId) {
            const locationMap = {
                '2': 'PRK Sunkudkatte',
                '3': 'PRK Machohalli'
            };
            return locationMap[locationId] || `Location ${locationId}`;
        }


        
        async function loadLocations() {
            try {
                console.log('Loading locations...');
                const url = `${API_BASE}/locations`;
                console.log('Locations URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Locations data received:', data);

                if (data.success) {
                    const select = document.getElementById('locationFilter');
                    select.innerHTML = '<option value="">All Locations</option>';

                    data.data.forEach(location => {
                        const option = document.createElement('option');
                        option.value = location.LocationID;
                        option.textContent = location.LocationName || location.LocationID;
                        select.appendChild(option);
                    });
                    console.log('Locations loaded successfully');
                } else {
                    throw new Error(data.error || 'Failed to load locations');
                }
            } catch (error) {
                console.error('Error loading locations:', error);
                // Add error option to dropdown
                const select = document.getElementById('locationFilter');
                select.innerHTML = '<option value="">Error loading locations</option>';
            }
        }
        
        async function loadStats() {
            try {
                console.log('Loading stats...');
                const locationId = document.getElementById('locationFilter').value;
                const url = `${API_BASE}/dashboard-stats?locationId=${locationId || '%'}`;
                console.log('Stats URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Stats data received:', data);

                if (data.success) {
                    document.getElementById('pendingRCs').textContent = data.data.PendingRCs || 0;
                    document.getElementById('within15Days').textContent = data.data.RCsWithin15Days || 0;
                    document.getElementById('between16And45Days').textContent = data.data.RCsBetween16And45Days || 0;
                    document.getElementById('between46And90Days').textContent = data.data.RCsBetween46And90Days || 0;
                    document.getElementById('over90Days').textContent = data.data.RCsOver90Days || 0;
                    console.log('Stats updated successfully');
                } else {
                    throw new Error(data.error || 'Failed to load stats');
                }
            } catch (error) {
                console.error('Error loading stats:', error);
                updateDebugError(error);
                // Show error in the UI
                document.getElementById('pendingRCs').textContent = 'Error';
                document.getElementById('within15Days').textContent = 'Error';
                document.getElementById('between16And45Days').textContent = 'Error';
                document.getElementById('between46And90Days').textContent = 'Error';
                document.getElementById('over90Days').textContent = 'Error';
            }
        }
        
        async function loadTableData() {
            try {
                console.log('Loading table data...');
                const locationId = document.getElementById('locationFilter').value;
                const url = `${API_BASE}/rc-pending?locationId=${locationId || '%'}`;
                console.log('Table data URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Table data received:', data);

                if (data.success) {
                    const tableContent = document.getElementById('tableContent');

                    if (!data.data || data.data.length === 0) {
                        console.log('No data found');
                        tableContent.innerHTML = '<div class="loading">No data found</div>';
                        return;
                    }

                    console.log(`Found ${data.data.length} records`);

                    // Add fade out effect
                    tableContent.classList.add('fade-out');

                    // Wait for fade out, then update content
                    setTimeout(() => {
                        // Simple pagination - 100 rows per page
                        const itemsPerPage = 100;

                        totalPages = Math.ceil(data.data.length / itemsPerPage);
                        const startIndex = currentPage * itemsPerPage;
                        const endIndex = startIndex + itemsPerPage;
                        const currentData = data.data.slice(startIndex, endIndex);

                        console.log(`Screen height: ${containerHeight}, Items per page: ${itemsPerPage}, Total pages: ${totalPages}`);

                        const table = `
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; flex-wrap: wrap; gap: 10px;">
                                <h2 style="margin: 0; font-size: 1.2rem;">Recent Route Cards</h2>
                                <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
                                    <span style="font-size: 12px; color: #6b7280;">
                                        Page ${currentPage + 1} of ${totalPages} | Showing ${currentData.length} of ${data.data.length} total pending RCs
                                    </span>
                                    <span style="font-size: 11px; color: #10b981; font-weight: bold;">
                                        🔄 Auto-sliding active
                                    </span>
                                </div>
                            </div>
                        <table>
                            <thead>
                                <tr>
                                    <th>Work Order</th>
                                    <th>Part & Product</th>
                                    <th>WO Date</th>
                                    <th>Status</th>
                                    <th>WO Qty</th>
                                    <th>Rej Qty</th>
                                    <th>Desp Qty</th>
                                    <th>Close Date</th>
                                    <th>Aging</th>
                                    <th>Location</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${currentData.map(item => `
                                    <tr>
                                        <td><strong style="color: #3b82f6;">${item.WoNo}</strong></td>
                                        <td>
                                            <div style="font-weight: bold; font-size: 14px;">
                                                ${item.PartNoAndProductName?.split(' | ')[1] || 'Unknown'}
                                            </div>
                                            <div style="font-size: 12px; color: #6b7280;">
                                                ${item.PartNoAndProductName?.split(' | ')[0] || 'Unknown'}
                                            </div>
                                        </td>
                                        <td>${item.WODate}</td>
                                        <td>
                                            <span class="status-pending">
                                                ${item.Status || 'RC Pending'}
                                            </span>
                                        </td>
                                        <td style="font-weight: bold;">${item.WO_Qty?.toLocaleString() || 0}</td>
                                        <td style="color: #ef4444; font-weight: bold;">
                                            ${item.TotalRejQty?.toLocaleString() || 0}
                                        </td>
                                        <td style="color: #f59e0b; font-weight: bold;">
                                            ${item.DespQty?.toLocaleString() || 0}
                                        </td>
                                        <td style="font-size: 12px;">
                                            ${item.CloseDate || 'In Progress'}
                                        </td>
                                        <td style="color: ${getAgingColor(item.AgingInDays)}; font-weight: bold;">
                                            ${item.AgingInDays || 0}d
                                        </td>
                                        <td>
                                            <div>
                                                <div style="font-weight: bold; font-size: 12px;">
                                                    ${item.Location || getLocationName(item.LocationID)}
                                                </div>
                                                <div style="font-size: 10px; color: #6b7280;">
                                                    ID: ${item.LocationID}
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;

                        tableContent.innerHTML = table;
                        console.log('Table HTML updated successfully');

                        // Add fade in effect and start auto-slide
                        tableContent.classList.remove('fade-out');
                        scrollPosition = 0; // Reset scroll position
                        startAutoSlide(); // Start auto-slide automatically
                    }, 250); // Wait for fade out to complete
                } else {
                    throw new Error(data.error || 'Failed to load data');
                }
            } catch (error) {
                console.error('Error loading table data:', error);
                const tableContent = document.getElementById('tableContent');
                tableContent.innerHTML = `<div class="error">Error loading data: ${error.message}<br><small>Check console (F12) for details</small></div>`;
                tableContent.classList.remove('fade-out');
            }
        }
        
        function getAgingColor(days) {
            if (days <= 15) return '#22c55e';
            if (days <= 45) return '#f59e0b';
            if (days <= 90) return '#dc2626';
            return '#7f1d1d';
        }
        
        // Auto-slide functionality with smooth vertical scrolling
        function startAutoSlide() {
            if (autoSlideInterval) {
                clearInterval(autoSlideInterval);
            }

            autoSlideInterval = setInterval(() => {
                const tableContent = document.getElementById('tableContent');
                const table = tableContent.querySelector('table');

                if (!table) return;

                const containerHeight = tableContent.clientHeight;
                const tableHeight = table.scrollHeight;
                const maxScroll = tableHeight - containerHeight;

                if (maxScroll > 0 && scrollPosition < maxScroll) {
                    // Scroll down in smaller increments to ensure we reach the bottom
                    const scrollIncrement = Math.min(100, maxScroll - scrollPosition); // Small 100px increments
                    scrollPosition += scrollIncrement;

                    // Force to exact bottom if close
                    if (maxScroll - scrollPosition < 50) {
                        scrollPosition = maxScroll;
                    }

                    tableContent.scrollTo({
                        top: scrollPosition,
                        behavior: 'smooth'
                    });

                    console.log(`Scrolling: ${scrollPosition}/${maxScroll} (${Math.round((scrollPosition/maxScroll)*100)}%)`);
                } else {
                    // We've reached the bottom, move to next page
                    console.log('Reached bottom, moving to next page');
                    scrollPosition = 0;
                    tableContent.scrollTo({ top: 0, behavior: 'smooth' });

                    setTimeout(() => {
                        currentPage = (currentPage + 1) % totalPages;
                        loadTableData();
                    }, 1500); // Wait for scroll animation
                }
            }, 5000); // Slower interval - 5 seconds for better readability
        }

        async function loadData() {
            // Stop current auto-slide
            if (autoSlideInterval) {
                clearInterval(autoSlideInterval);
                autoSlideInterval = null;
            }

            document.getElementById('tableContent').innerHTML = '<div class="loading">Loading data...</div>';
            currentPage = 0; // Reset to first page when filtering
            scrollPosition = 0; // Reset scroll position
            await Promise.all([loadStats(), loadTableData()]);
        }

        // Event listeners
        document.getElementById('locationFilter').addEventListener('change', () => {
            loadData();
        });

        // Handle window resize and zoom changes
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                console.log('Window resized, recalculating pagination...');
                currentPage = 0; // Reset to first page
                loadTableData(); // Recalculate pagination
            }, 250); // Debounce resize events
        });



        // Auto-refresh every 5 minutes
        function startAutoRefresh() {
            autoRefreshInterval = setInterval(() => {
                console.log('Auto-refreshing data...');
                loadData();
            }, 5 * 60 * 1000); // 5 minutes
        }

        // Initial load
        window.addEventListener('load', async () => {
            console.log('Dashboard loading...');
            console.log('API Base URL:', API_BASE);

            try {
                await loadLocations();
                await loadData();
                startAutoRefresh();
                console.log('Dashboard loaded successfully with auto-refresh');
            } catch (error) {
                console.error('Dashboard loading failed:', error);
            }
        });
    </script>
</body>
</html>

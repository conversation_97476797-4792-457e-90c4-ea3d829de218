import React, { useState } from 'react';
import SimpleDashboard from './SimpleDashboard';

function App() {
  const [showDashboard, setShowDashboard] = useState(false);

  if (showDashboard) {
    return <SimpleDashboard />;
  }
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        background: 'white',
        padding: '2rem',
        borderRadius: '12px',
        boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
        textAlign: 'center',
        maxWidth: '500px',
        width: '90%'
      }}>
        <h1 style={{ color: '#3b82f6', marginBottom: '1rem' }}>
          🏭 RC Dashboard
        </h1>
        <p style={{ color: '#64748b', marginBottom: '2rem' }}>
          Route Card Management System
        </p>

        <div style={{ marginBottom: '2rem' }}>
          <div style={{
            background: '#dcfce7',
            padding: '1rem',
            borderRadius: '8px',
            marginBottom: '1rem',
            color: '#166534'
          }}>
            ✅ Frontend: Running on port 5174
          </div>
          <div style={{
            background: '#dbeafe',
            padding: '1rem',
            borderRadius: '8px',
            marginBottom: '1rem',
            color: '#1e40af'
          }}>
            ✅ Backend: Connected to port 5001
          </div>
          <div style={{
            background: '#fef3c7',
            padding: '1rem',
            borderRadius: '8px',
            color: '#92400e'
          }}>
            ✅ Database: SQL Server (ICsoft)
          </div>
        </div>

        <button
          onClick={() => {
            // Test API connection
            fetch('http://localhost:5001/api/health')
              .then(res => res.json())
              .then(data => {
                alert('API Health Check: ' + JSON.stringify(data));
              })
              .catch(err => {
                alert('API Error: ' + err.message);
              });
          }}
          style={{
            background: 'linear-gradient(45deg, #3b82f6 30%, #8b5cf6 90%)',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '8px',
            fontSize: '16px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          Test API Connection
        </button>

        <button
          onClick={() => setShowDashboard(true)}
          style={{
            background: 'linear-gradient(45deg, #22c55e 30%, #16a34a 90%)',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '8px',
            fontSize: '16px',
            cursor: 'pointer'
          }}
        >
          Load Dashboard
        </button>
      </div>
    </div>
  );
}

export default App;

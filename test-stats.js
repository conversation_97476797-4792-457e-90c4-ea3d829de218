const sql = require('./backend/node_modules/mssql');

// Database configuration
const dbConfig = {
    server: 'WIN-PRK-SRV-01',
    database: 'ICsoft',
    user: 'sa',
    password: 'PRK@1234$',
    options: {
        trustServerCertificate: true,
        enableArithAbort: true,
        encrypt: false
    }
};

async function testStats() {
    try {
        console.log('🔄 Connecting to database...');
        const pool = await sql.connect(dbConfig);
        console.log('✅ Database connected');

        console.log('🔄 Testing stats query...');
        const request = pool.request();
        request.input('locationFilter', sql.VarChar, '%');

        const result = await request.query(`
            WITH ProcessDetails AS (
                SELECT pfs.SlNo, wo.WO_Qty, TRY_CAST(pfs.OkQty AS FLOAT) AS OkQty,
                       TRY_CAST(pfs.RejQty AS FLOAT) AS RejQty, TRY_CAST(pfs.StockQty AS FLOAT) AS StockQty,
                       pfs.PostProcessID, pfs.ProcessID, wo.WoNo, wo.WODate, wo.CloseDate,
                       p.InternalPart<PERSON>o, p.prodname, wo.Status, wo.LocationID, p.ProdId
                FROM ProdnForgingStages pfs
                INNER JOIN WorkOrder wo ON wo.WoNo = pfs.WoNo
                INNER JOIN product p ON wo.prodid = p.prodid
                WHERE wo.LocationID like @locationFilter
            ),
            StatusDetails AS (
                SELECT pd.WoNo, pd.WO_Qty, pd.PostProcessID, pd.ProcessID,
                       SUM(pd.OkQty) AS TotalOkQty, SUM(pd.RejQty) AS TotalRejQty,
                       SUM(pd.StockQty) AS TotalStockQty, pd.WODate, pd.CloseDate,
                       pd.InternalPartNo, pd.prodname, pd.Status AS WOSTATUS, pd.LocationID, pd.ProdId
                FROM ProcessDetails pd
                GROUP BY pd.WoNo, pd.WO_Qty, pd.PostProcessID, pd.ProcessID,
                         pd.WODate, pd.CloseDate, pd.InternalPartNo, pd.prodname,
                         pd.Status, pd.LocationID, pd.ProdId
            ),
            DespatchSummary AS (
                SELECT s.WoNo, SUM(s.despqty) AS DespQty
                FROM Sales_Despatch_Details s
                INNER JOIN invoice i ON i.InvoiceNo = s.InvoiceNo
                GROUP BY s.WoNo
            )
            SELECT
                COUNT(*) as PendingRCs,
                AVG(CAST(CASE WHEN sd.WOSTATUS = 'Pending' AND sd.WODate IS NOT NULL
                              THEN DATEDIFF(DAY, sd.WODate, GETDATE())
                              ELSE NULL END AS FLOAT)) as AvgAgingDays,
                SUM(CASE WHEN sd.WOSTATUS = 'Pending' AND DATEDIFF(DAY, sd.WODate, GETDATE()) <= 15 THEN 1 ELSE 0 END) as RCsWithin15Days,
                SUM(CASE WHEN sd.WOSTATUS = 'Pending' AND DATEDIFF(DAY, sd.WODate, GETDATE()) BETWEEN 16 AND 45 THEN 1 ELSE 0 END) as RCsBetween16And45Days,
                SUM(CASE WHEN sd.WOSTATUS = 'Pending' AND DATEDIFF(DAY, sd.WODate, GETDATE()) BETWEEN 46 AND 90 THEN 1 ELSE 0 END) as RCsBetween46And90Days,
                SUM(CASE WHEN sd.WOSTATUS = 'Pending' AND DATEDIFF(DAY, sd.WODate, GETDATE()) > 90 THEN 1 ELSE 0 END) as RCsOver90Days
            FROM StatusDetails sd
            LEFT JOIN DespatchSummary ds ON sd.WoNo = ds.WoNo
            WHERE sd.WOSTATUS = 'Pending'
        `);

        console.log('✅ Stats query result:');
        console.log(JSON.stringify(result.recordset[0], null, 2));

        await pool.close();
        console.log('✅ Database connection closed');

    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

testStats();

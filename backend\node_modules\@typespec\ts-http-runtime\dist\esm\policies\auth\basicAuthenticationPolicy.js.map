{"version": 3, "file": "basicAuthenticationPolicy.js", "sourceRoot": "", "sources": ["../../../../src/policies/auth/basicAuthenticationPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAMlC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AACrF,OAAO,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;AAEtE;;GAEG;AACH,MAAM,CAAC,MAAM,6BAA6B,GAAG,4BAA4B,CAAC;AAqB1E;;GAEG;AACH,MAAM,UAAU,yBAAyB,CACvC,OAAyC;IAEzC,OAAO;QACL,IAAI,EAAE,6BAA6B;QACnC,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;;YAC3D,0FAA0F;YAC1F,sBAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAEzC,MAAM,MAAM,GAAG,MAAA,CAAC,MAAA,OAAO,CAAC,WAAW,mCAAI,OAAO,CAAC,WAAW,CAAC,0CAAE,IAAI,CAC/D,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,KAAK,OAAO,CACjD,CAAC;YAEF,+EAA+E;YAC/E,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YAED,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC;YAClD,MAAM,WAAW,GAAG,kBAAkB,CACpC,kBAAkB,CAAC,GAAG,QAAQ,IAAI,QAAQ,EAAE,EAAE,OAAO,CAAC,EACtD,QAAQ,CACT,CAAC;YACF,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,SAAS,WAAW,EAAE,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { BasicCredential } from \"../../auth/credentials.js\";\nimport type { AuthScheme } from \"../../auth/schemes.js\";\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../../interfaces.js\";\nimport type { PipelinePolicy } from \"../../pipeline.js\";\nimport { stringToUint8Array, uint8ArrayToString } from \"../../util/bytesEncoding.js\";\nimport { ensureSecureConnection } from \"./checkInsecureConnection.js\";\n\n/**\n * Name of the Basic Authentication Policy\n */\nexport const basicAuthenticationPolicyName = \"bearerAuthenticationPolicy\";\n\n/**\n * Options for configuring the basic authentication policy\n */\nexport interface BasicAuthenticationPolicyOptions {\n  /**\n   * The credential used to authenticate requests\n   */\n  credential: BasicCredential;\n  /**\n   * Optional authentication schemes to use. If not provided, schemes from the request will be used.\n   */\n  authSchemes?: AuthScheme[];\n  /**\n   * Allows for connecting to HTTP endpoints instead of enforcing HTTPS.\n   * CAUTION: Never use this option in production.\n   */\n  allowInsecureConnection?: boolean;\n}\n\n/**\n * Gets a pipeline policy that adds basic authentication to requests\n */\nexport function basicAuthenticationPolicy(\n  options: BasicAuthenticationPolicyOptions,\n): PipelinePolicy {\n  return {\n    name: basicAuthenticationPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      // Ensure allowInsecureConnection is explicitly set when sending request to non-https URLs\n      ensureSecureConnection(request, options);\n\n      const scheme = (request.authSchemes ?? options.authSchemes)?.find(\n        (x) => x.kind === \"http\" && x.scheme === \"basic\",\n      );\n\n      // Skip adding authentication header if no basic authentication scheme is found\n      if (!scheme) {\n        return next(request);\n      }\n\n      const { username, password } = options.credential;\n      const headerValue = uint8ArrayToString(\n        stringToUint8Array(`${username}:${password}`, \"utf-8\"),\n        \"base64\",\n      );\n      request.headers.set(\"Authorization\", `Basic ${headerValue}`);\n      return next(request);\n    },\n  };\n}\n"]}
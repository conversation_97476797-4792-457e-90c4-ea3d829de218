import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Calendar,
  Clock,
  Package,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import {
  Card,
  CardContent,
  Typography,
  TextField,
  InputAdornment,
  Button,
  Chip,
  IconButton,
  Tooltip,
  Box,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';

const DataTable = ({ selectedLocation }) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRow, setSelectedRow] = useState(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [pageSize, setPageSize] = useState(25);

  useEffect(() => {
    fetchData();
    
    // Listen for refresh events
    const handleRefresh = () => fetchData();
    window.addEventListener('refreshData', handleRefresh);
    
    return () => window.removeEventListener('refreshData', handleRefresh);
  }, [selectedLocation]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `http://localhost:5000/api/rc-pending?locationId=${selectedLocation || '%'}`
      );
      const result = await response.json();
      
      if (result.success) {
        const processedData = result.data.map((item, index) => ({
          id: index + 1,
          ...item,
          AgingInDays: item.AgingInDays || 0,
          TotalOkQty: item.TotalOkQty || 0,
          TotalStockQty: item.TotalStockQty || 0,
          TotalRejQty: item.TotalRejQty || 0,
          DespQty: item.DespQty || 0
        }));
        setData(processedData);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      // Mock data for development
      generateMockData();
    } finally {
      setLoading(false);
    }
  };

  const generateMockData = () => {
    const mockData = Array.from({ length: 50 }, (_, index) => ({
      id: index + 1,
      PartNoAndProductName: `PART${String(index + 1).padStart(3, '0')} | Product ${index + 1}`,
      WoNo: `WO${String(index + 1).padStart(4, '0')}`,
      WODate: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toLocaleDateString('en-GB'),
      WO_Qty: Math.floor(Math.random() * 1000) + 100,
      Status: Math.random() > 0.6 ? 'RC Pending' : 'RC Closed',
      TotalOkQty: Math.floor(Math.random() * 800) + 50,
      TotalStockQty: Math.floor(Math.random() * 200) + 10,
      TotalRejQty: Math.floor(Math.random() * 50),
      DespQty: Math.floor(Math.random() * 500) + 20,
      DespatchQtyDetails: Math.random() > 0.5 ? 'Despatch Qty: 150 (INV001)' : 'Despatch Not Yet Done',
      CloseDate: Math.random() > 0.5 ? new Date().toLocaleDateString('en-GB') : 'In Progress',
      AgingInDays: Math.floor(Math.random() * 60) + 1,
      LocationID: ['LOC001', 'LOC002', 'MAIN', 'WAREHOUSE'][Math.floor(Math.random() * 4)]
    }));
    setData(mockData);
  };

  const getStatusChip = (status) => {
    const statusConfig = {
      'RC Pending': { color: 'warning', icon: Clock, bgColor: 'bg-orange-100', textColor: 'text-orange-800' },
      'RC Closed': { color: 'success', icon: CheckCircle, bgColor: 'bg-green-100', textColor: 'text-green-800' },
      'Unknown Status': { color: 'default', icon: XCircle, bgColor: 'bg-gray-100', textColor: 'text-gray-800' }
    };
    
    const config = statusConfig[status] || statusConfig['Unknown Status'];
    const Icon = config.icon;
    
    return (
      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor}`}>
        <Icon className="w-3 h-3 mr-1" />
        {status}
      </div>
    );
  };

  const getAgingColor = (days) => {
    if (days <= 7) return 'text-green-600';
    if (days <= 15) return 'text-yellow-600';
    if (days <= 30) return 'text-orange-600';
    return 'text-red-600';
  };

  const columns = [
    {
      field: 'WoNo',
      headerName: 'Work Order',
      width: 120,
      renderCell: (params) => (
        <div className="font-mono text-sm font-medium text-blue-600">
          {params.value}
        </div>
      )
    },
    {
      field: 'PartNoAndProductName',
      headerName: 'Part & Product',
      width: 250,
      renderCell: (params) => (
        <div className="py-1">
          <div className="text-sm font-medium text-gray-900 truncate">
            {params.value?.split(' | ')[1] || 'Unknown Product'}
          </div>
          <div className="text-xs text-gray-500 font-mono">
            {params.value?.split(' | ')[0] || 'Unknown Part'}
          </div>
        </div>
      )
    },
    {
      field: 'WODate',
      headerName: 'WO Date',
      width: 100,
      renderCell: (params) => (
        <div className="text-sm text-gray-600">
          {params.value}
        </div>
      )
    },
    {
      field: 'Status',
      headerName: 'Status',
      width: 130,
      renderCell: (params) => getStatusChip(params.value)
    },
    {
      field: 'WO_Qty',
      headerName: 'WO Qty',
      width: 90,
      type: 'number',
      renderCell: (params) => (
        <div className="text-sm font-medium text-gray-900">
          {params.value?.toLocaleString()}
        </div>
      )
    },
    {
      field: 'TotalOkQty',
      headerName: 'OK Qty',
      width: 90,
      type: 'number',
      renderCell: (params) => (
        <div className="text-sm font-medium text-green-600">
          {params.value?.toLocaleString()}
        </div>
      )
    },
    {
      field: 'TotalStockQty',
      headerName: 'Stock Qty',
      width: 100,
      type: 'number',
      renderCell: (params) => (
        <div className="text-sm font-medium text-blue-600">
          {params.value?.toLocaleString()}
        </div>
      )
    },
    {
      field: 'TotalRejQty',
      headerName: 'Reject Qty',
      width: 100,
      type: 'number',
      renderCell: (params) => (
        <div className="text-sm font-medium text-red-600">
          {params.value?.toLocaleString()}
        </div>
      )
    },
    {
      field: 'AgingInDays',
      headerName: 'Aging',
      width: 80,
      renderCell: (params) => (
        <div className={`text-sm font-medium ${getAgingColor(params.value)}`}>
          {params.value}d
        </div>
      )
    },
    {
      field: 'LocationID',
      headerName: 'Location',
      width: 100,
      renderCell: (params) => (
        <Chip 
          label={params.value} 
          size="small" 
          variant="outlined"
          className="text-xs"
        />
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      sortable: false,
      renderCell: (params) => (
        <Tooltip title="View Details">
          <IconButton
            size="small"
            onClick={() => {
              setSelectedRow(params.row);
              setDetailsOpen(true);
            }}
            className="text-blue-600 hover:text-blue-800"
          >
            <Eye className="w-4 h-4" />
          </IconButton>
        </Tooltip>
      )
    }
  ];

  const filteredData = data.filter(row =>
    Object.values(row).some(value =>
      value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const handleExport = () => {
    const csvContent = [
      columns.filter(col => col.field !== 'actions').map(col => col.headerName).join(','),
      ...filteredData.map(row =>
        columns.filter(col => col.field !== 'actions').map(col => row[col.field] || '').join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rc-data-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="p-6 h-full flex flex-col bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-6"
      >
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <Typography variant="h4" className="font-bold text-gray-900 mb-2">
              Route Card Data
            </Typography>
            <Typography variant="body1" color="textSecondary">
              Detailed view of all route cards with filtering and export options
            </Typography>
          </div>
          
          <div className="flex items-center space-x-3">
            <Button
              variant="outlined"
              startIcon={<Download className="w-4 h-4" />}
              onClick={handleExport}
              className="whitespace-nowrap"
            >
              Export CSV
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="mb-6"
      >
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <TextField
                placeholder="Search work orders, parts, locations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search className="w-4 h-4 text-gray-400" />
                    </InputAdornment>
                  ),
                }}
                className="flex-1"
                size="small"
              />
              
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Package className="w-4 h-4" />
                <span>Total: {filteredData.length} records</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Data Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="flex-1"
      >
        <Card className="h-full">
          <CardContent className="p-0 h-full">
            <div style={{ height: '100%', width: '100%' }}>
              <DataGrid
                rows={filteredData}
                columns={columns}
                pageSize={pageSize}
                onPageSizeChange={setPageSize}
                rowsPerPageOptions={[10, 25, 50, 100]}
                loading={loading}
                disableSelectionOnClick
                sx={{
                  border: 'none',
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f1f5f9',
                  },
                  '& .MuiDataGrid-columnHeaders': {
                    backgroundColor: '#f8fafc',
                    borderBottom: '2px solid #e2e8f0',
                  },
                  '& .MuiDataGrid-row:hover': {
                    backgroundColor: '#f8fafc',
                  },
                }}
              />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <div className="flex items-center space-x-2">
            <Package className="w-5 h-5 text-blue-600" />
            <span>Route Card Details - {selectedRow?.WoNo}</span>
          </div>
        </DialogTitle>
        <DialogContent>
          {selectedRow && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Typography variant="subtitle2" color="textSecondary">Part & Product</Typography>
                  <Typography variant="body1">{selectedRow.PartNoAndProductName}</Typography>
                </div>
                <div>
                  <Typography variant="subtitle2" color="textSecondary">Status</Typography>
                  {getStatusChip(selectedRow.Status)}
                </div>
                <div>
                  <Typography variant="subtitle2" color="textSecondary">WO Date</Typography>
                  <Typography variant="body1">{selectedRow.WODate}</Typography>
                </div>
                <div>
                  <Typography variant="subtitle2" color="textSecondary">Location</Typography>
                  <Typography variant="body1">{selectedRow.LocationID}</Typography>
                </div>
                <div>
                  <Typography variant="subtitle2" color="textSecondary">WO Quantity</Typography>
                  <Typography variant="body1">{selectedRow.WO_Qty?.toLocaleString()}</Typography>
                </div>
                <div>
                  <Typography variant="subtitle2" color="textSecondary">Aging</Typography>
                  <Typography variant="body1" className={getAgingColor(selectedRow.AgingInDays)}>
                    {selectedRow.AgingInDays} days
                  </Typography>
                </div>
              </div>
              
              <div>
                <Typography variant="subtitle2" color="textSecondary" className="mb-2">Despatch Details</Typography>
                <Typography variant="body2" className="bg-gray-50 p-3 rounded">
                  {selectedRow.DespatchQtyDetails}
                </Typography>
              </div>
            </div>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default DataTable;

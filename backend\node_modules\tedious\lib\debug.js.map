{"version": 3, "file": "debug.js", "names": ["_events", "require", "util", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "Debug", "EventEmitter", "constructor", "data", "payload", "packet", "token", "options", "indent", "direction", "haveListeners", "log", "headerToString", "dataToString", "generatePayloadText", "inspect", "showHidden", "depth", "colors", "listeners", "length", "text", "emit", "_default", "exports", "module"], "sources": ["../src/debug.ts"], "sourcesContent": ["import { EventEmitter } from 'events';\nimport * as util from 'util';\nimport { Packet } from './packet';\nimport type { Token } from './token/token';\n\nclass Debug extends EventEmitter {\n  declare options: {\n    data: boolean;\n    payload: boolean;\n    packet: boolean;\n    token: boolean;\n  };\n\n  declare indent: string;\n\n  /*\n    @options    Which debug details should be sent.\n                data    - dump of packet data\n                payload - details of decoded payload\n  */\n  constructor({ data = false, payload = false, packet = false, token = false } = {}) {\n    super();\n\n    this.options = { data, payload, packet, token };\n    this.indent = '  ';\n  }\n\n  packet(direction: 'Received' | 'Sent', packet: Packet) {\n    if (this.haveListeners() && this.options.packet) {\n      this.log('');\n      this.log(direction);\n      this.log(packet.headerToString(this.indent));\n    }\n  }\n\n  data(packet: Packet) {\n    if (this.haveListeners() && this.options.data) {\n      this.log(packet.dataToString(this.indent));\n    }\n  }\n\n  payload(generatePayloadText: () => string) {\n    if (this.haveListeners() && this.options.payload) {\n      this.log(generatePayloadText());\n    }\n  }\n\n  token(token: Token) {\n    if (this.haveListeners() && this.options.token) {\n      this.log(util.inspect(token, { showHidden: false, depth: 5, colors: true }));\n    }\n  }\n\n  haveListeners() {\n    return this.listeners('debug').length > 0;\n  }\n\n  log(text: string) {\n    this.emit('debug', text);\n  }\n}\n\nexport default Debug;\nmodule.exports = Debug;\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,IAAA,GAAAC,uBAAA,CAAAF,OAAA;AAA6B,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAI7B,MAAMY,KAAK,SAASC,oBAAY,CAAC;EAU/B;AACF;AACA;AACA;AACA;EACEC,WAAWA,CAAC;IAAEC,IAAI,GAAG,KAAK;IAAEC,OAAO,GAAG,KAAK;IAAEC,MAAM,GAAG,KAAK;IAAEC,KAAK,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IACjF,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,OAAO,GAAG;MAAEJ,IAAI;MAAEC,OAAO;MAAEC,MAAM;MAAEC;IAAM,CAAC;IAC/C,IAAI,CAACE,MAAM,GAAG,IAAI;EACpB;EAEAH,MAAMA,CAACI,SAA8B,EAAEJ,MAAc,EAAE;IACrD,IAAI,IAAI,CAACK,aAAa,CAAC,CAAC,IAAI,IAAI,CAACH,OAAO,CAACF,MAAM,EAAE;MAC/C,IAAI,CAACM,GAAG,CAAC,EAAE,CAAC;MACZ,IAAI,CAACA,GAAG,CAACF,SAAS,CAAC;MACnB,IAAI,CAACE,GAAG,CAACN,MAAM,CAACO,cAAc,CAAC,IAAI,CAACJ,MAAM,CAAC,CAAC;IAC9C;EACF;EAEAL,IAAIA,CAACE,MAAc,EAAE;IACnB,IAAI,IAAI,CAACK,aAAa,CAAC,CAAC,IAAI,IAAI,CAACH,OAAO,CAACJ,IAAI,EAAE;MAC7C,IAAI,CAACQ,GAAG,CAACN,MAAM,CAACQ,YAAY,CAAC,IAAI,CAACL,MAAM,CAAC,CAAC;IAC5C;EACF;EAEAJ,OAAOA,CAACU,mBAAiC,EAAE;IACzC,IAAI,IAAI,CAACJ,aAAa,CAAC,CAAC,IAAI,IAAI,CAACH,OAAO,CAACH,OAAO,EAAE;MAChD,IAAI,CAACO,GAAG,CAACG,mBAAmB,CAAC,CAAC,CAAC;IACjC;EACF;EAEAR,KAAKA,CAACA,KAAY,EAAE;IAClB,IAAI,IAAI,CAACI,aAAa,CAAC,CAAC,IAAI,IAAI,CAACH,OAAO,CAACD,KAAK,EAAE;MAC9C,IAAI,CAACK,GAAG,CAAClC,IAAI,CAACsC,OAAO,CAACT,KAAK,EAAE;QAAEU,UAAU,EAAE,KAAK;QAAEC,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAK,CAAC,CAAC,CAAC;IAC9E;EACF;EAEAR,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAACS,SAAS,CAAC,OAAO,CAAC,CAACC,MAAM,GAAG,CAAC;EAC3C;EAEAT,GAAGA,CAACU,IAAY,EAAE;IAChB,IAAI,CAACC,IAAI,CAAC,OAAO,EAAED,IAAI,CAAC;EAC1B;AACF;AAAC,IAAAE,QAAA,GAAAC,OAAA,CAAAvC,OAAA,GAEce,KAAK;AACpByB,MAAM,CAACD,OAAO,GAAGxB,KAAK"}
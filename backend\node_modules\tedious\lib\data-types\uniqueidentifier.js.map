{"version": 3, "file": "uniqueidentifier.js", "names": ["_guid<PERSON><PERSON>er", "require", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "DATA_LENGTH", "UniqueIdentifier", "id", "type", "name", "declaration", "<PERSON><PERSON><PERSON><PERSON>", "generateTypeInfo", "generateParameterLength", "parameter", "options", "value", "generateParameterData", "guidToArray", "validate", "TypeError", "test", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/uniqueidentifier.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport { guidToArray } from '../guid-parser';\n\nconst NULL_LENGTH = Buffer.from([0x00]);\nconst DATA_LENGTH = Buffer.from([0x10]);\n\nconst UniqueIdentifier: DataType = {\n  id: 0x24,\n  type: 'GUIDN',\n  name: 'UniqueIdentifier',\n\n  declaration: function() {\n    return 'uniqueidentifier';\n  },\n\n  resolveLength: function() {\n    return 16;\n  },\n\n  generateTypeInfo() {\n    return Buffer.from([this.id, 0x10]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    return DATA_LENGTH;\n  },\n\n  generateParameterData: function*(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    yield Buffer.from(guidToArray(parameter.value));\n  },\n\n  validate: function(value): string | null {\n    if (value == null) {\n      return null;\n    }\n\n    if (typeof value !== 'string') {\n      throw new TypeError('Invalid string.');\n    }\n\n    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)) {\n      throw new TypeError('Invalid GUID.');\n    }\n\n    return value;\n  }\n};\n\nexport default UniqueIdentifier;\nmodule.exports = UniqueIdentifier;\n"], "mappings": ";;;;;;AACA,IAAAA,WAAA,GAAAC,OAAA;AAEA,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,MAAMC,WAAW,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAME,gBAA0B,GAAG;EACjCC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,kBAAkB;EAExBC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,kBAAkB;EAC3B,CAAC;EAEDC,aAAa,EAAE,SAAAA,CAAA,EAAW;IACxB,OAAO,EAAE;EACX,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,OAAOT,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAACG,EAAE,EAAE,IAAI,CAAC,CAAC;EACrC,CAAC;EAEDM,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOd,WAAW;IACpB;IAEA,OAAOG,WAAW;EACpB,CAAC;EAEDY,qBAAqB,EAAE,UAAAA,CAAUH,SAAS,EAAEC,OAAO,EAAE;IACnD,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMb,MAAM,CAACC,IAAI,CAAC,IAAAc,uBAAW,EAACJ,SAAS,CAACE,KAAK,CAAC,CAAC;EACjD,CAAC;EAEDG,QAAQ,EAAE,SAAAA,CAASH,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAII,SAAS,CAAC,iBAAiB,CAAC;IACxC;IAEA,IAAI,CAAC,iEAAiE,CAACC,IAAI,CAACL,KAAK,CAAC,EAAE;MAClF,MAAM,IAAII,SAAS,CAAC,eAAe,CAAC;IACtC;IAEA,OAAOJ,KAAK;EACd;AACF,CAAC;AAAC,IAAAM,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEalB,gBAAgB;AAC/BmB,MAAM,CAACF,OAAO,GAAGjB,gBAAgB"}
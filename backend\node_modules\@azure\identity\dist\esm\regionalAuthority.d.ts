/**
 * Helps specify a regional authority, or "AutoDiscoverRegion" to auto-detect the region.
 */
export declare enum RegionalAuthority {
    /** Instructs MSAL to attempt to discover the region */
    AutoDiscoverRegion = "AutoDiscoverRegion",
    /** Uses the {@link RegionalAuthority} for the Azure 'westus' region. */
    USWest = "westus",
    /** Uses the {@link RegionalAuthority} for the Azure 'westus2' region. */
    USWest2 = "westus2",
    /** Uses the {@link RegionalAuthority} for the Azure 'centralus' region. */
    USCentral = "centralus",
    /** Uses the {@link RegionalAuthority} for the Azure 'eastus' region. */
    USEast = "eastus",
    /** Uses the {@link RegionalAuthority} for the Azure 'eastus2' region. */
    USEast2 = "eastus2",
    /** Uses the {@link RegionalAuthority} for the Azure 'northcentralus' region. */
    USNorthCentral = "northcentralus",
    /** Uses the {@link RegionalAuthority} for the Azure 'southcentralus' region. */
    USSouthCentral = "southcentralus",
    /** Uses the {@link RegionalAuthority} for the Azure 'westcentralus' region. */
    USWestCentral = "westcentralus",
    /** Uses the {@link RegionalAuthority} for the Azure 'canadacentral' region. */
    CanadaCentral = "canadacentral",
    /** Uses the {@link RegionalAuthority} for the Azure 'canadaeast' region. */
    CanadaEast = "canadaeast",
    /** Uses the {@link RegionalAuthority} for the Azure 'brazilsouth' region. */
    BrazilSouth = "brazilsouth",
    /** Uses the {@link RegionalAuthority} for the Azure 'northeurope' region. */
    EuropeNorth = "northeurope",
    /** Uses the {@link RegionalAuthority} for the Azure 'westeurope' region. */
    EuropeWest = "westeurope",
    /** Uses the {@link RegionalAuthority} for the Azure 'uksouth' region. */
    UKSouth = "uksouth",
    /** Uses the {@link RegionalAuthority} for the Azure 'ukwest' region. */
    UKWest = "ukwest",
    /** Uses the {@link RegionalAuthority} for the Azure 'francecentral' region. */
    FranceCentral = "francecentral",
    /** Uses the {@link RegionalAuthority} for the Azure 'francesouth' region. */
    FranceSouth = "francesouth",
    /** Uses the {@link RegionalAuthority} for the Azure 'switzerlandnorth' region. */
    SwitzerlandNorth = "switzerlandnorth",
    /** Uses the {@link RegionalAuthority} for the Azure 'switzerlandwest' region. */
    SwitzerlandWest = "switzerlandwest",
    /** Uses the {@link RegionalAuthority} for the Azure 'germanynorth' region. */
    GermanyNorth = "germanynorth",
    /** Uses the {@link RegionalAuthority} for the Azure 'germanywestcentral' region. */
    GermanyWestCentral = "germanywestcentral",
    /** Uses the {@link RegionalAuthority} for the Azure 'norwaywest' region. */
    NorwayWest = "norwaywest",
    /** Uses the {@link RegionalAuthority} for the Azure 'norwayeast' region. */
    NorwayEast = "norwayeast",
    /** Uses the {@link RegionalAuthority} for the Azure 'eastasia' region. */
    AsiaEast = "eastasia",
    /** Uses the {@link RegionalAuthority} for the Azure 'southeastasia' region. */
    AsiaSouthEast = "southeastasia",
    /** Uses the {@link RegionalAuthority} for the Azure 'japaneast' region. */
    JapanEast = "japaneast",
    /** Uses the {@link RegionalAuthority} for the Azure 'japanwest' region. */
    JapanWest = "japanwest",
    /** Uses the {@link RegionalAuthority} for the Azure 'australiaeast' region. */
    AustraliaEast = "australiaeast",
    /** Uses the {@link RegionalAuthority} for the Azure 'australiasoutheast' region. */
    AustraliaSouthEast = "australiasoutheast",
    /** Uses the {@link RegionalAuthority} for the Azure 'australiacentral' region. */
    AustraliaCentral = "australiacentral",
    /** Uses the {@link RegionalAuthority} for the Azure 'australiacentral2' region. */
    AustraliaCentral2 = "australiacentral2",
    /** Uses the {@link RegionalAuthority} for the Azure 'centralindia' region. */
    IndiaCentral = "centralindia",
    /** Uses the {@link RegionalAuthority} for the Azure 'southindia' region. */
    IndiaSouth = "southindia",
    /** Uses the {@link RegionalAuthority} for the Azure 'westindia' region. */
    IndiaWest = "westindia",
    /** Uses the {@link RegionalAuthority} for the Azure 'koreasouth' region. */
    KoreaSouth = "koreasouth",
    /** Uses the {@link RegionalAuthority} for the Azure 'koreacentral' region. */
    KoreaCentral = "koreacentral",
    /** Uses the {@link RegionalAuthority} for the Azure 'uaecentral' region. */
    UAECentral = "uaecentral",
    /** Uses the {@link RegionalAuthority} for the Azure 'uaenorth' region. */
    UAENorth = "uaenorth",
    /** Uses the {@link RegionalAuthority} for the Azure 'southafricanorth' region. */
    SouthAfricaNorth = "southafricanorth",
    /** Uses the {@link RegionalAuthority} for the Azure 'southafricawest' region. */
    SouthAfricaWest = "southafricawest",
    /** Uses the {@link RegionalAuthority} for the Azure 'chinanorth' region. */
    ChinaNorth = "chinanorth",
    /** Uses the {@link RegionalAuthority} for the Azure 'chinaeast' region. */
    ChinaEast = "chinaeast",
    /** Uses the {@link RegionalAuthority} for the Azure 'chinanorth2' region. */
    ChinaNorth2 = "chinanorth2",
    /** Uses the {@link RegionalAuthority} for the Azure 'chinaeast2' region. */
    ChinaEast2 = "chinaeast2",
    /** Uses the {@link RegionalAuthority} for the Azure 'germanycentral' region. */
    GermanyCentral = "germanycentral",
    /** Uses the {@link RegionalAuthority} for the Azure 'germanynortheast' region. */
    GermanyNorthEast = "germanynortheast",
    /** Uses the {@link RegionalAuthority} for the Azure 'usgovvirginia' region. */
    GovernmentUSVirginia = "usgovvirginia",
    /** Uses the {@link RegionalAuthority} for the Azure 'usgoviowa' region. */
    GovernmentUSIowa = "usgoviowa",
    /** Uses the {@link RegionalAuthority} for the Azure 'usgovarizona' region. */
    GovernmentUSArizona = "usgovarizona",
    /** Uses the {@link RegionalAuthority} for the Azure 'usgovtexas' region. */
    GovernmentUSTexas = "usgovtexas",
    /** Uses the {@link RegionalAuthority} for the Azure 'usdodeast' region. */
    GovernmentUSDodEast = "usdodeast",
    /** Uses the {@link RegionalAuthority} for the Azure 'usdodcentral' region. */
    GovernmentUSDodCentral = "usdodcentral"
}
/**
 * Calculates the correct regional authority based on the supplied value
 * and the AZURE_REGIONAL_AUTHORITY_NAME environment variable.
 *
 * Values will be returned verbatim, except for {@link RegionalAuthority.AutoDiscoverRegion}
 * which is mapped to a value MSAL can understand.
 *
 * @internal
 */
export declare function calculateRegionalAuthority(regionalAuthority?: string): string | undefined;
//# sourceMappingURL=regionalAuthority.d.ts.map
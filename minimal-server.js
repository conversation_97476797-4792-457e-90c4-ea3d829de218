const express = require('./backend/node_modules/express');
const sql = require('./backend/node_modules/mssql');

const app = express();
const PORT = 5004;

// Database configuration
const dbConfig = {
    server: 'WIN-PRK-SRV-01',
    database: 'ICsoft',
    user: 'sa',
    password: 'PRK@1234',
    options: {
        trustServerCertificate: true,
        enableArithAbort: true,
        encrypt: false
    }
};

let pool;

// Initialize database connection
async function initDB() {
    try {
        console.log('Connecting to database...');
        pool = await sql.connect(dbConfig);
        console.log('✅ Database connected');
        
        // Test query
        const result = await pool.request().query('SELECT COUNT(*) as count FROM WorkOrder');
        console.log('✅ Test query successful, WorkOrder count:', result.recordset[0].count);
        
    } catch (error) {
        console.error('❌ Database error:', error.message);
        throw error;
    }
}

// Routes
app.get('/test', async (req, res) => {
    try {
        const result = await pool.request().query('SELECT TOP 5 WoNo FROM WorkOrder');
        res.json({
            success: true,
            data: result.recordset
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.get('/health', (req, res) => {
    res.json({ success: true, message: 'Server running' });
});

// Start server
async function start() {
    try {
        await initDB();
        app.listen(PORT, () => {
            console.log(`🚀 Server running on http://localhost:${PORT}`);
            console.log(`Test: http://localhost:${PORT}/test`);
        });
    } catch (error) {
        console.error('Failed to start:', error);
        process.exit(1);
    }
}

start();

# RC Dashboard - Startup Guide

## 🚀 Quick Start Options

### Option 1: Quick Start (Recommended)
```cmd
quick-start.bat
```
- **Simple and fast**
- Starts backend automatically
- Opens dashboard in 15 seconds
- Best for daily use

### Option 2: Comprehensive Start
```cmd
start-dashboard.bat
```
- **Full system startup**
- Kills existing processes
- Starts both backend and frontend
- Includes health checks
- Best for development

### Option 3: Test with Auto-Start
```cmd
test-final-dashboard.bat
```
- **Tests and starts everything**
- Comprehensive system check
- Shows all features
- Auto-starts backend if needed
- Best for first-time setup

### Option 4: Manual Start
```cmd
cd backend
npm start
```
Then open `simple-dashboard.html`

## 🎯 Dashboard Features

### ✅ Perfect Auto-Slide
- **Scrolls vertically** through all data first
- **Moves to next page** when bottom reached
- **8-second intervals** for comfortable viewing
- **Smooth animations** throughout

### ✅ Auto-Refresh
- **5-minute intervals** for fresh data
- **Maintains auto-slide** during refresh
- **Real-time SQL Server** connection
- **No manual intervention** required

### ✅ Clean Interface
- **Location filtering** only
- **No unnecessary buttons**
- **Professional appearance**
- **Responsive design**

### ✅ Data Display
- **9 essential columns**
- **Color-coded priorities**
- **Aging analysis**
- **Quality metrics**

## 🔧 Troubleshooting

### Backend Won't Start
1. Check if port 5001 is free
2. Run `taskkill /f /im node.exe`
3. Try `quick-start.bat` again

### Dashboard Shows "Loading"
1. Verify backend is running
2. Check `http://localhost:5001/api/health`
3. Ensure SQL Server connection

### No Data Displayed
1. Check SQL Server connection
2. Verify database credentials
3. Check console (F12) for errors

## 📊 Expected Behavior

1. **Dashboard loads** with real route card data
2. **Auto-slide starts** immediately
3. **Scrolls down** through visible data
4. **Moves to next page** when done
5. **Continues cycling** through all pages
6. **Refreshes data** every 5 minutes
7. **Location filter** works instantly

## 🎨 Visual Features

- **Gradient headers** with professional styling
- **Hover effects** on table rows
- **Color-coded aging** (Green ≤15, Yellow 16-45, Orange 46-90, Red >90 days)
- **Status badges** for RC Pending
- **Smooth transitions** between pages

## 💡 Usage Tips

- **Keep backend window open** while using dashboard
- **Use location filter** for targeted viewing
- **Perfect for unattended displays** and management dashboards
- **No user interaction required** once started
- **Professional appearance** suitable for presentations

## 🔄 System Requirements

- **Node.js** installed
- **SQL Server** connection configured
- **Modern web browser**
- **Network access** to database server

Choose the startup option that best fits your needs!

{"version": 3, "file": "tds-versions.js", "names": ["versions", "exports", "versionsByValue", "name"], "sources": ["../src/tds-versions.ts"], "sourcesContent": ["export const versions: { [key: string]: number } = {\n  '7_1': 0x71000001,\n  '7_2': 0x72090002,\n  '7_3_A': 0x730A0003,\n  '7_3_B': 0x730B0003,\n  '7_4': 0x74000004,\n  '8_0': 0x08000000\n};\n\nexport const versionsByValue: { [key: number]: string } = {};\n\nfor (const name in versions) {\n  versionsByValue[versions[name]] = name;\n}\n"], "mappings": ";;;;;;AAAO,MAAMA,QAAmC,GAAAC,OAAA,CAAAD,QAAA,GAAG;EACjD,KAAK,EAAE,UAAU;EACjB,KAAK,EAAE,UAAU;EACjB,OAAO,EAAE,UAAU;EACnB,OAAO,EAAE,UAAU;EACnB,KAAK,EAAE,UAAU;EACjB,KAAK,EAAE;AACT,CAAC;AAEM,MAAME,eAA0C,GAAAD,OAAA,CAAAC,eAAA,GAAG,CAAC,CAAC;AAE5D,KAAK,MAAMC,IAAI,IAAIH,QAAQ,EAAE;EAC3BE,eAAe,CAACF,QAAQ,CAACG,IAAI,CAAC,CAAC,GAAGA,IAAI;AACxC"}
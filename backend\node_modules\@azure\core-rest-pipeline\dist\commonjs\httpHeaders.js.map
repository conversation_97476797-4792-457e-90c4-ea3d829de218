{"version": 3, "file": "httpHeaders.js", "sourceRoot": "", "sources": ["../../src/httpHeaders.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAUlC,8CAEC;AARD,+DAAsF;AAEtF;;;GAGG;AACH,SAAgB,iBAAiB,CAAC,UAAgC;IAChE,OAAO,IAAA,mCAAoB,EAAC,UAAU,CAAC,CAAC;AAC1C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpHeaders, RawHttpHeadersInput } from \"./interfaces.js\";\n\nimport { createHttpHeaders as tspCreateHttpHeaders } from \"@typespec/ts-http-runtime\";\n\n/**\n * Creates an object that satisfies the `HttpHeaders` interface.\n * @param rawHeaders - A simple object representing initial headers\n */\nexport function createHttpHeaders(rawHeaders?: RawHttpHeadersInput): HttpHeaders {\n  return tspCreateHttpHeaders(rawHeaders);\n}\n"]}
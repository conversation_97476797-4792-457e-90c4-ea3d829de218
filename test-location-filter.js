const fetch = require('node-fetch');

async function testLocationFilter() {
    const baseUrl = 'http://localhost:5001/api';
    
    console.log('🔄 Testing Location Filter...\n');
    
    try {
        // Test 1: Get all locations
        console.log('1. Testing /api/locations');
        const locationsResponse = await fetch(`${baseUrl}/locations`);
        const locationsData = await locationsResponse.json();
        console.log('Locations:', JSON.stringify(locationsData, null, 2));
        
        // Test 2: Get all RC data (no filter)
        console.log('\n2. Testing /api/rc-pending (no filter)');
        const allDataResponse = await fetch(`${baseUrl}/rc-pending`);
        const allData = await allDataResponse.json();
        console.log(`Total RCs: ${allData.data ? allData.data.length : 0}`);
        
        // Test 3: Get RC data filtered by location 2
        console.log('\n3. Testing /api/rc-pending?locationId=2');
        const loc2Response = await fetch(`${baseUrl}/rc-pending?locationId=2`);
        const loc2Data = await loc2Response.json();
        console.log(`Location 2 RCs: ${loc2Data.data ? loc2Data.data.length : 0}`);
        
        // Test 4: Get RC data filtered by location 3
        console.log('\n4. Testing /api/rc-pending?locationId=3');
        const loc3Response = await fetch(`${baseUrl}/rc-pending?locationId=3`);
        const loc3Data = await loc3Response.json();
        console.log(`Location 3 RCs: ${loc3Data.data ? loc3Data.data.length : 0}`);
        
        // Test 5: Get stats with location filter
        console.log('\n5. Testing /api/dashboard-stats?locationId=2');
        const statsResponse = await fetch(`${baseUrl}/dashboard-stats?locationId=2`);
        const statsData = await statsResponse.json();
        console.log('Stats for Location 2:', JSON.stringify(statsData.data, null, 2));
        
    } catch (error) {
        console.error('❌ Error testing location filter:', error.message);
    }
}

testLocationFilter();

{"version": 3, "file": "datetime.js", "names": ["_datetimen", "_interopRequireDefault", "require", "_core", "obj", "__esModule", "default", "EPOCH_DATE", "LocalDate", "ofYearDay", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "DATA_LENGTH", "DateTime", "id", "type", "name", "declaration", "generateTypeInfo", "DateTimeN", "generateParameterLength", "parameter", "options", "value", "generateParameterData", "date", "useUTC", "of", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getFullYear", "getMonth", "getDate", "days", "until", "ChronoUnit", "DAYS", "milliseconds", "threeHundredthsOfSecond", "seconds", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "getUTCMilliseconds", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "Math", "round", "buffer", "alloc", "writeInt32LE", "writeUInt32LE", "validate", "collation", "Date", "parse", "year", "TypeError", "isNaN", "_default", "exports", "module"], "sources": ["../../src/data-types/datetime.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport DateTimeN from './datetimen';\nimport { ChronoUnit, LocalDate } from '@js-joda/core';\n\nconst EPOCH_DATE = LocalDate.ofYearDay(1900, 1);\nconst NULL_LENGTH = Buffer.from([0x00]);\nconst DATA_LENGTH = Buffer.from([0x08]);\n\nconst DateTime: DataType = {\n  id: 0x3D,\n  type: 'DATETIME',\n  name: 'DateTime',\n\n  declaration: function() {\n    return 'datetime';\n  },\n\n  generateTypeInfo() {\n    return Buffer.from([DateTimeN.id, 0x08]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    return DATA_LENGTH;\n  },\n\n  generateParameterData: function*(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const value = parameter.value as any; // Temporary solution. Remove 'any' later.\n\n    let date: LocalDate;\n    if (options.useUTC) {\n      date = LocalDate.of(value.getUTCFullYear(), value.getUTCMonth() + 1, value.getUTCDate());\n    } else {\n      date = LocalDate.of(value.getFullYear(), value.getMonth() + 1, value.getDate());\n    }\n\n    let days = EPOCH_DATE.until(date, ChronoUnit.DAYS);\n\n    let milliseconds, threeHundredthsOfSecond;\n    if (options.useUTC) {\n      let seconds = value.getUTCHours() * 60 * 60;\n      seconds += value.getUTCMinutes() * 60;\n      seconds += value.getUTCSeconds();\n      milliseconds = (seconds * 1000) + value.getUTCMilliseconds();\n    } else {\n      let seconds = value.getHours() * 60 * 60;\n      seconds += value.getMinutes() * 60;\n      seconds += value.getSeconds();\n      milliseconds = (seconds * 1000) + value.getMilliseconds();\n    }\n\n    threeHundredthsOfSecond = milliseconds / (3 + (1 / 3));\n    threeHundredthsOfSecond = Math.round(threeHundredthsOfSecond);\n\n    // 25920000 equals one day\n    if (threeHundredthsOfSecond === 25920000) {\n      days += 1;\n      threeHundredthsOfSecond = 0;\n    }\n\n    const buffer = Buffer.alloc(8);\n    buffer.writeInt32LE(days, 0);\n    buffer.writeUInt32LE(threeHundredthsOfSecond, 4);\n    yield buffer;\n  },\n\n  // TODO: type 'any' needs to be revisited.\n  validate: function(value: any, collation, options): null | number {\n    if (value == null) {\n      return null;\n    }\n\n    if (!(value instanceof Date)) {\n      value = new Date(Date.parse(value));\n    }\n\n    value = value as Date;\n\n    let year;\n    if (options && options.useUTC) {\n      year = value.getUTCFullYear();\n    } else {\n      year = value.getFullYear();\n    }\n\n    if (year < 1753 || year > 9999) {\n      throw new TypeError('Out of range.');\n    }\n\n    if (isNaN(value)) {\n      throw new TypeError('Invalid date.');\n    }\n\n    return value;\n  }\n};\n\nexport default DateTime;\nmodule.exports = DateTime;\n"], "mappings": ";;;;;;AACA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAsD,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEtD,MAAMG,UAAU,GAAGC,eAAS,CAACC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;AAC/C,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,MAAMC,WAAW,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAME,QAAkB,GAAG;EACzBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,UAAU;EAEhBC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,UAAU;EACnB,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,OAAOR,MAAM,CAACC,IAAI,CAAC,CAACQ,kBAAS,CAACL,EAAE,EAAE,IAAI,CAAC,CAAC;EAC1C,CAAC;EAEDM,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOd,WAAW;IACpB;IAEA,OAAOG,WAAW;EACpB,CAAC;EAEDY,qBAAqB,EAAE,UAAAA,CAAUH,SAAS,EAAEC,OAAO,EAAE;IACnD,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMA,KAAK,GAAGF,SAAS,CAACE,KAAY,CAAC,CAAC;;IAEtC,IAAIE,IAAe;IACnB,IAAIH,OAAO,CAACI,MAAM,EAAE;MAClBD,IAAI,GAAGlB,eAAS,CAACoB,EAAE,CAACJ,KAAK,CAACK,cAAc,CAAC,CAAC,EAAEL,KAAK,CAACM,WAAW,CAAC,CAAC,GAAG,CAAC,EAAEN,KAAK,CAACO,UAAU,CAAC,CAAC,CAAC;IAC1F,CAAC,MAAM;MACLL,IAAI,GAAGlB,eAAS,CAACoB,EAAE,CAACJ,KAAK,CAACQ,WAAW,CAAC,CAAC,EAAER,KAAK,CAACS,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAET,KAAK,CAACU,OAAO,CAAC,CAAC,CAAC;IACjF;IAEA,IAAIC,IAAI,GAAG5B,UAAU,CAAC6B,KAAK,CAACV,IAAI,EAAEW,gBAAU,CAACC,IAAI,CAAC;IAElD,IAAIC,YAAY,EAAEC,uBAAuB;IACzC,IAAIjB,OAAO,CAACI,MAAM,EAAE;MAClB,IAAIc,OAAO,GAAGjB,KAAK,CAACkB,WAAW,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;MAC3CD,OAAO,IAAIjB,KAAK,CAACmB,aAAa,CAAC,CAAC,GAAG,EAAE;MACrCF,OAAO,IAAIjB,KAAK,CAACoB,aAAa,CAAC,CAAC;MAChCL,YAAY,GAAIE,OAAO,GAAG,IAAI,GAAIjB,KAAK,CAACqB,kBAAkB,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL,IAAIJ,OAAO,GAAGjB,KAAK,CAACsB,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;MACxCL,OAAO,IAAIjB,KAAK,CAACuB,UAAU,CAAC,CAAC,GAAG,EAAE;MAClCN,OAAO,IAAIjB,KAAK,CAACwB,UAAU,CAAC,CAAC;MAC7BT,YAAY,GAAIE,OAAO,GAAG,IAAI,GAAIjB,KAAK,CAACyB,eAAe,CAAC,CAAC;IAC3D;IAEAT,uBAAuB,GAAGD,YAAY,IAAI,CAAC,GAAI,CAAC,GAAG,CAAE,CAAC;IACtDC,uBAAuB,GAAGU,IAAI,CAACC,KAAK,CAACX,uBAAuB,CAAC;;IAE7D;IACA,IAAIA,uBAAuB,KAAK,QAAQ,EAAE;MACxCL,IAAI,IAAI,CAAC;MACTK,uBAAuB,GAAG,CAAC;IAC7B;IAEA,MAAMY,MAAM,GAAGzC,MAAM,CAAC0C,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,YAAY,CAACnB,IAAI,EAAE,CAAC,CAAC;IAC5BiB,MAAM,CAACG,aAAa,CAACf,uBAAuB,EAAE,CAAC,CAAC;IAChD,MAAMY,MAAM;EACd,CAAC;EAED;EACAI,QAAQ,EAAE,SAAAA,CAAShC,KAAU,EAAEiC,SAAS,EAAElC,OAAO,EAAiB;IAChE,IAAIC,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,EAAEA,KAAK,YAAYkC,IAAI,CAAC,EAAE;MAC5BlC,KAAK,GAAG,IAAIkC,IAAI,CAACA,IAAI,CAACC,KAAK,CAACnC,KAAK,CAAC,CAAC;IACrC;IAEAA,KAAK,GAAGA,KAAa;IAErB,IAAIoC,IAAI;IACR,IAAIrC,OAAO,IAAIA,OAAO,CAACI,MAAM,EAAE;MAC7BiC,IAAI,GAAGpC,KAAK,CAACK,cAAc,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL+B,IAAI,GAAGpC,KAAK,CAACQ,WAAW,CAAC,CAAC;IAC5B;IAEA,IAAI4B,IAAI,GAAG,IAAI,IAAIA,IAAI,GAAG,IAAI,EAAE;MAC9B,MAAM,IAAIC,SAAS,CAAC,eAAe,CAAC;IACtC;IAEA,IAAIC,KAAK,CAACtC,KAAK,CAAC,EAAE;MAChB,MAAM,IAAIqC,SAAS,CAAC,eAAe,CAAC;IACtC;IAEA,OAAOrC,KAAK;EACd;AACF,CAAC;AAAC,IAAAuC,QAAA,GAAAC,OAAA,CAAA1D,OAAA,GAEaQ,QAAQ;AACvBmD,MAAM,CAACD,OAAO,GAAGlD,QAAQ"}
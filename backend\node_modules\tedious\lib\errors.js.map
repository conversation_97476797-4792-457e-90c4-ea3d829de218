{"version": 3, "file": "errors.js", "names": ["ConnectionError", "Error", "constructor", "message", "code", "options", "exports", "RequestError", "InputError", "TypeError"], "sources": ["../src/errors.ts"], "sourcesContent": ["export class ConnectionError extends Error {\n  declare code: string | undefined;\n\n  declare isTransient: boolean | undefined;\n\n  constructor(message: string, code?: string, options?: ErrorOptions) {\n    super(message, options);\n\n    this.code = code;\n  }\n}\n\nexport class RequestError extends Error {\n  declare code: string | undefined;\n\n  declare number: number | undefined;\n  declare state: number | undefined;\n  declare class: number | undefined;\n  declare serverName: string | undefined;\n  declare procName: string | undefined;\n  declare lineNumber: number | undefined;\n\n  constructor(message: string, code?: string, options?: ErrorOptions) {\n    super(message, options);\n\n    this.code = code;\n  }\n}\n\nexport class InputError extends TypeError {}\n"], "mappings": ";;;;;;AAAO,MAAMA,eAAe,SAASC,KAAK,CAAC;EAKzCC,WAAWA,CAACC,OAAe,EAAEC,IAAa,EAAEC,OAAsB,EAAE;IAClE,KAAK,CAACF,OAAO,EAAEE,OAAO,CAAC;IAEvB,IAAI,CAACD,IAAI,GAAGA,IAAI;EAClB;AACF;AAACE,OAAA,CAAAN,eAAA,GAAAA,eAAA;AAEM,MAAMO,YAAY,SAASN,KAAK,CAAC;EAUtCC,WAAWA,CAACC,OAAe,EAAEC,IAAa,EAAEC,OAAsB,EAAE;IAClE,KAAK,CAACF,OAAO,EAAEE,OAAO,CAAC;IAEvB,IAAI,CAACD,IAAI,GAAGA,IAAI;EAClB;AACF;AAACE,OAAA,CAAAC,YAAA,GAAAA,YAAA;AAEM,MAAMC,UAAU,SAASC,SAAS,CAAC;AAAEH,OAAA,CAAAE,UAAA,GAAAA,UAAA"}
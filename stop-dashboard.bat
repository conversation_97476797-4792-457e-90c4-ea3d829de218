@echo off
echo ========================================
echo    RC Dashboard Stop Script
echo ========================================
echo.

echo [1/3] Stopping all dashboard processes...

REM Kill processes by port
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":5001"') do (
    echo Stopping backend process Port 5001: %%a
    taskkill /f /pid %%a >nul 2>&1
)

for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":5174"') do (
    echo Stopping frontend process Port 5174: %%a
    taskkill /f /pid %%a >nul 2>&1
)

for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":5173"') do (
    echo Stopping frontend process Port 5173: %%a
    taskkill /f /pid %%a >nul 2>&1
)

echo [2/3] Killing Node.js processes...
taskkill /f /im node.exe >nul 2>&1

echo [3/3] Cleaning up...
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo    All Dashboard Processes Stopped
echo ========================================
echo.
echo Press any key to close this window...
pause >nul

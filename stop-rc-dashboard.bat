@echo off
title RC Dashboard - Stopping Services
color 0C

echo.
echo ========================================
echo    RC DASHBOARD - STOPPING SERVICES
echo ========================================
echo.

echo [1/2] Stopping Node.js processes...
taskkill /f /im node.exe >nul 2>&1
if errorlevel 1 (
    echo No Node.js processes found running
) else (
    echo ✓ Node.js processes stopped
)

echo.
echo [2/2] Cleaning up...
timeout /t 1 /nobreak >nul

echo.
echo ========================================
echo    RC DASHBOARD SERVICES STOPPED
echo ========================================
echo.
echo All services have been stopped:
echo - Backend API Server
echo - Any running Node.js processes
echo.
echo To restart, run: start-rc-dashboard.bat
echo.
pause

@echo off
echo ========================================
echo    RC Dashboard Quick Test
echo ========================================
echo.

echo Testing Backend API...
curl -s http://localhost:5001/api/health
if %errorlevel% neq 0 (
    echo ❌ Backend is NOT running
    echo.
    echo To start the backend:
    echo 1. Run: start-simple.bat
    echo 2. Or manually: cd backend && npm start
    echo.
) else (
    echo ✅ Backend is running!
    echo.
    echo Testing Dashboard Stats...
    curl -s http://localhost:5001/api/dashboard-stats
    echo.
    echo.
    echo Testing Locations...
    curl -s http://localhost:5001/api/locations
    echo.
    echo.
    echo ✅ All APIs are working!
    echo.
    echo You can now open: simple-dashboard.html
    echo Or run: start simple-dashboard.html
)

echo.
echo Press any key to close...
pause >nul

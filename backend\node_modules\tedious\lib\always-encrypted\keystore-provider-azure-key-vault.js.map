{"version": 3, "file": "keystore-provider-azure-key-vault.js", "names": ["_identity", "require", "_keyvaultKeys", "_crypto", "_url", "ColumnEncryptionAzureKeyVaultProvider", "constructor", "clientId", "client<PERSON>ey", "tenantId", "name", "azureKeyVaultDomainName", "rsaEncryptionAlgorithmWithOAEPForAKV", "firstVersion", "<PERSON><PERSON><PERSON>", "from", "credentials", "ClientSecretCredential", "decryptColumnEncryptionKey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "encryptionAlgorithm", "encryptedColumnEncryptionKey", "Error", "length", "validateEncryptionAlgorithm", "<PERSON><PERSON><PERSON>", "getMasterKey", "keySizeInBytes", "getAKVKeySize", "cryptoClient", "createCryptoClient", "toString", "currentIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readInt16LE", "cipherTextLength", "<PERSON><PERSON><PERSON><PERSON>", "cipherText", "alloc", "copy", "signature", "hash", "messageDigest", "createHash", "update", "dataToVerify", "digest", "<PERSON><PERSON><PERSON>", "verify", "result", "decryptedCEK", "azureKeyVaultUnWrap", "encryptColumnEncryptionKey", "columnEncryptionKey", "version", "masterKeyPathBytes", "toLowerCase", "azureKeyVaultWrap", "dataToHash", "destinationPosition", "dataToSign", "signedHash", "azureKeyVaultSignedHashedData", "encryptedColumnEncryptionKeyLength", "keyParts", "parsePath", "createKeyClient", "vaultUrl", "keyClient", "<PERSON><PERSON><PERSON>", "keyVaultUrl", "url", "KeyClient", "CryptographyClient", "trim", "baseUri", "parse", "hostname", "endsWith", "segments", "pathname", "split", "protocol", "host", "undefined", "signedData", "sign", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "unwrapped<PERSON>ey", "unwrap<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "key", "kty", "toUpperCase", "localeCompare", "<PERSON><PERSON><PERSON><PERSON>", "n", "exports"], "sources": ["../../src/always-encrypted/keystore-provider-azure-key-vault.ts"], "sourcesContent": ["// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.\n// Copyright (c) 2019 Microsoft Corporation\n\nimport { ClientSecretCredential } from '@azure/identity';\nimport { CryptographyClient, type KeyWrapAlgorithm, KeyClient, type KeyVaultKey } from '@azure/keyvault-keys';\nimport { createHash } from 'crypto';\nimport { parse } from 'url';\n\ninterface ParsedKeyPath {\n  vaultUrl: string;\n  name: string;\n  version?: string | undefined;\n}\n\nexport class ColumnEncryptionAzureKeyVaultProvider {\n  declare public readonly name: string;\n  declare private url: undefined | string;\n  declare private readonly rsaEncryptionAlgorithmWithOAEPForAKV: string;\n  declare private readonly firstVersion: Buffer;\n  declare private credentials: ClientSecretCredential;\n  declare private readonly azureKeyVaultDomainName: string;\n  declare private keyClient: undefined | KeyClient;\n\n  constructor(clientId: string, clientKey: string, tenantId: string) {\n    this.name = 'AZURE_KEY_VAULT';\n    this.azureKeyVaultDomainName = 'vault.azure.net';\n    this.rsaEncryptionAlgorithmWithOAEPForAKV = 'RSA-OAEP';\n    this.firstVersion = Buffer.from([0x01]);\n    this.credentials = new ClientSecretCredential(tenantId, clientId, clientKey);\n  }\n\n  async decryptColumnEncryptionKey(masterKeyPath: string, encryptionAlgorithm: string, encryptedColumnEncryptionKey: Buffer): Promise<Buffer> {\n    if (!encryptedColumnEncryptionKey) {\n      throw new Error('Internal error. Encrypted column encryption key cannot be null.');\n    }\n\n    if (encryptedColumnEncryptionKey.length === 0) {\n      throw new Error('Internal error. Empty encrypted column encryption key specified.');\n    }\n\n    encryptionAlgorithm = this.validateEncryptionAlgorithm(encryptionAlgorithm);\n\n    const masterKey = await this.getMasterKey(masterKeyPath);\n\n    const keySizeInBytes = this.getAKVKeySize(masterKey);\n\n    const cryptoClient = this.createCryptoClient(masterKey);\n\n    if (encryptedColumnEncryptionKey[0] !== this.firstVersion[0]) {\n      throw new Error(`Specified encrypted column encryption key contains an invalid encryption algorithm version ${Buffer.from([encryptedColumnEncryptionKey[0]]).toString('hex')}. Expected version is ${Buffer.from([this.firstVersion[0]]).toString('hex')}.`);\n    }\n\n    let currentIndex = this.firstVersion.length;\n    const keyPathLength: number = encryptedColumnEncryptionKey.readInt16LE(currentIndex);\n\n    currentIndex += 2;\n\n    const cipherTextLength: number = encryptedColumnEncryptionKey.readInt16LE(currentIndex);\n\n    currentIndex += 2;\n\n    currentIndex += keyPathLength;\n\n    if (cipherTextLength !== keySizeInBytes) {\n      throw new Error(`The specified encrypted column encryption key's ciphertext length: ${cipherTextLength} does not match the ciphertext length: ${keySizeInBytes} when using column master key (Azure Key Vault key) in ${masterKeyPath}. The encrypted column encryption key may be corrupt, or the specified Azure Key Vault key path may be incorrect.`);\n    }\n\n    const signatureLength: number = encryptedColumnEncryptionKey.length - currentIndex - cipherTextLength;\n\n    if (signatureLength !== keySizeInBytes) {\n      throw new Error(`The specified encrypted column encryption key's signature length: ${signatureLength} does not match the signature length: ${keySizeInBytes} when using column master key (Azure Key Vault key) in ${masterKeyPath}. The encrypted column encryption key may be corrupt, or the specified Azure Key Vault key path may be incorrect.`);\n    }\n\n    const cipherText = Buffer.alloc(cipherTextLength);\n    encryptedColumnEncryptionKey.copy(cipherText, 0, currentIndex, currentIndex + cipherTextLength);\n    currentIndex += cipherTextLength;\n\n    const signature = Buffer.alloc(signatureLength);\n    encryptedColumnEncryptionKey.copy(signature, 0, currentIndex, currentIndex + signatureLength);\n\n    const hash = Buffer.alloc(encryptedColumnEncryptionKey.length - signature.length);\n    encryptedColumnEncryptionKey.copy(hash, 0, 0, encryptedColumnEncryptionKey.length - signature.length);\n\n    const messageDigest = createHash('sha256');\n    messageDigest.update(hash);\n\n    const dataToVerify: Buffer = messageDigest.digest();\n\n    if (!dataToVerify) {\n      throw new Error('Hash should not be null while decrypting encrypted column encryption key.');\n    }\n\n    const verifyKey = await cryptoClient.verify('RS256', dataToVerify, signature);\n    if (!verifyKey.result) {\n      throw new Error(`The specified encrypted column encryption key signature does not match the signature computed with the column master key (Asymmetric key in Azure Key Vault) in ${masterKeyPath}. The encrypted column encryption key may be corrupt, or the specified path may be incorrect.`);\n    }\n\n    const decryptedCEK: Buffer = await this.azureKeyVaultUnWrap(cryptoClient, encryptionAlgorithm, cipherText);\n\n    return decryptedCEK;\n  }\n\n  async encryptColumnEncryptionKey(masterKeyPath: string, encryptionAlgorithm: string, columnEncryptionKey: Buffer): Promise<Buffer> {\n    if (!columnEncryptionKey) {\n      throw new Error('Column encryption key cannot be null.');\n    }\n\n    if (columnEncryptionKey.length === 0) {\n      throw new Error('Empty column encryption key specified.');\n    }\n\n    encryptionAlgorithm = this.validateEncryptionAlgorithm(encryptionAlgorithm);\n\n    const masterKey = await this.getMasterKey(masterKeyPath);\n\n    const keySizeInBytes = this.getAKVKeySize(masterKey);\n\n    const cryptoClient = this.createCryptoClient(masterKey);\n\n    const version = Buffer.from([this.firstVersion[0]]);\n\n    const masterKeyPathBytes: Buffer = Buffer.from(masterKeyPath.toLowerCase(), 'utf8');\n\n    const keyPathLength: Buffer = Buffer.alloc(2);\n\n    keyPathLength[0] = masterKeyPathBytes.length & 0xff;\n    keyPathLength[1] = masterKeyPathBytes.length >> 8 & 0xff;\n\n    const cipherText: Buffer = await this.azureKeyVaultWrap(cryptoClient, encryptionAlgorithm, columnEncryptionKey);\n\n    const cipherTextLength: Buffer = Buffer.alloc(2);\n\n    cipherTextLength[0] = cipherText.length & 0xff;\n    cipherTextLength[1] = cipherText.length >> 8 & 0xff;\n\n    if (cipherText.length !== keySizeInBytes) {\n      throw new Error('CipherText length does not match the RSA key size.');\n    }\n\n    const dataToHash: Buffer = Buffer.alloc(version.length + keyPathLength.length + cipherTextLength.length + masterKeyPathBytes.length + cipherText.length);\n    let destinationPosition: number = version.length;\n    version.copy(dataToHash, 0, 0, version.length);\n\n    keyPathLength.copy(dataToHash, destinationPosition, 0, keyPathLength.length);\n    destinationPosition += keyPathLength.length;\n\n    cipherTextLength.copy(dataToHash, destinationPosition, 0, cipherTextLength.length);\n    destinationPosition += cipherTextLength.length;\n\n    masterKeyPathBytes.copy(dataToHash, destinationPosition, 0, masterKeyPathBytes.length);\n    destinationPosition += masterKeyPathBytes.length;\n\n    cipherText.copy(dataToHash, destinationPosition, 0, cipherText.length);\n\n    const messageDigest = createHash('sha256');\n\n    messageDigest.update(dataToHash);\n\n    const dataToSign: Buffer = messageDigest.digest();\n\n    const signedHash: Buffer = await this.azureKeyVaultSignedHashedData(cryptoClient, dataToSign);\n    if (signedHash.length !== keySizeInBytes) {\n      throw new Error('Signed hash length does not match the RSA key size.');\n    }\n\n    const verifyKey = await cryptoClient.verify('RS256', dataToSign, signedHash);\n\n    if (!verifyKey.result) {\n      throw new Error('Invalid signature of the encrypted column encryption key computed.');\n    }\n\n    const encryptedColumnEncryptionKeyLength: number = version.length + cipherTextLength.length + keyPathLength.length + cipherText.length + masterKeyPathBytes.length + signedHash.length;\n    const encryptedColumnEncryptionKey: Buffer = Buffer.alloc(encryptedColumnEncryptionKeyLength);\n\n    let currentIndex = 0;\n    version.copy(encryptedColumnEncryptionKey, currentIndex, 0, version.length);\n    currentIndex += version.length;\n\n    keyPathLength.copy(encryptedColumnEncryptionKey, currentIndex, 0, keyPathLength.length);\n    currentIndex += keyPathLength.length;\n\n    cipherTextLength.copy(encryptedColumnEncryptionKey, currentIndex, 0, cipherTextLength.length);\n    currentIndex += cipherTextLength.length;\n\n    masterKeyPathBytes.copy(encryptedColumnEncryptionKey, currentIndex, 0, masterKeyPathBytes.length);\n    currentIndex += masterKeyPathBytes.length;\n\n    cipherText.copy(encryptedColumnEncryptionKey, currentIndex, 0, cipherText.length);\n    currentIndex += cipherText.length;\n\n    signedHash.copy(encryptedColumnEncryptionKey, currentIndex, 0, signedHash.length);\n\n    return encryptedColumnEncryptionKey;\n  }\n\n  private async getMasterKey(masterKeyPath: string): Promise<KeyVaultKey> {\n    if (!masterKeyPath) {\n      throw new Error('Master key path cannot be null or undefined');\n    }\n    const keyParts = this.parsePath(masterKeyPath);\n\n    this.createKeyClient(keyParts.vaultUrl);\n\n    return await (this.keyClient as KeyClient).getKey(keyParts.name, keyParts.version ? { version: keyParts.version } : {});\n  }\n\n  private createKeyClient(keyVaultUrl: string): void {\n    if (!keyVaultUrl) {\n      throw new Error('Cannot create key client with null or undefined keyVaultUrl');\n    }\n    if (!this.keyClient) {\n      this.url = keyVaultUrl;\n      this.keyClient = new KeyClient(keyVaultUrl, this.credentials);\n    }\n  }\n\n  private createCryptoClient(masterKey: KeyVaultKey): CryptographyClient {\n    if (!masterKey) {\n      throw new Error('Cannot create CryptographyClient with null or undefined masterKey');\n    }\n    return new CryptographyClient(masterKey, this.credentials);\n  }\n\n  private parsePath(masterKeyPath: string): ParsedKeyPath {\n    if (!masterKeyPath || masterKeyPath.trim() === '') {\n      throw new Error('Azure Key Vault key path cannot be null.');\n    }\n\n    let baseUri;\n    try {\n      baseUri = parse(masterKeyPath, true, true);\n    } catch {\n      throw new Error(`Invalid keys identifier: ${masterKeyPath}. Not a valid URI`);\n    }\n\n    if (!baseUri.hostname || !baseUri.hostname.toLowerCase().endsWith(this.azureKeyVaultDomainName)) {\n      throw new Error(`Invalid Azure Key Vault key path specified: ${masterKeyPath}.`);\n    }\n\n    // Path is of the form '/collection/name[/version]'\n    const segments = (baseUri.pathname || '').split('/');\n    if (segments.length !== 3 && segments.length !== 4) {\n      throw new Error(\n        `Invalid keys identifier: ${masterKeyPath}. Bad number of segments: ${segments.length}`\n      );\n    }\n\n    if ('keys' !== segments[1]) {\n      throw new Error(\n        `Invalid keys identifier: ${masterKeyPath}. segment [1] should be \"keys\", found \"${segments[1]}\"`\n      );\n    }\n\n    const vaultUrl = `${baseUri.protocol}//${baseUri.host}`;\n    const name = segments[2];\n    const version = segments.length === 4 ? segments[3] : undefined;\n    return {\n      vaultUrl,\n      name,\n      version\n    };\n  }\n\n  private async azureKeyVaultSignedHashedData(cryptoClient: CryptographyClient, dataToSign: Buffer): Promise<Buffer> {\n    if (!cryptoClient) {\n      throw new Error('Azure KVS Crypto Client is not defined.');\n    }\n\n    const signedData = await cryptoClient.sign('RS256', dataToSign);\n\n    return Buffer.from(signedData.result);\n  }\n\n  private async azureKeyVaultWrap(cryptoClient: CryptographyClient, encryptionAlgorithm: string, columnEncryptionKey: Buffer): Promise<Buffer> {\n    if (!cryptoClient) {\n      throw new Error('Azure KVS Crypto Client is not defined.');\n    }\n\n    if (!columnEncryptionKey) {\n      throw new Error('Column encryption key cannot be null.');\n    }\n\n    const wrappedKey = await cryptoClient.wrapKey(encryptionAlgorithm as KeyWrapAlgorithm, columnEncryptionKey);\n\n    return Buffer.from(wrappedKey.result);\n  }\n\n  private async azureKeyVaultUnWrap(cryptoClient: CryptographyClient, encryptionAlgorithm: string, encryptedColumnEncryptionKey: Buffer): Promise<Buffer> {\n    if (!cryptoClient) {\n      throw new Error('Azure KVS Crypto Client is not defined.');\n    }\n\n    if (!encryptionAlgorithm) {\n      throw new Error('Encryption Algorithm cannot be null or undefined');\n    }\n\n    if (!encryptedColumnEncryptionKey) {\n      throw new Error('Encrypted column encryption key cannot be null.');\n    }\n\n    if (encryptedColumnEncryptionKey.length === 0) {\n      throw new Error('Encrypted Column Encryption Key length should not be zero.');\n    }\n\n    const unwrappedKey = await cryptoClient.unwrapKey(encryptionAlgorithm as KeyWrapAlgorithm, encryptedColumnEncryptionKey);\n\n    return Buffer.from(unwrappedKey.result);\n  }\n\n  private getAKVKeySize(retrievedKey: KeyVaultKey): number {\n    if (!retrievedKey) {\n      throw new Error('Retrieved key cannot be null or undefined');\n    }\n    const key = retrievedKey.key;\n\n    if (!key) {\n      throw new Error(`Key does not exist ${retrievedKey.name}`);\n    }\n\n    const kty: string | undefined = key && key.kty && key.kty.toString().toUpperCase();\n\n    if (!kty || 'RSA'.localeCompare(kty, 'en') !== 0) {\n      throw new Error(`Cannot use a non-RSA key: ${kty}.`);\n    }\n\n    const keyLength = key && key.n && key.n.length;\n\n    return keyLength || 0;\n  }\n\n  private validateEncryptionAlgorithm(encryptionAlgorithm: string): string {\n    if (!encryptionAlgorithm) {\n      throw new Error('Key encryption algorithm cannot be null.');\n    }\n\n    if ('RSA_OAEP'.localeCompare(encryptionAlgorithm.toUpperCase(), 'en') === 0) {\n      encryptionAlgorithm = 'RSA-OAEP';\n    }\n\n    if (this.rsaEncryptionAlgorithmWithOAEPForAKV.localeCompare(encryptionAlgorithm.trim().toUpperCase(), 'en') !== 0) {\n      throw new Error(`Invalid key encryption algorithm specified: ${encryptionAlgorithm}. Expected value: ${this.rsaEncryptionAlgorithmWithOAEPForAKV}.`);\n    }\n\n    return encryptionAlgorithm;\n  }\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,IAAA,GAAAH,OAAA;AANA;AACA;;AAaO,MAAMI,qCAAqC,CAAC;EASjDC,WAAWA,CAACC,QAAgB,EAAEC,SAAiB,EAAEC,QAAgB,EAAE;IACjE,IAAI,CAACC,IAAI,GAAG,iBAAiB;IAC7B,IAAI,CAACC,uBAAuB,GAAG,iBAAiB;IAChD,IAAI,CAACC,oCAAoC,GAAG,UAAU;IACtD,IAAI,CAACC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IACvC,IAAI,CAACC,WAAW,GAAG,IAAIC,gCAAsB,CAACR,QAAQ,EAAEF,QAAQ,EAAEC,SAAS,CAAC;EAC9E;EAEA,MAAMU,0BAA0BA,CAACC,aAAqB,EAAEC,mBAA2B,EAAEC,4BAAoC,EAAmB;IAC1I,IAAI,CAACA,4BAA4B,EAAE;MACjC,MAAM,IAAIC,KAAK,CAAC,iEAAiE,CAAC;IACpF;IAEA,IAAID,4BAA4B,CAACE,MAAM,KAAK,CAAC,EAAE;MAC7C,MAAM,IAAID,KAAK,CAAC,kEAAkE,CAAC;IACrF;IAEAF,mBAAmB,GAAG,IAAI,CAACI,2BAA2B,CAACJ,mBAAmB,CAAC;IAE3E,MAAMK,SAAS,GAAG,MAAM,IAAI,CAACC,YAAY,CAACP,aAAa,CAAC;IAExD,MAAMQ,cAAc,GAAG,IAAI,CAACC,aAAa,CAACH,SAAS,CAAC;IAEpD,MAAMI,YAAY,GAAG,IAAI,CAACC,kBAAkB,CAACL,SAAS,CAAC;IAEvD,IAAIJ,4BAA4B,CAAC,CAAC,CAAC,KAAK,IAAI,CAACR,YAAY,CAAC,CAAC,CAAC,EAAE;MAC5D,MAAM,IAAIS,KAAK,CAAE,8FAA6FR,MAAM,CAACC,IAAI,CAAC,CAACM,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC,CAACU,QAAQ,CAAC,KAAK,CAAE,yBAAwBjB,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAACF,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAACkB,QAAQ,CAAC,KAAK,CAAE,GAAE,CAAC;IAC9P;IAEA,IAAIC,YAAY,GAAG,IAAI,CAACnB,YAAY,CAACU,MAAM;IAC3C,MAAMU,aAAqB,GAAGZ,4BAA4B,CAACa,WAAW,CAACF,YAAY,CAAC;IAEpFA,YAAY,IAAI,CAAC;IAEjB,MAAMG,gBAAwB,GAAGd,4BAA4B,CAACa,WAAW,CAACF,YAAY,CAAC;IAEvFA,YAAY,IAAI,CAAC;IAEjBA,YAAY,IAAIC,aAAa;IAE7B,IAAIE,gBAAgB,KAAKR,cAAc,EAAE;MACvC,MAAM,IAAIL,KAAK,CAAE,sEAAqEa,gBAAiB,0CAAyCR,cAAe,0DAAyDR,aAAc,mHAAkH,CAAC;IAC3V;IAEA,MAAMiB,eAAuB,GAAGf,4BAA4B,CAACE,MAAM,GAAGS,YAAY,GAAGG,gBAAgB;IAErG,IAAIC,eAAe,KAAKT,cAAc,EAAE;MACtC,MAAM,IAAIL,KAAK,CAAE,qEAAoEc,eAAgB,yCAAwCT,cAAe,0DAAyDR,aAAc,mHAAkH,CAAC;IACxV;IAEA,MAAMkB,UAAU,GAAGvB,MAAM,CAACwB,KAAK,CAACH,gBAAgB,CAAC;IACjDd,4BAA4B,CAACkB,IAAI,CAACF,UAAU,EAAE,CAAC,EAAEL,YAAY,EAAEA,YAAY,GAAGG,gBAAgB,CAAC;IAC/FH,YAAY,IAAIG,gBAAgB;IAEhC,MAAMK,SAAS,GAAG1B,MAAM,CAACwB,KAAK,CAACF,eAAe,CAAC;IAC/Cf,4BAA4B,CAACkB,IAAI,CAACC,SAAS,EAAE,CAAC,EAAER,YAAY,EAAEA,YAAY,GAAGI,eAAe,CAAC;IAE7F,MAAMK,IAAI,GAAG3B,MAAM,CAACwB,KAAK,CAACjB,4BAA4B,CAACE,MAAM,GAAGiB,SAAS,CAACjB,MAAM,CAAC;IACjFF,4BAA4B,CAACkB,IAAI,CAACE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAEpB,4BAA4B,CAACE,MAAM,GAAGiB,SAAS,CAACjB,MAAM,CAAC;IAErG,MAAMmB,aAAa,GAAG,IAAAC,kBAAU,EAAC,QAAQ,CAAC;IAC1CD,aAAa,CAACE,MAAM,CAACH,IAAI,CAAC;IAE1B,MAAMI,YAAoB,GAAGH,aAAa,CAACI,MAAM,CAAC,CAAC;IAEnD,IAAI,CAACD,YAAY,EAAE;MACjB,MAAM,IAAIvB,KAAK,CAAC,2EAA2E,CAAC;IAC9F;IAEA,MAAMyB,SAAS,GAAG,MAAMlB,YAAY,CAACmB,MAAM,CAAC,OAAO,EAAEH,YAAY,EAAEL,SAAS,CAAC;IAC7E,IAAI,CAACO,SAAS,CAACE,MAAM,EAAE;MACrB,MAAM,IAAI3B,KAAK,CAAE,mKAAkKH,aAAc,+FAA8F,CAAC;IAClS;IAEA,MAAM+B,YAAoB,GAAG,MAAM,IAAI,CAACC,mBAAmB,CAACtB,YAAY,EAAET,mBAAmB,EAAEiB,UAAU,CAAC;IAE1G,OAAOa,YAAY;EACrB;EAEA,MAAME,0BAA0BA,CAACjC,aAAqB,EAAEC,mBAA2B,EAAEiC,mBAA2B,EAAmB;IACjI,IAAI,CAACA,mBAAmB,EAAE;MACxB,MAAM,IAAI/B,KAAK,CAAC,uCAAuC,CAAC;IAC1D;IAEA,IAAI+B,mBAAmB,CAAC9B,MAAM,KAAK,CAAC,EAAE;MACpC,MAAM,IAAID,KAAK,CAAC,wCAAwC,CAAC;IAC3D;IAEAF,mBAAmB,GAAG,IAAI,CAACI,2BAA2B,CAACJ,mBAAmB,CAAC;IAE3E,MAAMK,SAAS,GAAG,MAAM,IAAI,CAACC,YAAY,CAACP,aAAa,CAAC;IAExD,MAAMQ,cAAc,GAAG,IAAI,CAACC,aAAa,CAACH,SAAS,CAAC;IAEpD,MAAMI,YAAY,GAAG,IAAI,CAACC,kBAAkB,CAACL,SAAS,CAAC;IAEvD,MAAM6B,OAAO,GAAGxC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAACF,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IAEnD,MAAM0C,kBAA0B,GAAGzC,MAAM,CAACC,IAAI,CAACI,aAAa,CAACqC,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC;IAEnF,MAAMvB,aAAqB,GAAGnB,MAAM,CAACwB,KAAK,CAAC,CAAC,CAAC;IAE7CL,aAAa,CAAC,CAAC,CAAC,GAAGsB,kBAAkB,CAAChC,MAAM,GAAG,IAAI;IACnDU,aAAa,CAAC,CAAC,CAAC,GAAGsB,kBAAkB,CAAChC,MAAM,IAAI,CAAC,GAAG,IAAI;IAExD,MAAMc,UAAkB,GAAG,MAAM,IAAI,CAACoB,iBAAiB,CAAC5B,YAAY,EAAET,mBAAmB,EAAEiC,mBAAmB,CAAC;IAE/G,MAAMlB,gBAAwB,GAAGrB,MAAM,CAACwB,KAAK,CAAC,CAAC,CAAC;IAEhDH,gBAAgB,CAAC,CAAC,CAAC,GAAGE,UAAU,CAACd,MAAM,GAAG,IAAI;IAC9CY,gBAAgB,CAAC,CAAC,CAAC,GAAGE,UAAU,CAACd,MAAM,IAAI,CAAC,GAAG,IAAI;IAEnD,IAAIc,UAAU,CAACd,MAAM,KAAKI,cAAc,EAAE;MACxC,MAAM,IAAIL,KAAK,CAAC,oDAAoD,CAAC;IACvE;IAEA,MAAMoC,UAAkB,GAAG5C,MAAM,CAACwB,KAAK,CAACgB,OAAO,CAAC/B,MAAM,GAAGU,aAAa,CAACV,MAAM,GAAGY,gBAAgB,CAACZ,MAAM,GAAGgC,kBAAkB,CAAChC,MAAM,GAAGc,UAAU,CAACd,MAAM,CAAC;IACxJ,IAAIoC,mBAA2B,GAAGL,OAAO,CAAC/B,MAAM;IAChD+B,OAAO,CAACf,IAAI,CAACmB,UAAU,EAAE,CAAC,EAAE,CAAC,EAAEJ,OAAO,CAAC/B,MAAM,CAAC;IAE9CU,aAAa,CAACM,IAAI,CAACmB,UAAU,EAAEC,mBAAmB,EAAE,CAAC,EAAE1B,aAAa,CAACV,MAAM,CAAC;IAC5EoC,mBAAmB,IAAI1B,aAAa,CAACV,MAAM;IAE3CY,gBAAgB,CAACI,IAAI,CAACmB,UAAU,EAAEC,mBAAmB,EAAE,CAAC,EAAExB,gBAAgB,CAACZ,MAAM,CAAC;IAClFoC,mBAAmB,IAAIxB,gBAAgB,CAACZ,MAAM;IAE9CgC,kBAAkB,CAAChB,IAAI,CAACmB,UAAU,EAAEC,mBAAmB,EAAE,CAAC,EAAEJ,kBAAkB,CAAChC,MAAM,CAAC;IACtFoC,mBAAmB,IAAIJ,kBAAkB,CAAChC,MAAM;IAEhDc,UAAU,CAACE,IAAI,CAACmB,UAAU,EAAEC,mBAAmB,EAAE,CAAC,EAAEtB,UAAU,CAACd,MAAM,CAAC;IAEtE,MAAMmB,aAAa,GAAG,IAAAC,kBAAU,EAAC,QAAQ,CAAC;IAE1CD,aAAa,CAACE,MAAM,CAACc,UAAU,CAAC;IAEhC,MAAME,UAAkB,GAAGlB,aAAa,CAACI,MAAM,CAAC,CAAC;IAEjD,MAAMe,UAAkB,GAAG,MAAM,IAAI,CAACC,6BAA6B,CAACjC,YAAY,EAAE+B,UAAU,CAAC;IAC7F,IAAIC,UAAU,CAACtC,MAAM,KAAKI,cAAc,EAAE;MACxC,MAAM,IAAIL,KAAK,CAAC,qDAAqD,CAAC;IACxE;IAEA,MAAMyB,SAAS,GAAG,MAAMlB,YAAY,CAACmB,MAAM,CAAC,OAAO,EAAEY,UAAU,EAAEC,UAAU,CAAC;IAE5E,IAAI,CAACd,SAAS,CAACE,MAAM,EAAE;MACrB,MAAM,IAAI3B,KAAK,CAAC,oEAAoE,CAAC;IACvF;IAEA,MAAMyC,kCAA0C,GAAGT,OAAO,CAAC/B,MAAM,GAAGY,gBAAgB,CAACZ,MAAM,GAAGU,aAAa,CAACV,MAAM,GAAGc,UAAU,CAACd,MAAM,GAAGgC,kBAAkB,CAAChC,MAAM,GAAGsC,UAAU,CAACtC,MAAM;IACtL,MAAMF,4BAAoC,GAAGP,MAAM,CAACwB,KAAK,CAACyB,kCAAkC,CAAC;IAE7F,IAAI/B,YAAY,GAAG,CAAC;IACpBsB,OAAO,CAACf,IAAI,CAAClB,4BAA4B,EAAEW,YAAY,EAAE,CAAC,EAAEsB,OAAO,CAAC/B,MAAM,CAAC;IAC3ES,YAAY,IAAIsB,OAAO,CAAC/B,MAAM;IAE9BU,aAAa,CAACM,IAAI,CAAClB,4BAA4B,EAAEW,YAAY,EAAE,CAAC,EAAEC,aAAa,CAACV,MAAM,CAAC;IACvFS,YAAY,IAAIC,aAAa,CAACV,MAAM;IAEpCY,gBAAgB,CAACI,IAAI,CAAClB,4BAA4B,EAAEW,YAAY,EAAE,CAAC,EAAEG,gBAAgB,CAACZ,MAAM,CAAC;IAC7FS,YAAY,IAAIG,gBAAgB,CAACZ,MAAM;IAEvCgC,kBAAkB,CAAChB,IAAI,CAAClB,4BAA4B,EAAEW,YAAY,EAAE,CAAC,EAAEuB,kBAAkB,CAAChC,MAAM,CAAC;IACjGS,YAAY,IAAIuB,kBAAkB,CAAChC,MAAM;IAEzCc,UAAU,CAACE,IAAI,CAAClB,4BAA4B,EAAEW,YAAY,EAAE,CAAC,EAAEK,UAAU,CAACd,MAAM,CAAC;IACjFS,YAAY,IAAIK,UAAU,CAACd,MAAM;IAEjCsC,UAAU,CAACtB,IAAI,CAAClB,4BAA4B,EAAEW,YAAY,EAAE,CAAC,EAAE6B,UAAU,CAACtC,MAAM,CAAC;IAEjF,OAAOF,4BAA4B;EACrC;EAEA,MAAcK,YAAYA,CAACP,aAAqB,EAAwB;IACtE,IAAI,CAACA,aAAa,EAAE;MAClB,MAAM,IAAIG,KAAK,CAAC,6CAA6C,CAAC;IAChE;IACA,MAAM0C,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAC9C,aAAa,CAAC;IAE9C,IAAI,CAAC+C,eAAe,CAACF,QAAQ,CAACG,QAAQ,CAAC;IAEvC,OAAO,MAAO,IAAI,CAACC,SAAS,CAAeC,MAAM,CAACL,QAAQ,CAACtD,IAAI,EAAEsD,QAAQ,CAACV,OAAO,GAAG;MAAEA,OAAO,EAAEU,QAAQ,CAACV;IAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;EACzH;EAEQY,eAAeA,CAACI,WAAmB,EAAQ;IACjD,IAAI,CAACA,WAAW,EAAE;MAChB,MAAM,IAAIhD,KAAK,CAAC,6DAA6D,CAAC;IAChF;IACA,IAAI,CAAC,IAAI,CAAC8C,SAAS,EAAE;MACnB,IAAI,CAACG,GAAG,GAAGD,WAAW;MACtB,IAAI,CAACF,SAAS,GAAG,IAAII,uBAAS,CAACF,WAAW,EAAE,IAAI,CAACtD,WAAW,CAAC;IAC/D;EACF;EAEQc,kBAAkBA,CAACL,SAAsB,EAAsB;IACrE,IAAI,CAACA,SAAS,EAAE;MACd,MAAM,IAAIH,KAAK,CAAC,mEAAmE,CAAC;IACtF;IACA,OAAO,IAAImD,gCAAkB,CAAChD,SAAS,EAAE,IAAI,CAACT,WAAW,CAAC;EAC5D;EAEQiD,SAASA,CAAC9C,aAAqB,EAAiB;IACtD,IAAI,CAACA,aAAa,IAAIA,aAAa,CAACuD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjD,MAAM,IAAIpD,KAAK,CAAC,0CAA0C,CAAC;IAC7D;IAEA,IAAIqD,OAAO;IACX,IAAI;MACFA,OAAO,GAAG,IAAAC,UAAK,EAACzD,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC;IAC5C,CAAC,CAAC,MAAM;MACN,MAAM,IAAIG,KAAK,CAAE,4BAA2BH,aAAc,mBAAkB,CAAC;IAC/E;IAEA,IAAI,CAACwD,OAAO,CAACE,QAAQ,IAAI,CAACF,OAAO,CAACE,QAAQ,CAACrB,WAAW,CAAC,CAAC,CAACsB,QAAQ,CAAC,IAAI,CAACnE,uBAAuB,CAAC,EAAE;MAC/F,MAAM,IAAIW,KAAK,CAAE,+CAA8CH,aAAc,GAAE,CAAC;IAClF;;IAEA;IACA,MAAM4D,QAAQ,GAAG,CAACJ,OAAO,CAACK,QAAQ,IAAI,EAAE,EAAEC,KAAK,CAAC,GAAG,CAAC;IACpD,IAAIF,QAAQ,CAACxD,MAAM,KAAK,CAAC,IAAIwD,QAAQ,CAACxD,MAAM,KAAK,CAAC,EAAE;MAClD,MAAM,IAAID,KAAK,CACZ,4BAA2BH,aAAc,6BAA4B4D,QAAQ,CAACxD,MAAO,EACxF,CAAC;IACH;IAEA,IAAI,MAAM,KAAKwD,QAAQ,CAAC,CAAC,CAAC,EAAE;MAC1B,MAAM,IAAIzD,KAAK,CACZ,4BAA2BH,aAAc,0CAAyC4D,QAAQ,CAAC,CAAC,CAAE,GACjG,CAAC;IACH;IAEA,MAAMZ,QAAQ,GAAI,GAAEQ,OAAO,CAACO,QAAS,KAAIP,OAAO,CAACQ,IAAK,EAAC;IACvD,MAAMzE,IAAI,GAAGqE,QAAQ,CAAC,CAAC,CAAC;IACxB,MAAMzB,OAAO,GAAGyB,QAAQ,CAACxD,MAAM,KAAK,CAAC,GAAGwD,QAAQ,CAAC,CAAC,CAAC,GAAGK,SAAS;IAC/D,OAAO;MACLjB,QAAQ;MACRzD,IAAI;MACJ4C;IACF,CAAC;EACH;EAEA,MAAcQ,6BAA6BA,CAACjC,YAAgC,EAAE+B,UAAkB,EAAmB;IACjH,IAAI,CAAC/B,YAAY,EAAE;MACjB,MAAM,IAAIP,KAAK,CAAC,yCAAyC,CAAC;IAC5D;IAEA,MAAM+D,UAAU,GAAG,MAAMxD,YAAY,CAACyD,IAAI,CAAC,OAAO,EAAE1B,UAAU,CAAC;IAE/D,OAAO9C,MAAM,CAACC,IAAI,CAACsE,UAAU,CAACpC,MAAM,CAAC;EACvC;EAEA,MAAcQ,iBAAiBA,CAAC5B,YAAgC,EAAET,mBAA2B,EAAEiC,mBAA2B,EAAmB;IAC3I,IAAI,CAACxB,YAAY,EAAE;MACjB,MAAM,IAAIP,KAAK,CAAC,yCAAyC,CAAC;IAC5D;IAEA,IAAI,CAAC+B,mBAAmB,EAAE;MACxB,MAAM,IAAI/B,KAAK,CAAC,uCAAuC,CAAC;IAC1D;IAEA,MAAMiE,UAAU,GAAG,MAAM1D,YAAY,CAAC2D,OAAO,CAACpE,mBAAmB,EAAsBiC,mBAAmB,CAAC;IAE3G,OAAOvC,MAAM,CAACC,IAAI,CAACwE,UAAU,CAACtC,MAAM,CAAC;EACvC;EAEA,MAAcE,mBAAmBA,CAACtB,YAAgC,EAAET,mBAA2B,EAAEC,4BAAoC,EAAmB;IACtJ,IAAI,CAACQ,YAAY,EAAE;MACjB,MAAM,IAAIP,KAAK,CAAC,yCAAyC,CAAC;IAC5D;IAEA,IAAI,CAACF,mBAAmB,EAAE;MACxB,MAAM,IAAIE,KAAK,CAAC,kDAAkD,CAAC;IACrE;IAEA,IAAI,CAACD,4BAA4B,EAAE;MACjC,MAAM,IAAIC,KAAK,CAAC,iDAAiD,CAAC;IACpE;IAEA,IAAID,4BAA4B,CAACE,MAAM,KAAK,CAAC,EAAE;MAC7C,MAAM,IAAID,KAAK,CAAC,4DAA4D,CAAC;IAC/E;IAEA,MAAMmE,YAAY,GAAG,MAAM5D,YAAY,CAAC6D,SAAS,CAACtE,mBAAmB,EAAsBC,4BAA4B,CAAC;IAExH,OAAOP,MAAM,CAACC,IAAI,CAAC0E,YAAY,CAACxC,MAAM,CAAC;EACzC;EAEQrB,aAAaA,CAAC+D,YAAyB,EAAU;IACvD,IAAI,CAACA,YAAY,EAAE;MACjB,MAAM,IAAIrE,KAAK,CAAC,2CAA2C,CAAC;IAC9D;IACA,MAAMsE,GAAG,GAAGD,YAAY,CAACC,GAAG;IAE5B,IAAI,CAACA,GAAG,EAAE;MACR,MAAM,IAAItE,KAAK,CAAE,sBAAqBqE,YAAY,CAACjF,IAAK,EAAC,CAAC;IAC5D;IAEA,MAAMmF,GAAuB,GAAGD,GAAG,IAAIA,GAAG,CAACC,GAAG,IAAID,GAAG,CAACC,GAAG,CAAC9D,QAAQ,CAAC,CAAC,CAAC+D,WAAW,CAAC,CAAC;IAElF,IAAI,CAACD,GAAG,IAAI,KAAK,CAACE,aAAa,CAACF,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;MAChD,MAAM,IAAIvE,KAAK,CAAE,6BAA4BuE,GAAI,GAAE,CAAC;IACtD;IAEA,MAAMG,SAAS,GAAGJ,GAAG,IAAIA,GAAG,CAACK,CAAC,IAAIL,GAAG,CAACK,CAAC,CAAC1E,MAAM;IAE9C,OAAOyE,SAAS,IAAI,CAAC;EACvB;EAEQxE,2BAA2BA,CAACJ,mBAA2B,EAAU;IACvE,IAAI,CAACA,mBAAmB,EAAE;MACxB,MAAM,IAAIE,KAAK,CAAC,0CAA0C,CAAC;IAC7D;IAEA,IAAI,UAAU,CAACyE,aAAa,CAAC3E,mBAAmB,CAAC0E,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;MAC3E1E,mBAAmB,GAAG,UAAU;IAClC;IAEA,IAAI,IAAI,CAACR,oCAAoC,CAACmF,aAAa,CAAC3E,mBAAmB,CAACsD,IAAI,CAAC,CAAC,CAACoB,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;MACjH,MAAM,IAAIxE,KAAK,CAAE,+CAA8CF,mBAAoB,qBAAoB,IAAI,CAACR,oCAAqC,GAAE,CAAC;IACtJ;IAEA,OAAOQ,mBAAmB;EAC5B;AACF;AAAC8E,OAAA,CAAA7F,qCAAA,GAAAA,qCAAA"}
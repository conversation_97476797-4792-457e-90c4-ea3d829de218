{"version": 3, "file": "decimal.js", "names": ["_decimaln", "_interopRequireDefault", "require", "_writableTrackingBuffer", "obj", "__esModule", "default", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "Decimal", "id", "type", "name", "declaration", "parameter", "resolvePrecision", "resolveScale", "precision", "value", "scale", "generateTypeInfo", "_options", "DecimalN", "generateParameterLength", "options", "generateParameterData", "sign", "Math", "round", "abs", "pow", "buffer", "alloc", "writeUInt8", "writeUInt32LE", "WritableTrackingBuffer", "writeUInt64LE", "data", "validate", "parseFloat", "isNaN", "TypeError", "_default", "exports", "module"], "sources": ["../../src/data-types/decimal.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport DecimalN from './decimaln';\nimport WritableTrackingBuffer from '../tracking-buffer/writable-tracking-buffer';\n\nconst NULL_LENGTH = Buffer.from([0x00]);\n\nconst Decimal: DataType & { resolvePrecision: NonNullable<DataType['resolvePrecision']>, resolveScale: NonNullable<DataType['resolveScale']> } = {\n  id: 0x37,\n  type: 'DECIMAL',\n  name: 'Decimal',\n\n  declaration: function(parameter) {\n    return 'decimal(' + (this.resolvePrecision(parameter)) + ', ' + (this.resolveScale(parameter)) + ')';\n  },\n\n  resolvePrecision: function(parameter) {\n    if (parameter.precision != null) {\n      return parameter.precision;\n    } else if (parameter.value === null) {\n      return 1;\n    } else {\n      return 18;\n    }\n  },\n\n  resolveScale: function(parameter) {\n    if (parameter.scale != null) {\n      return parameter.scale;\n    } else {\n      return 0;\n    }\n  },\n\n  generateTypeInfo(parameter, _options) {\n    let precision;\n    if (parameter.precision! <= 9) {\n      precision = 0x05;\n    } else if (parameter.precision! <= 19) {\n      precision = 0x09;\n    } else if (parameter.precision! <= 28) {\n      precision = 0x0D;\n    } else {\n      precision = 0x11;\n    }\n\n    return Buffer.from([DecimalN.id, precision, parameter.precision!, parameter.scale!]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    const precision = parameter.precision!;\n    if (precision <= 9) {\n      return Buffer.from([0x05]);\n    } else if (precision <= 19) {\n      return Buffer.from([0x09]);\n    } else if (precision <= 28) {\n      return Buffer.from([0x0D]);\n    } else {\n      return Buffer.from([0x11]);\n    }\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const sign = parameter.value < 0 ? 0 : 1;\n    const value = Math.round(Math.abs(parameter.value * Math.pow(10, parameter.scale!)));\n    const precision = parameter.precision!;\n    if (precision <= 9) {\n      const buffer = Buffer.alloc(5);\n      buffer.writeUInt8(sign, 0);\n      buffer.writeUInt32LE(value, 1);\n      yield buffer;\n    } else if (precision <= 19) {\n      const buffer = new WritableTrackingBuffer(9);\n      buffer.writeUInt8(sign);\n      buffer.writeUInt64LE(value);\n      yield buffer.data;\n    } else if (precision <= 28) {\n      const buffer = new WritableTrackingBuffer(13);\n      buffer.writeUInt8(sign);\n      buffer.writeUInt64LE(value);\n      buffer.writeUInt32LE(0x00000000);\n      yield buffer.data;\n    } else {\n      const buffer = new WritableTrackingBuffer(17);\n      buffer.writeUInt8(sign);\n      buffer.writeUInt64LE(value);\n      buffer.writeUInt32LE(0x00000000);\n      buffer.writeUInt32LE(0x00000000);\n      yield buffer.data;\n    }\n  },\n\n  validate: function(value): number | null {\n    if (value == null) {\n      return null;\n    }\n    value = parseFloat(value);\n    if (isNaN(value)) {\n      throw new TypeError('Invalid number.');\n    }\n    return value;\n  }\n};\n\nexport default Decimal;\nmodule.exports = Decimal;\n"], "mappings": ";;;;;;AACA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAiF,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEjF,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAMC,OAAwI,GAAG;EAC/IC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EAEfC,WAAW,EAAE,SAAAA,CAASC,SAAS,EAAE;IAC/B,OAAO,UAAU,GAAI,IAAI,CAACC,gBAAgB,CAACD,SAAS,CAAE,GAAG,IAAI,GAAI,IAAI,CAACE,YAAY,CAACF,SAAS,CAAE,GAAG,GAAG;EACtG,CAAC;EAEDC,gBAAgB,EAAE,SAAAA,CAASD,SAAS,EAAE;IACpC,IAAIA,SAAS,CAACG,SAAS,IAAI,IAAI,EAAE;MAC/B,OAAOH,SAAS,CAACG,SAAS;IAC5B,CAAC,MAAM,IAAIH,SAAS,CAACI,KAAK,KAAK,IAAI,EAAE;MACnC,OAAO,CAAC;IACV,CAAC,MAAM;MACL,OAAO,EAAE;IACX;EACF,CAAC;EAEDF,YAAY,EAAE,SAAAA,CAASF,SAAS,EAAE;IAChC,IAAIA,SAAS,CAACK,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOL,SAAS,CAACK,KAAK;IACxB,CAAC,MAAM;MACL,OAAO,CAAC;IACV;EACF,CAAC;EAEDC,gBAAgBA,CAACN,SAAS,EAAEO,QAAQ,EAAE;IACpC,IAAIJ,SAAS;IACb,IAAIH,SAAS,CAACG,SAAS,IAAK,CAAC,EAAE;MAC7BA,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM,IAAIH,SAAS,CAACG,SAAS,IAAK,EAAE,EAAE;MACrCA,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM,IAAIH,SAAS,CAACG,SAAS,IAAK,EAAE,EAAE;MACrCA,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM;MACLA,SAAS,GAAG,IAAI;IAClB;IAEA,OAAOV,MAAM,CAACC,IAAI,CAAC,CAACc,iBAAQ,CAACZ,EAAE,EAAEO,SAAS,EAAEH,SAAS,CAACG,SAAS,EAAGH,SAAS,CAACK,KAAK,CAAE,CAAC;EACtF,CAAC;EAEDI,uBAAuBA,CAACT,SAAS,EAAEU,OAAO,EAAE;IAC1C,IAAIV,SAAS,CAACI,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOZ,WAAW;IACpB;IAEA,MAAMW,SAAS,GAAGH,SAAS,CAACG,SAAU;IACtC,IAAIA,SAAS,IAAI,CAAC,EAAE;MAClB,OAAOV,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC,MAAM,IAAIS,SAAS,IAAI,EAAE,EAAE;MAC1B,OAAOV,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC,MAAM,IAAIS,SAAS,IAAI,EAAE,EAAE;MAC1B,OAAOV,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC,MAAM;MACL,OAAOD,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,CAAEiB,qBAAqBA,CAACX,SAAS,EAAEU,OAAO,EAAE;IAC1C,IAAIV,SAAS,CAACI,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMQ,IAAI,GAAGZ,SAAS,CAACI,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACxC,MAAMA,KAAK,GAAGS,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACf,SAAS,CAACI,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEhB,SAAS,CAACK,KAAM,CAAC,CAAC,CAAC;IACpF,MAAMF,SAAS,GAAGH,SAAS,CAACG,SAAU;IACtC,IAAIA,SAAS,IAAI,CAAC,EAAE;MAClB,MAAMc,MAAM,GAAGxB,MAAM,CAACyB,KAAK,CAAC,CAAC,CAAC;MAC9BD,MAAM,CAACE,UAAU,CAACP,IAAI,EAAE,CAAC,CAAC;MAC1BK,MAAM,CAACG,aAAa,CAAChB,KAAK,EAAE,CAAC,CAAC;MAC9B,MAAMa,MAAM;IACd,CAAC,MAAM,IAAId,SAAS,IAAI,EAAE,EAAE;MAC1B,MAAMc,MAAM,GAAG,IAAII,+BAAsB,CAAC,CAAC,CAAC;MAC5CJ,MAAM,CAACE,UAAU,CAACP,IAAI,CAAC;MACvBK,MAAM,CAACK,aAAa,CAAClB,KAAK,CAAC;MAC3B,MAAMa,MAAM,CAACM,IAAI;IACnB,CAAC,MAAM,IAAIpB,SAAS,IAAI,EAAE,EAAE;MAC1B,MAAMc,MAAM,GAAG,IAAII,+BAAsB,CAAC,EAAE,CAAC;MAC7CJ,MAAM,CAACE,UAAU,CAACP,IAAI,CAAC;MACvBK,MAAM,CAACK,aAAa,CAAClB,KAAK,CAAC;MAC3Ba,MAAM,CAACG,aAAa,CAAC,UAAU,CAAC;MAChC,MAAMH,MAAM,CAACM,IAAI;IACnB,CAAC,MAAM;MACL,MAAMN,MAAM,GAAG,IAAII,+BAAsB,CAAC,EAAE,CAAC;MAC7CJ,MAAM,CAACE,UAAU,CAACP,IAAI,CAAC;MACvBK,MAAM,CAACK,aAAa,CAAClB,KAAK,CAAC;MAC3Ba,MAAM,CAACG,aAAa,CAAC,UAAU,CAAC;MAChCH,MAAM,CAACG,aAAa,CAAC,UAAU,CAAC;MAChC,MAAMH,MAAM,CAACM,IAAI;IACnB;EACF,CAAC;EAEDC,QAAQ,EAAE,SAAAA,CAASpB,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACAA,KAAK,GAAGqB,UAAU,CAACrB,KAAK,CAAC;IACzB,IAAIsB,KAAK,CAACtB,KAAK,CAAC,EAAE;MAChB,MAAM,IAAIuB,SAAS,CAAC,iBAAiB,CAAC;IACxC;IACA,OAAOvB,KAAK;EACd;AACF,CAAC;AAAC,IAAAwB,QAAA,GAAAC,OAAA,CAAAtC,OAAA,GAEaI,OAAO;AACtBmC,MAAM,CAACD,OAAO,GAAGlC,OAAO"}
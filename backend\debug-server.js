console.log('🚀 Starting debug server...');

const express = require('express');
const app = express();
const PORT = 5003;

console.log('✅ Express loaded');

// Basic middleware
app.use(express.json());
app.use(express.static('../'));

console.log('✅ Middleware configured');

// Test routes
app.get('/test', (req, res) => {
    console.log('📝 Test route called');
    res.json({ success: true, message: 'Debug server working!' });
});

app.get('/api/health', (req, res) => {
    console.log('📝 Health route called');
    res.json({ success: true, message: 'Health check OK' });
});

// Start server
app.listen(PORT, () => {
    console.log(`✅ Debug server running on port ${PORT}`);
    console.log(`🔗 Test URL: http://localhost:${PORT}/test`);
    console.log(`🔗 Health URL: http://localhost:${PORT}/api/health`);
});

console.log('🔄 Server setup complete, waiting for connections...');

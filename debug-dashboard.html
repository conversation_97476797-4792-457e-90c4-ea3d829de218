<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug RC Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>RC Dashboard Debug Tool</h1>
    
    <div class="debug-section">
        <h2>API Tests</h2>
        <button onclick="testHealth()">Test Health</button>
        <button onclick="testLocations()">Test Locations</button>
        <button onclick="testStats()">Test Stats</button>
        <button onclick="testRCPending()">Test RC Pending</button>
        <div id="apiResults"></div>
    </div>

    <div class="debug-section">
        <h2>Console Logs</h2>
        <div id="consoleLogs"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        const resultsDiv = document.getElementById('apiResults');
        const logsDiv = document.getElementById('consoleLogs');

        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog('info', args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog('error', args.join(' '));
        };

        function addLog(type, message) {
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function addResult(title, data, isError = false) {
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                <h3 class="${isError ? 'error' : 'success'}">${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        async function testHealth() {
            try {
                console.log('Testing health endpoint...');
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                addResult('Health Check', data);
            } catch (error) {
                console.error('Health test failed:', error);
                addResult('Health Check Error', error.message, true);
            }
        }

        async function testLocations() {
            try {
                console.log('Testing locations endpoint...');
                const response = await fetch(`${API_BASE}/locations`);
                const data = await response.json();
                addResult('Locations', data);
            } catch (error) {
                console.error('Locations test failed:', error);
                addResult('Locations Error', error.message, true);
            }
        }

        async function testStats() {
            try {
                console.log('Testing stats endpoint...');
                const response = await fetch(`${API_BASE}/dashboard-stats`);
                const data = await response.json();
                addResult('Dashboard Stats', data);
            } catch (error) {
                console.error('Stats test failed:', error);
                addResult('Stats Error', error.message, true);
            }
        }

        async function testRCPending() {
            try {
                console.log('Testing RC pending endpoint...');
                const response = await fetch(`${API_BASE}/rc-pending`);
                const data = await response.json();
                console.log(`Received ${data.data ? data.data.length : 0} records`);
                addResult('RC Pending Data', {
                    success: data.success,
                    recordCount: data.data ? data.data.length : 0,
                    timestamp: data.timestamp,
                    firstRecord: data.data && data.data.length > 0 ? data.data[0] : null
                });
            } catch (error) {
                console.error('RC Pending test failed:', error);
                addResult('RC Pending Error', error.message, true);
            }
        }

        // Auto-run tests on load
        window.addEventListener('load', () => {
            console.log('Debug dashboard loaded');
            setTimeout(() => {
                testHealth();
                setTimeout(() => testStats(), 1000);
                setTimeout(() => testRCPending(), 2000);
            }, 500);
        });
    </script>
</body>
</html>

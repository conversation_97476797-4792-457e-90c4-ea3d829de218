@echo off
echo ========================================
echo    OK Quantity Fix Test
echo ========================================
echo.

echo [1/2] Testing backend API...
curl -s http://localhost:5001/api/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Backend is NOT running
    echo Please run: npm start (in backend folder)
    pause
    exit
)

echo ✅ Backend is running!

echo [2/2] Opening test pages...
echo.
echo Opening OK Qty analysis page...
start check-okqty.html

timeout /t 2 /nobreak >nul

echo Opening main dashboard...
start simple-dashboard.html

echo.
echo ========================================
echo    Check These in the Dashboard:
echo ========================================
echo.
echo 1. ✅ OK Qty should now be DIFFERENT from WO Qty
echo 2. ✅ OK Qty should be 30-80%% of WO Qty (realistic production)
echo 3. ✅ Green color for OK Qty indicates production progress
echo.
echo In the OK Qty Analysis page:
echo 1. Click "Check OK Quantities" button
echo 2. Look at the summary - should show mixed values
echo 3. Green rows = has production data
echo 4. Red rows = no production data (if any)
echo.
echo Expected behavior:
echo - WO Qty: Original work order quantity
echo - OK Qty: Actual produced quantity (should be less than WO Qty for pending RCs)
echo - Difference: Remaining quantity to produce
echo.
echo Press any key to close...
pause >nul

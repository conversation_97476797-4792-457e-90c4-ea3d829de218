@echo off
echo ========================================
echo    RC Dashboard Connection Test
echo ========================================
echo.

echo [1/3] Testing Backend API Connection...
curl -s http://localhost:5001/api/health
if %errorlevel% neq 0 (
    echo ERROR: Backend API not responding on port 5001
    echo Make sure the backend server is running
) else (
    echo SUCCESS: Backend API is responding
)

echo.
echo [2/3] Testing Frontend Server...
curl -s -I http://localhost:5174
if %errorlevel% neq 0 (
    echo ERROR: Frontend server not responding on port 5174
    echo Make sure the frontend server is running
) else (
    echo SUCCESS: Frontend server is responding
)

echo.
echo [3/3] Testing Database Connection...
curl -s http://localhost:5001/api/locations
if %errorlevel% neq 0 (
    echo ERROR: Database connection failed
    echo Check SQL Server connection and credentials
) else (
    echo SUCCESS: Database connection working
)

echo.
echo ========================================
echo    Connection Test Complete
echo ========================================
echo.
echo Press any key to close this window...
pause >nul

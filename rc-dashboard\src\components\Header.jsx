import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Menu, 
  Search, 
  Bell, 
  MapPin, 
  Calendar,
  Filter,
  RefreshCw,
  Download
} from 'lucide-react';
import { 
  Select, 
  MenuItem, 
  FormControl, 
  InputLabel,
  IconButton,
  Badge,
  Tooltip,
  TextField,
  InputAdornment
} from '@mui/material';

const Header = ({ selectedLocation, onLocationChange, onSidebarToggle, sidebarOpen }) => {
  const [locations, setLocations] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [lastRefresh, setLastRefresh] = useState(new Date());
  const [notifications, setNotifications] = useState(3);

  useEffect(() => {
    // Fetch locations from API
    fetchLocations();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      setLastRefresh(new Date());
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const fetchLocations = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/locations');
      const data = await response.json();
      if (data.success) {
        setLocations(data.data);
      }
    } catch (error) {
      console.error('Error fetching locations:', error);
      // Mock data for development
      setLocations([
        { LocationID: 'LOC001' },
        { LocationID: 'LOC002' },
        { LocationID: 'LOC003' },
        { LocationID: 'MAIN' },
        { LocationID: 'WAREHOUSE' }
      ]);
    }
  };

  const handleRefresh = () => {
    setLastRefresh(new Date());
    // Trigger data refresh in parent components
    window.dispatchEvent(new CustomEvent('refreshData'));
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="bg-white shadow-sm border-b border-gray-200 px-6 py-4"
    >
      <div className="flex items-center justify-between">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          <motion.button
            onClick={onSidebarToggle}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors lg:hidden"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Menu className="w-5 h-5 text-gray-600" />
          </motion.button>

          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <MapPin className="w-5 h-5 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">
                Route Card Dashboard
              </h2>
            </div>
            
            <div className="hidden md:flex items-center space-x-2 text-sm text-gray-500">
              <Calendar className="w-4 h-4" />
              <span>{new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}</span>
            </div>
          </div>
        </div>

        {/* Center Section - Search and Filters */}
        <div className="hidden lg:flex items-center space-x-4 flex-1 max-w-2xl mx-8">
          <TextField
            size="small"
            placeholder="Search work orders, parts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search className="w-4 h-4 text-gray-400" />
                </InputAdornment>
              ),
            }}
            className="flex-1"
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '12px',
                backgroundColor: '#f8fafc',
                '&:hover': {
                  backgroundColor: '#f1f5f9',
                },
                '&.Mui-focused': {
                  backgroundColor: '#ffffff',
                },
              },
            }}
          />

          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Location</InputLabel>
            <Select
              value={selectedLocation}
              onChange={(e) => onLocationChange(e.target.value)}
              label="Location"
              sx={{
                borderRadius: '12px',
                backgroundColor: '#f8fafc',
                '&:hover': {
                  backgroundColor: '#f1f5f9',
                },
              }}
            >
              <MenuItem value="">
                <em>All Locations</em>
              </MenuItem>
              {locations.map((location) => (
                <MenuItem key={location.LocationID} value={location.LocationID}>
                  {location.LocationID}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-3">
          <div className="hidden md:flex items-center space-x-2 text-sm text-gray-500 bg-gray-50 px-3 py-2 rounded-lg">
            <RefreshCw className="w-4 h-4" />
            <span>Last updated: {formatTime(lastRefresh)}</span>
          </div>

          <Tooltip title="Refresh Data">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <IconButton
                onClick={handleRefresh}
                className="text-gray-600 hover:text-blue-600"
                size="small"
              >
                <RefreshCw className="w-5 h-5" />
              </IconButton>
            </motion.div>
          </Tooltip>

          <Tooltip title="Export Data">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <IconButton
                className="text-gray-600 hover:text-green-600"
                size="small"
              >
                <Download className="w-5 h-5" />
              </IconButton>
            </motion.div>
          </Tooltip>

          <Tooltip title="Notifications">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <IconButton
                className="text-gray-600 hover:text-orange-600"
                size="small"
              >
                <Badge badgeContent={notifications} color="error">
                  <Bell className="w-5 h-5" />
                </Badge>
              </IconButton>
            </motion.div>
          </Tooltip>

          <Tooltip title="Advanced Filters">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <IconButton
                className="text-gray-600 hover:text-purple-600"
                size="small"
              >
                <Filter className="w-5 h-5" />
              </IconButton>
            </motion.div>
          </Tooltip>
        </div>
      </div>

      {/* Mobile Search Bar */}
      <motion.div
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: 1, height: 'auto' }}
        className="lg:hidden mt-4"
      >
        <TextField
          size="small"
          placeholder="Search work orders, parts..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search className="w-4 h-4 text-gray-400" />
              </InputAdornment>
            ),
          }}
          fullWidth
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: '12px',
              backgroundColor: '#f8fafc',
            },
          }}
        />
      </motion.div>
    </motion.header>
  );
};

export default Header;

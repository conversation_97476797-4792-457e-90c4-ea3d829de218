{"version": 3, "file": "checkInsecureConnection.js", "sourceRoot": "", "sources": ["../../../../src/policies/auth/checkInsecureConnection.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAEtC,2CAA2C;AAC3C,IAAI,iCAAiC,GAAG,KAAK,CAAC;AAE9C;;;;;;;GAOG;AACH,SAAS,uBAAuB,CAC9B,OAAwB,EACxB,OAA8C;IAE9C,IAAI,OAAO,CAAC,uBAAuB,IAAI,OAAO,CAAC,uBAAuB,EAAE,CAAC;QACvE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,GAAG,CAAC,QAAQ,KAAK,WAAW,IAAI,GAAG,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;YACjE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;GAIG;AACH,SAAS,6BAA6B;IACpC,MAAM,OAAO,GAAG,gFAAgF,CAAC;IAEjG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAExB,IAAI,OAAO,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,CAAA,KAAK,UAAU,IAAI,CAAC,iCAAiC,EAAE,CAAC;QACrF,iCAAiC,GAAG,IAAI,CAAC;QACzC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,sBAAsB,CACpC,OAAwB,EACxB,OAA8C;IAE9C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;QACtD,IAAI,uBAAuB,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC;YAC9C,6BAA6B,EAAE,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CACb,+GAA+G,CAChH,CAAC;QACJ,CAAC;IACH,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineRequest } from \"../../interfaces.js\";\nimport { logger } from \"../../log.js\";\n\n// Ensure the warining is only emitted once\nlet insecureConnectionWarningEmmitted = false;\n\n/**\n * Checks if the request is allowed to be sent over an insecure connection.\n *\n * A request is allowed to be sent over an insecure connection when:\n * - The `allowInsecureConnection` option is set to `true`.\n * - The request has the `allowInsecureConnection` property set to `true`.\n * - The request is being sent to `localhost` or `127.0.0.1`\n */\nfunction allowInsecureConnection(\n  request: PipelineRequest,\n  options: { allowInsecureConnection?: boolean },\n): boolean {\n  if (options.allowInsecureConnection && request.allowInsecureConnection) {\n    const url = new URL(request.url);\n    if (url.hostname === \"localhost\" || url.hostname === \"127.0.0.1\") {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Logs a warning about sending a token over an insecure connection.\n *\n * This function will emit a node warning once, but log the warning every time.\n */\nfunction emitInsecureConnectionWarning(): void {\n  const warning = \"Sending token over insecure transport. Assume any token issued is compromised.\";\n\n  logger.warning(warning);\n\n  if (typeof process?.emitWarning === \"function\" && !insecureConnectionWarningEmmitted) {\n    insecureConnectionWarningEmmitted = true;\n    process.emitWarning(warning);\n  }\n}\n\n/**\n * Ensures that authentication is only allowed over HTTPS unless explicitly allowed.\n * Throws an error if the connection is not secure and not explicitly allowed.\n */\nexport function ensureSecureConnection(\n  request: PipelineRequest,\n  options: { allowInsecureConnection?: boolean },\n): void {\n  if (!request.url.toLowerCase().startsWith(\"https://\")) {\n    if (allowInsecureConnection(request, options)) {\n      emitInsecureConnectionWarning();\n    } else {\n      throw new Error(\n        \"Authentication is not permitted for non-TLS protected (non-https) URLs when allowInsecureConnection is false.\",\n      );\n    }\n  }\n}\n"]}
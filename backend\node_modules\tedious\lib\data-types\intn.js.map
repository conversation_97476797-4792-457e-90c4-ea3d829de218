{"version": 3, "file": "intn.js", "names": ["IntN", "id", "type", "name", "declaration", "Error", "generateTypeInfo", "generateParameterLength", "generateParameterData", "validate", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/intn.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\n\nconst IntN: DataType = {\n  id: 0x26,\n  type: 'INTN',\n  name: 'IntN',\n\n  declaration() {\n    throw new Error('not implemented');\n  },\n\n  generateTypeInfo() {\n    throw new Error('not implemented');\n  },\n\n  generateParameterLength() {\n    throw new Error('not implemented');\n  },\n\n  generateParameterData() {\n    throw new Error('not implemented');\n  },\n\n  validate() {\n    throw new Error('not implemented');\n  }\n};\n\nexport default IntN;\nmodule.exports = IntN;\n"], "mappings": ";;;;;;AAEA,MAAMA,IAAc,GAAG;EACrBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EAEZC,WAAWA,CAAA,EAAG;IACZ,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,MAAM,IAAID,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDE,uBAAuBA,CAAA,EAAG;IACxB,MAAM,IAAIF,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDG,qBAAqBA,CAAA,EAAG;IACtB,MAAM,IAAIH,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDI,QAAQA,CAAA,EAAG;IACT,MAAM,IAAIJ,KAAK,CAAC,iBAAiB,CAAC;EACpC;AACF,CAAC;AAAC,IAAAK,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEaZ,IAAI;AACnBa,MAAM,CAACF,OAAO,GAAGX,IAAI"}
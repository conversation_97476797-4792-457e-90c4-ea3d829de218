@echo off
echo ========================================
echo    RC Dashboard Simple Startup
echo ========================================
echo.

echo [1/4] Stopping any existing processes...
taskkill /f /im node.exe >nul 2>&1

echo [2/4] Starting Backend Server...
start "RC Dashboard Backend" cmd /k "cd /d backend && npm start"

echo [3/4] Waiting for backend to be ready...
:wait_loop
timeout /t 3 /nobreak >nul
curl -s http://localhost:5001/api/health >nul 2>&1
if %errorlevel% neq 0 (
    echo Backend starting, please wait...
    goto wait_loop
)

echo [4/4] Backend is ready! Opening HTML Dashboard...
timeout /t 1 /nobreak >nul

echo.
echo ========================================
echo    Dashboard Ready!
echo ========================================
echo.
echo Backend API: http://localhost:5001
echo Dashboard:   simple-dashboard.html
echo.

REM Open the HTML dashboard
start simple-dashboard.html

echo HTML Dashboard opened successfully!
echo.
echo The dashboard should now be working in your browser.
echo If you see any connection errors, wait a few more seconds
echo and refresh the page.
echo.
echo Press any key to close this window...
pause >nul

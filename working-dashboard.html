<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RC Dashboard - Working Version</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo h1 {
            color: #2c3e50;
            font-size: 2rem;
            font-weight: bold;
        }

        .controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        select {
            padding: 0.5rem 1rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        .pending { color: #e74c3c; }
        .days-15 { color: #27ae60; }
        .days-45 { color: #f39c12; }
        .days-90 { color: #e67e22; }
        .days-over { color: #c0392b; }

        .table-container {
            margin: 2rem;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            padding: 1rem 2rem;
            background: #34495e;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-content {
            max-height: 600px;
            overflow-y: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            font-size: 1.2rem;
            color: #666;
        }

        .error {
            text-align: center;
            padding: 3rem;
            font-size: 1.2rem;
            color: #e74c3c;
            background: #fdf2f2;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .refresh-btn:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">
            <h1>🏭 RC DASHBOARD</h1>
        </div>
        <div class="controls">
            <select id="locationFilter">
                <option value="">All Locations</option>
            </select>
            <button class="refresh-btn" onclick="loadData()">🔄 Refresh</button>
        </div>
    </div>

    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-number pending" id="totalPending">-</div>
            <div class="stat-label">Total Pending RCs</div>
        </div>
        <div class="stat-card">
            <div class="stat-number days-15" id="within15Days">-</div>
            <div class="stat-label">≤ 15 Days</div>
        </div>
        <div class="stat-card">
            <div class="stat-number days-45" id="between16And45Days">-</div>
            <div class="stat-label">16-45 Days</div>
        </div>
        <div class="stat-card">
            <div class="stat-number days-90" id="between46And90Days">-</div>
            <div class="stat-label">46-90 Days</div>
        </div>
        <div class="stat-card">
            <div class="stat-number days-over" id="over90Days">-</div>
            <div class="stat-label">> 90 Days</div>
        </div>
    </div>

    <div class="table-container">
        <div class="table-header">
            <h2>Recent Route Cards</h2>
            <span id="recordCount">Loading...</span>
        </div>
        <div class="table-content" id="tableContent">
            <div class="loading">Loading data...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        
        // Load locations
        async function loadLocations() {
            try {
                const response = await fetch(`${API_BASE}/locations`);
                const data = await response.json();
                
                const select = document.getElementById('locationFilter');
                select.innerHTML = '<option value="">All Locations</option>';
                
                if (data.success && data.data) {
                    data.data.forEach(location => {
                        const option = document.createElement('option');
                        option.value = location.LocationID;
                        option.textContent = location.LocationName;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading locations:', error);
            }
        }

        // Load statistics
        async function loadStats() {
            try {
                const locationId = document.getElementById('locationFilter').value;
                const url = `${API_BASE}/dashboard-stats?locationId=${locationId || '%'}`;
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success && data.data) {
                    const stats = data.data;
                    document.getElementById('totalPending').textContent = stats.TotalPending || 0;
                    document.getElementById('within15Days').textContent = stats.Within15Days || 0;
                    document.getElementById('between16And45Days').textContent = stats.Between16And45Days || 0;
                    document.getElementById('between46And90Days').textContent = stats.Between46And90Days || 0;
                    document.getElementById('over90Days').textContent = stats.Over90Days || 0;
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Load table data
        async function loadTableData() {
            try {
                console.log('Loading table data...');
                const locationId = document.getElementById('locationFilter').value;
                const url = `${API_BASE}/rc-pending?locationId=${locationId || '%'}`;
                
                console.log('Fetching from:', url);
                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Received data:', data);
                
                const tableContent = document.getElementById('tableContent');
                const recordCount = document.getElementById('recordCount');
                
                if (!data.success || !data.data || data.data.length === 0) {
                    tableContent.innerHTML = '<div class="loading">No data found</div>';
                    recordCount.textContent = '0 records';
                    return;
                }
                
                console.log(`Processing ${data.data.length} records`);
                recordCount.textContent = `${data.data.length} records`;
                
                // Create table
                const table = document.createElement('table');
                table.innerHTML = `
                    <thead>
                        <tr>
                            <th>Work Order</th>
                            <th>Part & Product</th>
                            <th>WO Date</th>
                            <th>Status</th>
                            <th>WO Qty</th>
                            <th>OK Qty</th>
                            <th>Rej Qty</th>
                            <th>Desp Qty</th>
                            <th>Aging</th>
                            <th>Location</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.data.slice(0, 100).map(item => `
                            <tr>
                                <td><strong style="color: #3498db;">${item.WoNo || 'N/A'}</strong></td>
                                <td>
                                    <div style="font-weight: bold; font-size: 14px;">
                                        ${(item.PartNoAndProductName || 'Unknown').split(' | ')[1] || 'Unknown'}
                                    </div>
                                    <div style="font-size: 12px; color: #666;">
                                        ${(item.PartNoAndProductName || 'Unknown').split(' | ')[0] || 'Unknown'}
                                    </div>
                                </td>
                                <td>${item.WODate || 'N/A'}</td>
                                <td><span class="status-pending">${item.Status || 'RC Pending'}</span></td>
                                <td style="font-weight: bold;">${(item.WO_Qty || 0).toLocaleString()}</td>
                                <td style="color: #27ae60; font-weight: bold;">${(item.TotalOkQty || 0).toLocaleString()}</td>
                                <td style="color: #e74c3c; font-weight: bold;">${(item.TotalRejQty || 0).toLocaleString()}</td>
                                <td style="color: #f39c12; font-weight: bold;">${(item.DespQty || 0).toLocaleString()}</td>
                                <td style="color: ${getAgingColor(item.AgingInDays)}; font-weight: bold;">${item.AgingInDays || 0}d</td>
                                <td>
                                    <div style="font-weight: bold; font-size: 12px;">
                                        ${item.Location || 'Unknown'}
                                    </div>
                                    <div style="font-size: 10px; color: #666;">
                                        ID: ${item.LocationID || 'N/A'}
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                `;
                
                tableContent.innerHTML = '';
                tableContent.appendChild(table);
                console.log('Table rendered successfully');
                
            } catch (error) {
                console.error('Error loading table data:', error);
                document.getElementById('tableContent').innerHTML = `
                    <div class="error">
                        Error loading data: ${error.message}
                        <br><small>Check console (F12) for details</small>
                    </div>
                `;
            }
        }

        function getAgingColor(days) {
            if (days <= 15) return '#27ae60';
            if (days <= 45) return '#f39c12';
            if (days <= 90) return '#e67e22';
            return '#c0392b';
        }

        // Load all data
        async function loadData() {
            console.log('Loading all data...');
            await Promise.all([loadStats(), loadTableData()]);
        }

        // Event listeners
        document.getElementById('locationFilter').addEventListener('change', loadData);

        // Initial load
        window.addEventListener('load', async () => {
            console.log('Dashboard loading...');
            try {
                await loadLocations();
                await loadData();
                console.log('Dashboard loaded successfully');
            } catch (error) {
                console.error('Dashboard loading failed:', error);
            }
        });

        // Auto-refresh every 5 minutes
        setInterval(() => {
            console.log('Auto-refreshing...');
            loadData();
        }, 5 * 60 * 1000);
    </script>
</body>
</html>

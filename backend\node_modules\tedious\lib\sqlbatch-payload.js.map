{"version": 3, "file": "sqlbatch-payload.js", "names": ["_writableTrackingBuffer", "_interopRequireDefault", "require", "_allHeaders", "obj", "__esModule", "default", "SqlBatchPayload", "constructor", "sqlText", "txnDescriptor", "options", "Symbol", "iterator", "tdsVersion", "buffer", "WritableTrackingBuffer", "outstandingRequestCount", "writeToTrackingBuffer", "data", "<PERSON><PERSON><PERSON>", "from", "toString", "indent", "_default", "exports", "module"], "sources": ["../src/sqlbatch-payload.ts"], "sourcesContent": ["import WritableTrackingBuffer from './tracking-buffer/writable-tracking-buffer';\nimport { writeToTrackingBuffer } from './all-headers';\n\n/*\n  s2.2.6.6\n */\nclass SqlBatchPayload implements Iterable<Buffer> {\n  declare sqlText: string;\n  declare txnDescriptor: Buffer;\n  declare options: { tdsVersion: string };\n\n  constructor(sqlText: string, txnDescriptor: Buffer, options: { tdsVersion: string }) {\n    this.sqlText = sqlText;\n    this.txnDescriptor = txnDescriptor;\n    this.options = options;\n  }\n\n  *[Symbol.iterator]() {\n    if (this.options.tdsVersion >= '7_2') {\n      const buffer = new WritableTrackingBuffer(18, 'ucs2');\n      const outstandingRequestCount = 1;\n\n      writeToTrackingBuffer(buffer, this.txnDescriptor, outstandingRequestCount);\n\n      yield buffer.data;\n    }\n\n    yield Buffer.from(this.sqlText, 'ucs2');\n  }\n\n  toString(indent = '') {\n    return indent + ('SQL Batch - ' + this.sqlText);\n  }\n}\n\nexport default SqlBatchPayload;\nmodule.exports = SqlBatchPayload;\n"], "mappings": ";;;;;;AAAA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAAsD,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEtD;AACA;AACA;AACA,MAAMG,eAAe,CAA6B;EAKhDC,WAAWA,CAACC,OAAe,EAAEC,aAAqB,EAAEC,OAA+B,EAAE;IACnF,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,OAAO,GAAGA,OAAO;EACxB;EAEA,EAAEC,MAAM,CAACC,QAAQ,IAAI;IACnB,IAAI,IAAI,CAACF,OAAO,CAACG,UAAU,IAAI,KAAK,EAAE;MACpC,MAAMC,MAAM,GAAG,IAAIC,+BAAsB,CAAC,EAAE,EAAE,MAAM,CAAC;MACrD,MAAMC,uBAAuB,GAAG,CAAC;MAEjC,IAAAC,iCAAqB,EAACH,MAAM,EAAE,IAAI,CAACL,aAAa,EAAEO,uBAAuB,CAAC;MAE1E,MAAMF,MAAM,CAACI,IAAI;IACnB;IAEA,MAAMC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACZ,OAAO,EAAE,MAAM,CAAC;EACzC;EAEAa,QAAQA,CAACC,MAAM,GAAG,EAAE,EAAE;IACpB,OAAOA,MAAM,IAAI,cAAc,GAAG,IAAI,CAACd,OAAO,CAAC;EACjD;AACF;AAAC,IAAAe,QAAA,GAAAC,OAAA,CAAAnB,OAAA,GAEcC,eAAe;AAC9BmB,MAAM,CAACD,OAAO,GAAGlB,eAAe"}
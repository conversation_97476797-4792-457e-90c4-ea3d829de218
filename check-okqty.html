<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check OK Qty</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 12px;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .zero { background: #ffebee; }
        .nonzero { background: #e8f5e8; }
        .summary {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 OK Quantity Analysis</h1>
    
    <button onclick="checkOkQty()">Check OK Quantities</button>
    
    <div id="summary" class="summary" style="display: none;">
        <h3>Summary</h3>
        <div id="summaryContent"></div>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        
        async function checkOkQty() {
            const results = document.getElementById('results');
            const summary = document.getElementById('summary');
            const summaryContent = document.getElementById('summaryContent');
            
            results.innerHTML = '<div style="text-align: center; padding: 20px;">Loading data...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/rc-pending`);
                const data = await response.json();
                
                if (data.success && data.data) {
                    const records = data.data.slice(0, 20); // Show first 20 records
                    
                    // Analyze the data
                    const zeroOkQty = data.data.filter(item => (item.TotalOkQty || 0) === 0);
                    const nonZeroOkQty = data.data.filter(item => (item.TotalOkQty || 0) > 0);
                    const sameAsWoQty = data.data.filter(item => (item.TotalOkQty || 0) === (item.WO_Qty || 0));
                    
                    // Show summary
                    summaryContent.innerHTML = `
                        <p><strong>Total Records:</strong> ${data.data.length}</p>
                        <p><strong>Records with OK Qty = 0:</strong> ${zeroOkQty.length}</p>
                        <p><strong>Records with OK Qty > 0:</strong> ${nonZeroOkQty.length}</p>
                        <p><strong>Records where OK Qty = WO Qty:</strong> ${sameAsWoQty.length}</p>
                        ${zeroOkQty.length === data.data.length ? 
                            '<p style="color: red;"><strong>⚠️ Issue:</strong> All OK quantities are 0 - production data may not be linked properly</p>' : 
                            '<p style="color: green;"><strong>✅ Good:</strong> Some production data is available</p>'
                        }
                    `;
                    summary.style.display = 'block';
                    
                    // Create table
                    const table = `
                        <table>
                            <thead>
                                <tr>
                                    <th>Work Order</th>
                                    <th>Product</th>
                                    <th>WO Qty</th>
                                    <th>OK Qty</th>
                                    <th>Difference</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${records.map(item => {
                                    const woQty = item.WO_Qty || 0;
                                    const okQty = item.TotalOkQty || 0;
                                    const diff = woQty - okQty;
                                    const rowClass = okQty === 0 ? 'zero' : 'nonzero';
                                    
                                    return `
                                        <tr class="${rowClass}">
                                            <td><strong>${item.WoNo}</strong></td>
                                            <td>${item.PartNoAndProductName?.split(' | ')[1] || 'Unknown'}</td>
                                            <td>${woQty.toLocaleString()}</td>
                                            <td><strong>${okQty.toLocaleString()}</strong></td>
                                            <td>${diff.toLocaleString()}</td>
                                            <td>${okQty === 0 ? '❌ No Production' : okQty === woQty ? '✅ Complete' : '🔄 In Progress'}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                        <p style="margin-top: 15px; font-size: 12px; color: #666;">
                            <span style="background: #ffebee; padding: 2px 6px; border-radius: 3px;">Red rows</span> = OK Qty is 0 (no production data)
                            <br>
                            <span style="background: #e8f5e8; padding: 2px 6px; border-radius: 3px;">Green rows</span> = OK Qty > 0 (has production data)
                        </p>
                    `;
                    
                    results.innerHTML = table;
                    
                } else {
                    results.innerHTML = '<div style="color: red; text-align: center; padding: 20px;">Error loading data</div>';
                }
                
            } catch (error) {
                console.error('Error:', error);
                results.innerHTML = `<div style="color: red; text-align: center; padding: 20px;">Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>

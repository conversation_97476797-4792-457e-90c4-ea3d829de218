{"version": 3, "file": "msalBrowserOptions.js", "sourceRoot": "", "sources": ["../../../../src/msal/browserFlows/msalBrowserOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AuthenticationRecord } from \"../types.js\";\nimport type { BrowserLoginStyle } from \"../../credentials/interactiveBrowserCredentialOptions.js\";\nimport type { LogPolicyOptions } from \"@azure/core-rest-pipeline\";\nimport type { MultiTenantTokenCredentialOptions } from \"../../credentials/multiTenantTokenCredentialOptions.js\";\nimport type { CredentialLogger } from \"../../util/logging.js\";\n\n/**\n * Options for the MSAL browser flows.\n * @internal\n */\nexport interface MsalBrowserFlowOptions {\n  logger: CredentialLogger;\n\n  /**\n   * The Client ID of the Microsoft Entra application that users will sign into.\n   * This parameter is required on the browser.\n   */\n  clientId?: string;\n\n  /**\n   * The Microsoft Entra tenant (directory) ID.\n   */\n  tenantId?: string;\n\n  /**\n   * The authority host to use for authentication requests.\n   * Possible values are available through {@link AzureAuthorityHosts}.\n   * The default is \"https://login.microsoftonline.com\".\n   */\n  authorityHost?: string;\n\n  /**\n   * Result of a previous authentication that can be used to retrieve the cached credentials of each individual account.\n   * This is necessary to provide in case the application wants to work with more than one account per\n   * Client ID and Tenant ID pair.\n   *\n   * This record can be retrieved by calling to the credential's `authenticate()` method, as follows:\n   *\n   *     const authenticationRecord = await credential.authenticate();\n   *\n   */\n  authenticationRecord?: AuthenticationRecord;\n\n  /**\n   * Makes getToken throw if a manual authentication is necessary.\n   * Developers will need to call to `authenticate()` to control when to manually authenticate.\n   */\n  disableAutomaticAuthentication?: boolean;\n\n  /**\n   * The field determines whether instance discovery is performed when attempting to authenticate.\n   * Setting this to `true` will completely disable both instance discovery and authority validation.\n   * As a result, it's crucial to ensure that the configured authority host is valid and trustworthy.\n   * This functionality is intended for use in scenarios where the metadata endpoint cannot be reached, such as in private clouds or Azure Stack.\n   * The process of instance discovery entails retrieving authority metadata from https://login.microsoft.com/ to validate the authority.\n   */\n  disableInstanceDiscovery?: boolean;\n\n  /**\n   * Options for multi-tenant applications which allows for additionally allowed tenants.\n   */\n  tokenCredentialOptions: MultiTenantTokenCredentialOptions;\n\n  /**\n   * Gets the redirect URI of the application. This should be same as the value\n   * in the application registration portal.  Defaults to `window.location.href`.\n   * This field is no longer required for Node.js.\n   */\n  redirectUri?: string;\n\n  /**\n   * Specifies whether a redirect or a popup window should be used to\n   * initiate the user authentication flow. Possible values are \"redirect\"\n   * or \"popup\" (default) for browser and \"popup\" (default) for node.\n   *\n   */\n  loginStyle: BrowserLoginStyle;\n\n  /**\n   * loginHint allows a user name to be pre-selected for interactive logins.\n   * Setting this option skips the account selection prompt and immediately attempts to login with the specified account.\n   */\n  loginHint?: string;\n\n  /**\n   * Allows users to configure settings for logging policy options, allow logging account information and personally identifiable information for customer support.\n   */\n  loggingOptions?: LogPolicyOptions & {\n    /**\n     * Allows logging account information once the authentication flow succeeds.\n     */\n    allowLoggingAccountIdentifiers?: boolean;\n    /**\n     * Allows logging personally identifiable information for customer support.\n     */\n    enableUnsafeSupportLogging?: boolean;\n  };\n}\n"]}
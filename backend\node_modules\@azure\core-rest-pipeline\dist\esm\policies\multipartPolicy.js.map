{"version": 3, "file": "multipartPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/multipartPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EACL,mBAAmB,IAAI,sBAAsB,EAC7C,eAAe,IAAI,kBAAkB,GACtC,MAAM,6CAA6C,CAAC;AAKrD,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAE/D;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,sBAAsB,CAAC;AAE1D;;GAEG;AACH,MAAM,UAAU,eAAe;IAC7B,MAAM,SAAS,GAAG,kBAAkB,EAAE,CAAC;IAEvC,OAAO;QACL,IAAI,EAAE,mBAAmB;QACzB,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;YACnC,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;oBAC/C,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC7B,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACvC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC,WAAW,CAAC,OAA6B,EAAE,IAAsB,CAAC,CAAC;QACtF,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\nimport {\n  multipartPolicyName as tspMultipartPolicyName,\n  multipartPolicy as tspMultipartPolicy,\n} from \"@typespec/ts-http-runtime/internal/policies\";\nimport type {\n  PipelineRequest as TspPipelineRequest,\n  SendRequest as TspSendRequest,\n} from \"@typespec/ts-http-runtime\";\nimport { getRawContent, hasRawContent } from \"../util/file.js\";\n\n/**\n * Name of multipart policy\n */\nexport const multipartPolicyName = tspMultipartPolicyName;\n\n/**\n * Pipeline policy for multipart requests\n */\nexport function multipartPolicy(): PipelinePolicy {\n  const tspPolicy = tspMultipartPolicy();\n\n  return {\n    name: multipartPolicyName,\n    sendRequest: async (request, next) => {\n      if (request.multipartBody) {\n        for (const part of request.multipartBody.parts) {\n          if (hasRawContent(part.body)) {\n            part.body = getRawContent(part.body);\n          }\n        }\n      }\n\n      return tspPolicy.sendRequest(request as TspPipelineRequest, next as TspSendRequest);\n    },\n  };\n}\n"]}
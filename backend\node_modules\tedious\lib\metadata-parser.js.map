{"version": 3, "file": "metadata-parser.js", "names": ["_collation", "require", "_dataType", "_sprintfJs", "_helpers", "readCollation", "buf", "offset", "length", "NotEnoughDataError", "collation", "Collation", "fromBuffer", "slice", "Result", "readSchema", "schemaPresent", "value", "readUInt8", "undefined", "dbname", "readBVarChar", "owningSchema", "xmlSchemaCollection", "readUsVarChar", "readUDTInfo", "maxByteSize", "readUInt16LE", "typeName", "assemblyName", "readMetadata", "options", "userType", "tdsVersion", "readUInt32LE", "flags", "typeNumber", "type", "TYPE", "Error", "sprintf", "name", "precision", "scale", "dataLength", "schema", "udtInfo", "metadataParse", "parser", "callback", "result", "buffer", "position", "err", "waitForChunk", "_default", "exports", "default", "module"], "sources": ["../src/metadata-parser.ts"], "sourcesContent": ["import { Collation } from './collation';\nimport Parser, { type ParserOptions } from './token/stream-parser';\nimport { TYPE, type DataType } from './data-type';\nimport { type CryptoMetadata } from './always-encrypted/types';\n\nimport { sprintf } from 'sprintf-js';\n\nimport { Result, NotEnoughDataError, readUInt8, readBVarChar, readUsVarChar, readUInt16LE, readUInt32LE } from './token/helpers';\n\ninterface XmlSchema {\n  dbname: string;\n  owningSchema: string;\n  xmlSchemaCollection: string;\n}\n\ninterface UdtInfo {\n  maxByteSize: number;\n  dbname: string;\n  owningSchema: string;\n  typeName: string;\n  assemblyName: string;\n}\n\nexport type BaseMetadata = {\n  userType: number;\n\n  flags: number;\n  /**\n   * The column's type, such as VarChar, Int or Binary.\n   */\n  type: DataType;\n\n  collation: Collation | undefined;\n  /**\n   * The precision. Only applicable to numeric and decimal.\n   */\n  precision: number | undefined;\n\n  /**\n   * The scale. Only applicable to numeric, decimal, time, datetime2 and datetimeoffset.\n   */\n  scale: number | undefined;\n\n  /**\n   * The length, for char, varchar, nvarchar and varbinary.\n   */\n  dataLength: number | undefined;\n\n  schema: XmlSchema | undefined;\n\n  udtInfo: UdtInfo | undefined;\n}\n\nexport type Metadata = {\n  cryptoMetadata?: CryptoMetadata;\n} & BaseMetadata;\n\nfunction readCollation(buf: Buffer, offset: number): Result<Collation> {\n  offset = +offset;\n\n  if (buf.length < offset + 5) {\n    throw new NotEnoughDataError(offset + 5);\n  }\n\n  const collation = Collation.fromBuffer(buf.slice(offset, offset + 5));\n  return new Result(collation, offset + 5);\n}\n\nfunction readSchema(buf: Buffer, offset: number): Result<XmlSchema | undefined> {\n  offset = +offset;\n\n  let schemaPresent;\n  ({ offset, value: schemaPresent } = readUInt8(buf, offset));\n\n  if (schemaPresent !== 0x01) {\n    return new Result(undefined, offset);\n  }\n\n  let dbname;\n  ({ offset, value: dbname } = readBVarChar(buf, offset));\n\n  let owningSchema;\n  ({ offset, value: owningSchema } = readBVarChar(buf, offset));\n\n  let xmlSchemaCollection;\n  ({ offset, value: xmlSchemaCollection } = readUsVarChar(buf, offset));\n\n  return new Result({ dbname, owningSchema, xmlSchemaCollection }, offset);\n}\n\nfunction readUDTInfo(buf: Buffer, offset: number): Result<UdtInfo> {\n  let maxByteSize;\n  ({ offset, value: maxByteSize } = readUInt16LE(buf, offset));\n\n  let dbname;\n  ({ offset, value: dbname } = readBVarChar(buf, offset));\n\n  let owningSchema;\n  ({ offset, value: owningSchema } = readBVarChar(buf, offset));\n\n  let typeName;\n  ({ offset, value: typeName } = readBVarChar(buf, offset));\n\n  let assemblyName;\n  ({ offset, value: assemblyName } = readUsVarChar(buf, offset));\n\n  return new Result({\n    maxByteSize: maxByteSize,\n    dbname: dbname,\n    owningSchema: owningSchema,\n    typeName: typeName,\n    assemblyName: assemblyName\n  }, offset);\n}\n\nfunction readMetadata(buf: Buffer, offset: number, options: ParserOptions): Result<Metadata> {\n  let userType;\n  ({ offset, value: userType } = (options.tdsVersion < '7_2' ? readUInt16LE : readUInt32LE)(buf, offset));\n\n  let flags;\n  ({ offset, value: flags } = readUInt16LE(buf, offset));\n\n  let typeNumber;\n  ({ offset, value: typeNumber } = readUInt8(buf, offset));\n\n  const type: DataType = TYPE[typeNumber];\n  if (!type) {\n    throw new Error(sprintf('Unrecognised data type 0x%02X', typeNumber));\n  }\n\n  switch (type.name) {\n    case 'Null':\n    case 'TinyInt':\n    case 'SmallInt':\n    case 'Int':\n    case 'BigInt':\n    case 'Real':\n    case 'Float':\n    case 'SmallMoney':\n    case 'Money':\n    case 'Bit':\n    case 'SmallDateTime':\n    case 'DateTime':\n    case 'Date':\n      return new Result({\n        userType: userType,\n        flags: flags,\n        type: type,\n        collation: undefined,\n        precision: undefined,\n        scale: undefined,\n        dataLength: undefined,\n        schema: undefined,\n        udtInfo: undefined\n      }, offset);\n\n    case 'IntN':\n    case 'FloatN':\n    case 'MoneyN':\n    case 'BitN':\n    case 'UniqueIdentifier':\n    case 'DateTimeN': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt8(buf, offset));\n\n      return new Result({\n        userType: userType,\n        flags: flags,\n        type: type,\n        collation: undefined,\n        precision: undefined,\n        scale: undefined,\n        dataLength: dataLength,\n        schema: undefined,\n        udtInfo: undefined\n      }, offset);\n    }\n\n    case 'Variant': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt32LE(buf, offset));\n\n      return new Result({\n        userType: userType,\n        flags: flags,\n        type: type,\n        collation: undefined,\n        precision: undefined,\n        scale: undefined,\n        dataLength: dataLength,\n        schema: undefined,\n        udtInfo: undefined\n      }, offset);\n    }\n\n    case 'VarChar':\n    case 'Char':\n    case 'NVarChar':\n    case 'NChar': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt16LE(buf, offset));\n\n      let collation;\n      ({ offset, value: collation } = readCollation(buf, offset));\n\n      return new Result({\n        userType: userType,\n        flags: flags,\n        type: type,\n        collation: collation,\n        precision: undefined,\n        scale: undefined,\n        dataLength: dataLength,\n        schema: undefined,\n        udtInfo: undefined\n      }, offset);\n    }\n\n    case 'Text':\n    case 'NText': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt32LE(buf, offset));\n\n      let collation;\n      ({ offset, value: collation } = readCollation(buf, offset));\n\n      return new Result({\n        userType: userType,\n        flags: flags,\n        type: type,\n        collation: collation,\n        precision: undefined,\n        scale: undefined,\n        dataLength: dataLength,\n        schema: undefined,\n        udtInfo: undefined\n      }, offset);\n    }\n\n    case 'VarBinary':\n    case 'Binary': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt16LE(buf, offset));\n\n      return new Result({\n        userType: userType,\n        flags: flags,\n        type: type,\n        collation: undefined,\n        precision: undefined,\n        scale: undefined,\n        dataLength: dataLength,\n        schema: undefined,\n        udtInfo: undefined\n      }, offset);\n    }\n\n    case 'Image': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt32LE(buf, offset));\n\n      return new Result({\n        userType: userType,\n        flags: flags,\n        type: type,\n        collation: undefined,\n        precision: undefined,\n        scale: undefined,\n        dataLength: dataLength,\n        schema: undefined,\n        udtInfo: undefined\n      }, offset);\n    }\n\n    case 'Xml': {\n      let schema;\n      ({ offset, value: schema } = readSchema(buf, offset));\n\n      return new Result({\n        userType: userType,\n        flags: flags,\n        type: type,\n        collation: undefined,\n        precision: undefined,\n        scale: undefined,\n        dataLength: undefined,\n        schema: schema,\n        udtInfo: undefined\n      }, offset);\n    }\n\n    case 'Time':\n    case 'DateTime2':\n    case 'DateTimeOffset': {\n      let scale;\n      ({ offset, value: scale } = readUInt8(buf, offset));\n\n      return new Result({\n        userType: userType,\n        flags: flags,\n        type: type,\n        collation: undefined,\n        precision: undefined,\n        scale: scale,\n        dataLength: undefined,\n        schema: undefined,\n        udtInfo: undefined\n      }, offset);\n    }\n\n    case 'NumericN':\n    case 'DecimalN': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt8(buf, offset));\n\n      let precision;\n      ({ offset, value: precision } = readUInt8(buf, offset));\n\n      let scale;\n      ({ offset, value: scale } = readUInt8(buf, offset));\n\n      return new Result({\n        userType: userType,\n        flags: flags,\n        type: type,\n        collation: undefined,\n        precision: precision,\n        scale: scale,\n        dataLength: dataLength,\n        schema: undefined,\n        udtInfo: undefined\n      }, offset);\n    }\n\n    case 'UDT': {\n      let udtInfo;\n      ({ offset, value: udtInfo } = readUDTInfo(buf, offset));\n\n      return new Result({\n        userType: userType,\n        flags: flags,\n        type: type,\n        collation: undefined,\n        precision: undefined,\n        scale: undefined,\n        dataLength: undefined,\n        schema: undefined,\n        udtInfo: udtInfo\n      }, offset);\n    }\n\n    default:\n      throw new Error(sprintf('Unrecognised type %s', type.name));\n  }\n}\n\nfunction metadataParse(parser: Parser, options: ParserOptions, callback: (metadata: Metadata) => void) {\n  (async () => {\n    while (true) {\n      let result;\n      try {\n        result = readMetadata(parser.buffer, parser.position, options);\n      } catch (err: any) {\n        if (err instanceof NotEnoughDataError) {\n          await parser.waitForChunk();\n          continue;\n        }\n\n        throw err;\n      }\n\n      parser.position = result.offset;\n      return callback(result.value);\n    }\n  })();\n}\n\nexport default metadataParse;\nexport { readCollation, readMetadata };\n\nmodule.exports = metadataParse;\nmodule.exports.readCollation = readCollation;\nmodule.exports.readMetadata = readMetadata;\n"], "mappings": ";;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,SAAA,GAAAD,OAAA;AAGA,IAAAE,UAAA,GAAAF,OAAA;AAEA,IAAAG,QAAA,GAAAH,OAAA;AAkDA,SAASI,aAAaA,CAACC,GAAW,EAAEC,MAAc,EAAqB;EACrEA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAID,GAAG,CAACE,MAAM,GAAGD,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIE,2BAAkB,CAACF,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,MAAMG,SAAS,GAAGC,oBAAS,CAACC,UAAU,CAACN,GAAG,CAACO,KAAK,CAACN,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC,CAAC;EACrE,OAAO,IAAIO,eAAM,CAACJ,SAAS,EAAEH,MAAM,GAAG,CAAC,CAAC;AAC1C;AAEA,SAASQ,UAAUA,CAACT,GAAW,EAAEC,MAAc,EAAiC;EAC9EA,MAAM,GAAG,CAACA,MAAM;EAEhB,IAAIS,aAAa;EACjB,CAAC;IAAET,MAAM;IAAEU,KAAK,EAAED;EAAc,CAAC,GAAG,IAAAE,kBAAS,EAACZ,GAAG,EAAEC,MAAM,CAAC;EAE1D,IAAIS,aAAa,KAAK,IAAI,EAAE;IAC1B,OAAO,IAAIF,eAAM,CAACK,SAAS,EAAEZ,MAAM,CAAC;EACtC;EAEA,IAAIa,MAAM;EACV,CAAC;IAAEb,MAAM;IAAEU,KAAK,EAAEG;EAAO,CAAC,GAAG,IAAAC,qBAAY,EAACf,GAAG,EAAEC,MAAM,CAAC;EAEtD,IAAIe,YAAY;EAChB,CAAC;IAAEf,MAAM;IAAEU,KAAK,EAAEK;EAAa,CAAC,GAAG,IAAAD,qBAAY,EAACf,GAAG,EAAEC,MAAM,CAAC;EAE5D,IAAIgB,mBAAmB;EACvB,CAAC;IAAEhB,MAAM;IAAEU,KAAK,EAAEM;EAAoB,CAAC,GAAG,IAAAC,sBAAa,EAAClB,GAAG,EAAEC,MAAM,CAAC;EAEpE,OAAO,IAAIO,eAAM,CAAC;IAAEM,MAAM;IAAEE,YAAY;IAAEC;EAAoB,CAAC,EAAEhB,MAAM,CAAC;AAC1E;AAEA,SAASkB,WAAWA,CAACnB,GAAW,EAAEC,MAAc,EAAmB;EACjE,IAAImB,WAAW;EACf,CAAC;IAAEnB,MAAM;IAAEU,KAAK,EAAES;EAAY,CAAC,GAAG,IAAAC,qBAAY,EAACrB,GAAG,EAAEC,MAAM,CAAC;EAE3D,IAAIa,MAAM;EACV,CAAC;IAAEb,MAAM;IAAEU,KAAK,EAAEG;EAAO,CAAC,GAAG,IAAAC,qBAAY,EAACf,GAAG,EAAEC,MAAM,CAAC;EAEtD,IAAIe,YAAY;EAChB,CAAC;IAAEf,MAAM;IAAEU,KAAK,EAAEK;EAAa,CAAC,GAAG,IAAAD,qBAAY,EAACf,GAAG,EAAEC,MAAM,CAAC;EAE5D,IAAIqB,QAAQ;EACZ,CAAC;IAAErB,MAAM;IAAEU,KAAK,EAAEW;EAAS,CAAC,GAAG,IAAAP,qBAAY,EAACf,GAAG,EAAEC,MAAM,CAAC;EAExD,IAAIsB,YAAY;EAChB,CAAC;IAAEtB,MAAM;IAAEU,KAAK,EAAEY;EAAa,CAAC,GAAG,IAAAL,sBAAa,EAAClB,GAAG,EAAEC,MAAM,CAAC;EAE7D,OAAO,IAAIO,eAAM,CAAC;IAChBY,WAAW,EAAEA,WAAW;IACxBN,MAAM,EAAEA,MAAM;IACdE,YAAY,EAAEA,YAAY;IAC1BM,QAAQ,EAAEA,QAAQ;IAClBC,YAAY,EAAEA;EAChB,CAAC,EAAEtB,MAAM,CAAC;AACZ;AAEA,SAASuB,YAAYA,CAACxB,GAAW,EAAEC,MAAc,EAAEwB,OAAsB,EAAoB;EAC3F,IAAIC,QAAQ;EACZ,CAAC;IAAEzB,MAAM;IAAEU,KAAK,EAAEe;EAAS,CAAC,GAAG,CAACD,OAAO,CAACE,UAAU,GAAG,KAAK,GAAGN,qBAAY,GAAGO,qBAAY,EAAE5B,GAAG,EAAEC,MAAM,CAAC;EAEtG,IAAI4B,KAAK;EACT,CAAC;IAAE5B,MAAM;IAAEU,KAAK,EAAEkB;EAAM,CAAC,GAAG,IAAAR,qBAAY,EAACrB,GAAG,EAAEC,MAAM,CAAC;EAErD,IAAI6B,UAAU;EACd,CAAC;IAAE7B,MAAM;IAAEU,KAAK,EAAEmB;EAAW,CAAC,GAAG,IAAAlB,kBAAS,EAACZ,GAAG,EAAEC,MAAM,CAAC;EAEvD,MAAM8B,IAAc,GAAGC,cAAI,CAACF,UAAU,CAAC;EACvC,IAAI,CAACC,IAAI,EAAE;IACT,MAAM,IAAIE,KAAK,CAAC,IAAAC,kBAAO,EAAC,+BAA+B,EAAEJ,UAAU,CAAC,CAAC;EACvE;EAEA,QAAQC,IAAI,CAACI,IAAI;IACf,KAAK,MAAM;IACX,KAAK,SAAS;IACd,KAAK,UAAU;IACf,KAAK,KAAK;IACV,KAAK,QAAQ;IACb,KAAK,MAAM;IACX,KAAK,OAAO;IACZ,KAAK,YAAY;IACjB,KAAK,OAAO;IACZ,KAAK,KAAK;IACV,KAAK,eAAe;IACpB,KAAK,UAAU;IACf,KAAK,MAAM;MACT,OAAO,IAAI3B,eAAM,CAAC;QAChBkB,QAAQ,EAAEA,QAAQ;QAClBG,KAAK,EAAEA,KAAK;QACZE,IAAI,EAAEA,IAAI;QACV3B,SAAS,EAAES,SAAS;QACpBuB,SAAS,EAAEvB,SAAS;QACpBwB,KAAK,EAAExB,SAAS;QAChByB,UAAU,EAAEzB,SAAS;QACrB0B,MAAM,EAAE1B,SAAS;QACjB2B,OAAO,EAAE3B;MACX,CAAC,EAAEZ,MAAM,CAAC;IAEZ,KAAK,MAAM;IACX,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,MAAM;IACX,KAAK,kBAAkB;IACvB,KAAK,WAAW;MAAE;QAChB,IAAIqC,UAAU;QACd,CAAC;UAAErC,MAAM;UAAEU,KAAK,EAAE2B;QAAW,CAAC,GAAG,IAAA1B,kBAAS,EAACZ,GAAG,EAAEC,MAAM,CAAC;QAEvD,OAAO,IAAIO,eAAM,CAAC;UAChBkB,QAAQ,EAAEA,QAAQ;UAClBG,KAAK,EAAEA,KAAK;UACZE,IAAI,EAAEA,IAAI;UACV3B,SAAS,EAAES,SAAS;UACpBuB,SAAS,EAAEvB,SAAS;UACpBwB,KAAK,EAAExB,SAAS;UAChByB,UAAU,EAAEA,UAAU;UACtBC,MAAM,EAAE1B,SAAS;UACjB2B,OAAO,EAAE3B;QACX,CAAC,EAAEZ,MAAM,CAAC;MACZ;IAEA,KAAK,SAAS;MAAE;QACd,IAAIqC,UAAU;QACd,CAAC;UAAErC,MAAM;UAAEU,KAAK,EAAE2B;QAAW,CAAC,GAAG,IAAAV,qBAAY,EAAC5B,GAAG,EAAEC,MAAM,CAAC;QAE1D,OAAO,IAAIO,eAAM,CAAC;UAChBkB,QAAQ,EAAEA,QAAQ;UAClBG,KAAK,EAAEA,KAAK;UACZE,IAAI,EAAEA,IAAI;UACV3B,SAAS,EAAES,SAAS;UACpBuB,SAAS,EAAEvB,SAAS;UACpBwB,KAAK,EAAExB,SAAS;UAChByB,UAAU,EAAEA,UAAU;UACtBC,MAAM,EAAE1B,SAAS;UACjB2B,OAAO,EAAE3B;QACX,CAAC,EAAEZ,MAAM,CAAC;MACZ;IAEA,KAAK,SAAS;IACd,KAAK,MAAM;IACX,KAAK,UAAU;IACf,KAAK,OAAO;MAAE;QACZ,IAAIqC,UAAU;QACd,CAAC;UAAErC,MAAM;UAAEU,KAAK,EAAE2B;QAAW,CAAC,GAAG,IAAAjB,qBAAY,EAACrB,GAAG,EAAEC,MAAM,CAAC;QAE1D,IAAIG,SAAS;QACb,CAAC;UAAEH,MAAM;UAAEU,KAAK,EAAEP;QAAU,CAAC,GAAGL,aAAa,CAACC,GAAG,EAAEC,MAAM,CAAC;QAE1D,OAAO,IAAIO,eAAM,CAAC;UAChBkB,QAAQ,EAAEA,QAAQ;UAClBG,KAAK,EAAEA,KAAK;UACZE,IAAI,EAAEA,IAAI;UACV3B,SAAS,EAAEA,SAAS;UACpBgC,SAAS,EAAEvB,SAAS;UACpBwB,KAAK,EAAExB,SAAS;UAChByB,UAAU,EAAEA,UAAU;UACtBC,MAAM,EAAE1B,SAAS;UACjB2B,OAAO,EAAE3B;QACX,CAAC,EAAEZ,MAAM,CAAC;MACZ;IAEA,KAAK,MAAM;IACX,KAAK,OAAO;MAAE;QACZ,IAAIqC,UAAU;QACd,CAAC;UAAErC,MAAM;UAAEU,KAAK,EAAE2B;QAAW,CAAC,GAAG,IAAAV,qBAAY,EAAC5B,GAAG,EAAEC,MAAM,CAAC;QAE1D,IAAIG,SAAS;QACb,CAAC;UAAEH,MAAM;UAAEU,KAAK,EAAEP;QAAU,CAAC,GAAGL,aAAa,CAACC,GAAG,EAAEC,MAAM,CAAC;QAE1D,OAAO,IAAIO,eAAM,CAAC;UAChBkB,QAAQ,EAAEA,QAAQ;UAClBG,KAAK,EAAEA,KAAK;UACZE,IAAI,EAAEA,IAAI;UACV3B,SAAS,EAAEA,SAAS;UACpBgC,SAAS,EAAEvB,SAAS;UACpBwB,KAAK,EAAExB,SAAS;UAChByB,UAAU,EAAEA,UAAU;UACtBC,MAAM,EAAE1B,SAAS;UACjB2B,OAAO,EAAE3B;QACX,CAAC,EAAEZ,MAAM,CAAC;MACZ;IAEA,KAAK,WAAW;IAChB,KAAK,QAAQ;MAAE;QACb,IAAIqC,UAAU;QACd,CAAC;UAAErC,MAAM;UAAEU,KAAK,EAAE2B;QAAW,CAAC,GAAG,IAAAjB,qBAAY,EAACrB,GAAG,EAAEC,MAAM,CAAC;QAE1D,OAAO,IAAIO,eAAM,CAAC;UAChBkB,QAAQ,EAAEA,QAAQ;UAClBG,KAAK,EAAEA,KAAK;UACZE,IAAI,EAAEA,IAAI;UACV3B,SAAS,EAAES,SAAS;UACpBuB,SAAS,EAAEvB,SAAS;UACpBwB,KAAK,EAAExB,SAAS;UAChByB,UAAU,EAAEA,UAAU;UACtBC,MAAM,EAAE1B,SAAS;UACjB2B,OAAO,EAAE3B;QACX,CAAC,EAAEZ,MAAM,CAAC;MACZ;IAEA,KAAK,OAAO;MAAE;QACZ,IAAIqC,UAAU;QACd,CAAC;UAAErC,MAAM;UAAEU,KAAK,EAAE2B;QAAW,CAAC,GAAG,IAAAV,qBAAY,EAAC5B,GAAG,EAAEC,MAAM,CAAC;QAE1D,OAAO,IAAIO,eAAM,CAAC;UAChBkB,QAAQ,EAAEA,QAAQ;UAClBG,KAAK,EAAEA,KAAK;UACZE,IAAI,EAAEA,IAAI;UACV3B,SAAS,EAAES,SAAS;UACpBuB,SAAS,EAAEvB,SAAS;UACpBwB,KAAK,EAAExB,SAAS;UAChByB,UAAU,EAAEA,UAAU;UACtBC,MAAM,EAAE1B,SAAS;UACjB2B,OAAO,EAAE3B;QACX,CAAC,EAAEZ,MAAM,CAAC;MACZ;IAEA,KAAK,KAAK;MAAE;QACV,IAAIsC,MAAM;QACV,CAAC;UAAEtC,MAAM;UAAEU,KAAK,EAAE4B;QAAO,CAAC,GAAG9B,UAAU,CAACT,GAAG,EAAEC,MAAM,CAAC;QAEpD,OAAO,IAAIO,eAAM,CAAC;UAChBkB,QAAQ,EAAEA,QAAQ;UAClBG,KAAK,EAAEA,KAAK;UACZE,IAAI,EAAEA,IAAI;UACV3B,SAAS,EAAES,SAAS;UACpBuB,SAAS,EAAEvB,SAAS;UACpBwB,KAAK,EAAExB,SAAS;UAChByB,UAAU,EAAEzB,SAAS;UACrB0B,MAAM,EAAEA,MAAM;UACdC,OAAO,EAAE3B;QACX,CAAC,EAAEZ,MAAM,CAAC;MACZ;IAEA,KAAK,MAAM;IACX,KAAK,WAAW;IAChB,KAAK,gBAAgB;MAAE;QACrB,IAAIoC,KAAK;QACT,CAAC;UAAEpC,MAAM;UAAEU,KAAK,EAAE0B;QAAM,CAAC,GAAG,IAAAzB,kBAAS,EAACZ,GAAG,EAAEC,MAAM,CAAC;QAElD,OAAO,IAAIO,eAAM,CAAC;UAChBkB,QAAQ,EAAEA,QAAQ;UAClBG,KAAK,EAAEA,KAAK;UACZE,IAAI,EAAEA,IAAI;UACV3B,SAAS,EAAES,SAAS;UACpBuB,SAAS,EAAEvB,SAAS;UACpBwB,KAAK,EAAEA,KAAK;UACZC,UAAU,EAAEzB,SAAS;UACrB0B,MAAM,EAAE1B,SAAS;UACjB2B,OAAO,EAAE3B;QACX,CAAC,EAAEZ,MAAM,CAAC;MACZ;IAEA,KAAK,UAAU;IACf,KAAK,UAAU;MAAE;QACf,IAAIqC,UAAU;QACd,CAAC;UAAErC,MAAM;UAAEU,KAAK,EAAE2B;QAAW,CAAC,GAAG,IAAA1B,kBAAS,EAACZ,GAAG,EAAEC,MAAM,CAAC;QAEvD,IAAImC,SAAS;QACb,CAAC;UAAEnC,MAAM;UAAEU,KAAK,EAAEyB;QAAU,CAAC,GAAG,IAAAxB,kBAAS,EAACZ,GAAG,EAAEC,MAAM,CAAC;QAEtD,IAAIoC,KAAK;QACT,CAAC;UAAEpC,MAAM;UAAEU,KAAK,EAAE0B;QAAM,CAAC,GAAG,IAAAzB,kBAAS,EAACZ,GAAG,EAAEC,MAAM,CAAC;QAElD,OAAO,IAAIO,eAAM,CAAC;UAChBkB,QAAQ,EAAEA,QAAQ;UAClBG,KAAK,EAAEA,KAAK;UACZE,IAAI,EAAEA,IAAI;UACV3B,SAAS,EAAES,SAAS;UACpBuB,SAAS,EAAEA,SAAS;UACpBC,KAAK,EAAEA,KAAK;UACZC,UAAU,EAAEA,UAAU;UACtBC,MAAM,EAAE1B,SAAS;UACjB2B,OAAO,EAAE3B;QACX,CAAC,EAAEZ,MAAM,CAAC;MACZ;IAEA,KAAK,KAAK;MAAE;QACV,IAAIuC,OAAO;QACX,CAAC;UAAEvC,MAAM;UAAEU,KAAK,EAAE6B;QAAQ,CAAC,GAAGrB,WAAW,CAACnB,GAAG,EAAEC,MAAM,CAAC;QAEtD,OAAO,IAAIO,eAAM,CAAC;UAChBkB,QAAQ,EAAEA,QAAQ;UAClBG,KAAK,EAAEA,KAAK;UACZE,IAAI,EAAEA,IAAI;UACV3B,SAAS,EAAES,SAAS;UACpBuB,SAAS,EAAEvB,SAAS;UACpBwB,KAAK,EAAExB,SAAS;UAChByB,UAAU,EAAEzB,SAAS;UACrB0B,MAAM,EAAE1B,SAAS;UACjB2B,OAAO,EAAEA;QACX,CAAC,EAAEvC,MAAM,CAAC;MACZ;IAEA;MACE,MAAM,IAAIgC,KAAK,CAAC,IAAAC,kBAAO,EAAC,sBAAsB,EAAEH,IAAI,CAACI,IAAI,CAAC,CAAC;EAC/D;AACF;AAEA,SAASM,aAAaA,CAACC,MAAc,EAAEjB,OAAsB,EAAEkB,QAAsC,EAAE;EACrG,CAAC,YAAY;IACX,OAAO,IAAI,EAAE;MACX,IAAIC,MAAM;MACV,IAAI;QACFA,MAAM,GAAGpB,YAAY,CAACkB,MAAM,CAACG,MAAM,EAAEH,MAAM,CAACI,QAAQ,EAAErB,OAAO,CAAC;MAChE,CAAC,CAAC,OAAOsB,GAAQ,EAAE;QACjB,IAAIA,GAAG,YAAY5C,2BAAkB,EAAE;UACrC,MAAMuC,MAAM,CAACM,YAAY,CAAC,CAAC;UAC3B;QACF;QAEA,MAAMD,GAAG;MACX;MAEAL,MAAM,CAACI,QAAQ,GAAGF,MAAM,CAAC3C,MAAM;MAC/B,OAAO0C,QAAQ,CAACC,MAAM,CAACjC,KAAK,CAAC;IAC/B;EACF,CAAC,EAAE,CAAC;AACN;AAAC,IAAAsC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcV,aAAa;AAG5BW,MAAM,CAACF,OAAO,GAAGT,aAAa;AAC9BW,MAAM,CAACF,OAAO,CAACnD,aAAa,GAAGA,aAAa;AAC5CqD,MAAM,CAACF,OAAO,CAAC1B,YAAY,GAAGA,YAAY"}
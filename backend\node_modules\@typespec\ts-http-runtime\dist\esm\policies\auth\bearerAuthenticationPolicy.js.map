{"version": 3, "file": "bearerAuthenticationPolicy.js", "sourceRoot": "", "sources": ["../../../../src/policies/auth/bearerAuthenticationPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAMlC,OAAO,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;AAEtE;;GAEG;AACH,MAAM,CAAC,MAAM,8BAA8B,GAAG,4BAA4B,CAAC;AAqB3E;;GAEG;AACH,MAAM,UAAU,0BAA0B,CACxC,OAA0C;IAE1C,OAAO;QACL,IAAI,EAAE,8BAA8B;QACpC,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;;YAC3D,0FAA0F;YAC1F,sBAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAEzC,MAAM,MAAM,GAAG,MAAA,CAAC,MAAA,OAAO,CAAC,WAAW,mCAAI,OAAO,CAAC,WAAW,CAAC,0CAAE,IAAI,CAC/D,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,KAAK,QAAQ,CAClD,CAAC;YAEF,gFAAgF;YAChF,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC;gBACpD,WAAW,EAAE,OAAO,CAAC,WAAW;aACjC,CAAC,CAAC;YACH,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { BearerTokenCredential } from \"../../auth/credentials.js\";\nimport type { AuthScheme } from \"../../auth/schemes.js\";\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../../interfaces.js\";\nimport type { PipelinePolicy } from \"../../pipeline.js\";\nimport { ensureSecureConnection } from \"./checkInsecureConnection.js\";\n\n/**\n * Name of the Bearer Authentication Policy\n */\nexport const bearerAuthenticationPolicyName = \"bearerAuthenticationPolicy\";\n\n/**\n * Options for configuring the bearer authentication policy\n */\nexport interface BearerAuthenticationPolicyOptions {\n  /**\n   * The BearerTokenCredential implementation that can supply the bearer token.\n   */\n  credential: BearerTokenCredential;\n  /**\n   * Optional authentication schemes to use. If not provided, schemes from the request will be used.\n   */\n  authSchemes?: AuthScheme[];\n  /**\n   * Allows for connecting to HTTP endpoints instead of enforcing HTTPS.\n   * CAUTION: Never use this option in production.\n   */\n  allowInsecureConnection?: boolean;\n}\n\n/**\n * Gets a pipeline policy that adds bearer token authentication to requests\n */\nexport function bearerAuthenticationPolicy(\n  options: BearerAuthenticationPolicyOptions,\n): PipelinePolicy {\n  return {\n    name: bearerAuthenticationPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      // Ensure allowInsecureConnection is explicitly set when sending request to non-https URLs\n      ensureSecureConnection(request, options);\n\n      const scheme = (request.authSchemes ?? options.authSchemes)?.find(\n        (x) => x.kind === \"http\" && x.scheme === \"bearer\",\n      );\n\n      // Skip adding authentication header if no bearer authentication scheme is found\n      if (!scheme) {\n        return next(request);\n      }\n\n      const token = await options.credential.getBearerToken({\n        abortSignal: request.abortSignal,\n      });\n      request.headers.set(\"Authorization\", `Bearer ${token}`);\n      return next(request);\n    },\n  };\n}\n"]}
{"version": 3, "file": "int.js", "names": ["_intn", "_interopRequireDefault", "require", "obj", "__esModule", "default", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "DATA_LENGTH", "Int", "id", "type", "name", "declaration", "generateTypeInfo", "IntN", "generateParameterLength", "parameter", "options", "value", "generateParameterData", "buffer", "alloc", "writeInt32LE", "Number", "validate", "isNaN", "TypeError", "_default", "exports", "module"], "sources": ["../../src/data-types/int.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport IntN from './intn';\n\nconst NULL_LENGTH = Buffer.from([0x00]);\nconst DATA_LENGTH = Buffer.from([0x04]);\n\nconst Int: DataType = {\n  id: 0x38,\n  type: 'INT4',\n  name: 'Int',\n\n  declaration: function() {\n    return 'int';\n  },\n\n  generateTypeInfo() {\n    return Buffer.from([IntN.id, 0x04]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    return DATA_LENGTH;\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const buffer = Buffer.alloc(4);\n    buffer.writeInt32LE(Number(parameter.value), 0);\n    yield buffer;\n  },\n\n  validate: function(value): number | null {\n    if (value == null) {\n      return null;\n    }\n\n    if (typeof value !== 'number') {\n      value = Number(value);\n    }\n\n    if (isNaN(value)) {\n      throw new TypeError('Invalid number.');\n    }\n\n    if (value < -2147483648 || value > 2147483647) {\n      throw new TypeError('Value must be between -2147483648 and 2147483647, inclusive.');\n    }\n\n    return value | 0;\n  }\n};\n\nexport default Int;\nmodule.exports = Int;\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0B,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE1B,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,MAAMC,WAAW,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAME,GAAa,GAAG;EACpBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,KAAK;EAEXC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,KAAK;EACd,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,OAAOR,MAAM,CAACC,IAAI,CAAC,CAACQ,aAAI,CAACL,EAAE,EAAE,IAAI,CAAC,CAAC;EACrC,CAAC;EAEDM,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOd,WAAW;IACpB;IAEA,OAAOG,WAAW;EACpB,CAAC;EAED,CAAEY,qBAAqBA,CAACH,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAME,MAAM,GAAGf,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,YAAY,CAACC,MAAM,CAACP,SAAS,CAACE,KAAK,CAAC,EAAE,CAAC,CAAC;IAC/C,MAAME,MAAM;EACd,CAAC;EAEDI,QAAQ,EAAE,SAAAA,CAASN,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAGK,MAAM,CAACL,KAAK,CAAC;IACvB;IAEA,IAAIO,KAAK,CAACP,KAAK,CAAC,EAAE;MAChB,MAAM,IAAIQ,SAAS,CAAC,iBAAiB,CAAC;IACxC;IAEA,IAAIR,KAAK,GAAG,CAAC,UAAU,IAAIA,KAAK,GAAG,UAAU,EAAE;MAC7C,MAAM,IAAIQ,SAAS,CAAC,8DAA8D,CAAC;IACrF;IAEA,OAAOR,KAAK,GAAG,CAAC;EAClB;AACF,CAAC;AAAC,IAAAS,QAAA,GAAAC,OAAA,CAAAzB,OAAA,GAEaK,GAAG;AAClBqB,MAAM,CAACD,OAAO,GAAGpB,GAAG"}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 RC Dashboard API Test</h1>
    
    <div>
        <button onclick="testHealth()">Test Health API</button>
        <button onclick="testStats()">Test Stats API</button>
        <button onclick="testLocations()">Test Locations API</button>
        <button onclick="testRCData()">Test RC Data API</button>
        <button onclick="testAll()">Test All APIs</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        
        function addResult(title, success, message, data = null) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${success ? 'success' : 'error'}`;
            
            let content = `<strong>${title}:</strong> ${message}`;
            if (data) {
                content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            div.innerHTML = content;
            results.appendChild(div);
        }
        
        function addLoading(title) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = 'test-result loading';
            div.innerHTML = `<strong>${title}:</strong> Testing...`;
            div.id = `loading-${title.replace(/\s+/g, '-').toLowerCase()}`;
            results.appendChild(div);
            return div;
        }
        
        function removeLoading(id) {
            const element = document.getElementById(id);
            if (element) {
                element.remove();
            }
        }
        
        async function testAPI(endpoint, title) {
            const loadingId = `loading-${title.replace(/\s+/g, '-').toLowerCase()}`;
            const loadingDiv = addLoading(title);
            
            try {
                console.log(`Testing ${title}: ${API_BASE}${endpoint}`);
                
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                removeLoading(loadingId);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                addResult(title, true, `Success (${response.status})`, data);
                return data;
                
            } catch (error) {
                removeLoading(loadingId);
                console.error(`${title} failed:`, error);
                addResult(title, false, error.message);
                return null;
            }
        }
        
        async function testHealth() {
            await testAPI('/health', 'Health Check');
        }
        
        async function testStats() {
            await testAPI('/dashboard-stats', 'Dashboard Stats');
        }
        
        async function testLocations() {
            await testAPI('/locations', 'Locations');
        }
        
        async function testRCData() {
            await testAPI('/rc-pending', 'RC Pending Data');
        }
        
        async function testAll() {
            clearResults();
            addResult('Test All APIs', true, 'Starting comprehensive API test...');
            
            await testHealth();
            await testStats();
            await testLocations();
            await testRCData();
            
            addResult('Test Complete', true, 'All API tests completed. Check results above.');
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            addResult('Page Loaded', true, 'API test page loaded successfully. Click buttons above to test APIs.');
        });
    </script>
</body>
</html>

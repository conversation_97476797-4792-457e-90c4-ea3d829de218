@echo off
echo ========================================
echo    Opening HTML RC Dashboard
echo ========================================
echo.

echo [1/2] Checking if backend is running...
curl -s http://localhost:5001/api/health >nul 2>&1
if %errorlevel% neq 0 (
    echo Backend not running. Starting backend...
    start "RC Dashboard Backend" cmd /k "cd /d backend && npm start"
    echo Waiting for backend to start...
    timeout /t 5 /nobreak >nul
) else (
    echo Backend is already running.
)

echo [2/2] Opening HTML Dashboard...
start simple-dashboard.html

echo.
echo ========================================
echo    HTML Dashboard Opened Successfully!
echo ========================================
echo.
echo Backend API:  http://localhost:5001
echo Dashboard:    simple-dashboard.html
echo.
echo Press any key to close this window...
pause >nul

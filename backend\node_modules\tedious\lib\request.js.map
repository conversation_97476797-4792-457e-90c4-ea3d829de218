{"version": 3, "file": "request.js", "names": ["_events", "require", "_errors", "_types", "Request", "EventEmitter", "on", "event", "listener", "emit", "args", "constructor", "sqlTextOrProcedure", "callback", "options", "parameters", "parametersByName", "preparing", "handle", "undefined", "canceled", "paused", "error", "connection", "timeout", "userCallback", "statementColumnEncryptionSetting", "SQLServerStatementColumnEncryptionSetting", "UseConnectionSetting", "cryptoMetadataLoaded", "err", "rowCount", "rows", "addParameter", "name", "type", "value", "output", "length", "precision", "scale", "parameter", "push", "addOutputParameter", "makeParamsParameter", "paramsParameter", "i", "len", "declaration", "validateParameters", "collation", "validate", "RequestError", "message", "cause", "pause", "resume", "cancel", "setTimeout", "_default", "exports", "default", "module"], "sources": ["../src/request.ts"], "sourcesContent": ["import { EventEmitter } from 'events';\nimport { type Parameter, type DataType } from './data-type';\nimport { RequestError } from './errors';\n\nimport Connection from './connection';\nimport { type Metadata } from './metadata-parser';\nimport { SQLServerStatementColumnEncryptionSetting } from './always-encrypted/types';\nimport { type ColumnMetadata } from './token/colmetadata-token-parser';\nimport { Collation } from './collation';\n\n/**\n * The callback is called when the request has completed, either successfully or with an error.\n * If an error occurs during execution of the statement(s), then `err` will describe the error.\n *\n * As only one request at a time may be executed on a connection, another request should not\n * be initiated until this callback is called.\n *\n * This callback is called before `requestCompleted` is emitted.\n */\ntype CompletionCallback =\n  /**\n   * @param error\n   *   If an error occurred, an error object.\n   *\n   * @param rowCount\n   *   The number of rows emitted as result of executing the SQL statement.\n   *\n   * @param rows\n   *   Rows as a result of executing the SQL statement.\n   *   Will only be available if [[ConnectionOptions.rowCollectionOnRequestCompletion]] is `true`.\n   */\n  // TODO: Figure out how to type the `rows` parameter here.\n  (error: Error | null | undefined, rowCount?: number, rows?: any) => void;\n\nexport interface ParameterOptions {\n  output?: boolean;\n  length?: number;\n  precision?: number;\n  scale?: number;\n}\n\ninterface RequestOptions {\n  statementColumnEncryptionSetting?: SQLServerStatementColumnEncryptionSetting;\n}\n\n/**\n * ```js\n * const { Request } = require('tedious');\n * const request = new Request(\"select 42, 'hello world'\", (err, rowCount) {\n *   // Request completion callback...\n * });\n * connection.execSql(request);\n * ```\n */\nclass Request extends EventEmitter {\n  /**\n   * @private\n   */\n  declare sqlTextOrProcedure: string | undefined;\n  /**\n   * @private\n   */\n  declare parameters: Parameter[];\n  /**\n   * @private\n   */\n  declare parametersByName: { [key: string]: Parameter };\n  /**\n   * @private\n   */\n  declare preparing: boolean;\n  /**\n   * @private\n   */\n  declare canceled: boolean;\n  /**\n   * @private\n   */\n  declare paused: boolean;\n  /**\n   * @private\n   */\n  declare userCallback: CompletionCallback;\n  /**\n   * @private\n   */\n  declare handle: number | undefined;\n  /**\n   * @private\n   */\n  declare error: Error | undefined;\n  /**\n   * @private\n   */\n  declare connection: Connection | undefined;\n  /**\n   * @private\n   */\n  declare timeout: number | undefined;\n\n  /**\n   * @private\n   */\n  declare rows?: Array<any>;\n  /**\n   * @private\n   */\n  declare rst?: Array<any>;\n  /**\n   * @private\n   */\n  declare rowCount?: number;\n\n  /**\n   * @private\n   */\n  declare callback: CompletionCallback;\n\n\n  declare shouldHonorAE?: boolean;\n  declare statementColumnEncryptionSetting: SQLServerStatementColumnEncryptionSetting;\n  declare cryptoMetadataLoaded: boolean;\n\n  /**\n   * This event, describing result set columns, will be emitted before row\n   * events are emitted. This event may be emitted multiple times when more\n   * than one recordset is produced by the statement.\n   *\n   * An array like object, where the columns can be accessed either by index\n   * or name. Columns with a name that is an integer are not accessible by name,\n   * as it would be interpreted as an array index.\n   */\n  on(\n    event: 'columnMetadata',\n    listener:\n    (columns: ColumnMetadata[] | { [key: string]: ColumnMetadata }) => void\n  ): this\n\n  /**\n   * The request has been prepared and can be used in subsequent calls to execute and unprepare.\n   */\n  on(event: 'prepared', listener: () => void): this\n\n  /**\n   * The request encountered an error and has not been prepared.\n   */\n  on(event: 'error', listener: (err: Error) => void): this\n\n  /**\n   * A row resulting from execution of the SQL statement.\n   */\n  on(\n    event: 'row',\n    listener:\n      /**\n       * An array or object (depends on [[ConnectionOptions.useColumnNames]]), where the columns can be accessed by index/name.\n       * Each column has two properties, `metadata` and `value`：\n       *\n       * * `metadata`\n       *\n       *    The same data that is exposed in the `columnMetadata` event.\n       *\n       * * `value`\n       *\n       *    The column's value. It will be `null` for a `NULL`.\n       *    If there are multiple columns with the same name, then this will be an array of the values.\n       */\n      (columns: any) => void\n  ): this\n\n  /**\n   * All rows from a result set have been provided (through `row` events).\n   *\n   * This token is used to indicate the completion of a SQL statement.\n   * As multiple SQL statements can be sent to the server in a single SQL batch, multiple `done` can be generated.\n   * An `done` event is emitted for each SQL statement in the SQL batch except variable declarations.\n   * For execution of SQL statements within stored procedures, `doneProc` and `doneInProc` events are used in place of `done`.\n   *\n   * If you are using [[Connection.execSql]] then SQL server may treat the multiple calls with the same query as a stored procedure.\n   * When this occurs, the `doneProc` and `doneInProc` events may be emitted instead. You must handle both events to ensure complete coverage.\n   */\n  on(\n    event: 'done',\n    listener:\n      /**\n       * @param rowCount\n       *   The number of result rows. May be `undefined` if not available.\n       *\n       * @param more\n       *   If there are more results to come (probably because multiple statements are being executed), then `true`.\n       *\n       * @param rst\n       *   Rows as a result of executing the SQL statement.\n       *   Will only be available if Connection's [[ConnectionOptions.rowCollectionOnDone]] is `true`.\n       */\n      (rowCount: number | undefined, more: boolean, rst?: any[]) => void\n  ): this\n\n  /**\n   * `request.on('doneInProc', function (rowCount, more, rows) { });`\n   *\n   * Indicates the completion status of a SQL statement within a stored procedure. All rows from a statement\n   * in a stored procedure have been provided (through `row` events).\n   *\n   * This event may also occur when executing multiple calls with the same query using [[execSql]].\n   */\n  on(\n    event: 'doneInProc',\n    listener:\n      /**\n       * @param rowCount\n       *   The number of result rows. May be `undefined` if not available.\n       *\n       * @param more\n       *   If there are more results to come (probably because multiple statements are being executed), then `true`.\n       *\n       * @param rst\n       *   Rows as a result of executing the SQL statement.\n       *   Will only be available if Connection's [[ConnectionOptions.rowCollectionOnDone]] is `true`.\n       */\n      (rowCount: number | undefined, more: boolean, rst?: any[]) => void\n  ): this\n\n  /**\n   * Indicates the completion status of a stored procedure. This is also generated for stored procedures\n   * executed through SQL statements.\\\n   * This event may also occur when executing multiple calls with the same query using [[execSql]].\n   */\n  on(\n    event: 'doneProc',\n    listener:\n      /**\n       * @param rowCount\n       *   The number of result rows. May be `undefined` if not available.\n       *\n       * @param more\n       *   If there are more results to come (probably because multiple statements are being executed), then `true`.\n       *\n       * @param rst\n       *   Rows as a result of executing the SQL statement.\n       *   Will only be available if Connection's [[ConnectionOptions.rowCollectionOnDone]] is `true`.\n       */\n      (rowCount: number | undefined, more: boolean, procReturnStatusValue: number, rst?: any[]) => void\n  ): this\n\n  /**\n   * A value for an output parameter (that was added to the request with [[addOutputParameter]]).\n   * See also `Using Parameters`.\n   */\n  on(\n    event: 'returnValue',\n    listener:\n      /**\n       * @param parameterName\n       *   The parameter name. (Does not start with '@'.)\n       *\n       * @param value\n       *   The parameter's output value.\n       *\n       * @param metadata\n       *   The same data that is exposed in the `columnMetaData` event.\n       */\n      (parameterName: string, value: unknown, metadata: Metadata) => void\n  ): this\n\n  /**\n   * This event gives the columns by which data is ordered, if `ORDER BY` clause is executed in SQL Server.\n   */\n  on(\n    event: 'order',\n    listener:\n      /**\n       * @param orderColumns\n       *   An array of column numbers in the result set by which data is ordered.\n       */\n      (orderColumns: number[]) => void\n  ): this\n\n  on(event: 'requestCompleted', listener: () => void): this\n\n  on(event: 'cancel', listener: () => void): this\n\n  on(event: 'pause', listener: () => void): this\n\n  on(event: 'resume', listener: () => void): this\n\n  on(event: string | symbol, listener: (...args: any[]) => void) {\n    return super.on(event, listener);\n  }\n\n  /**\n   * @private\n   */\n  emit(event: 'columnMetadata', columns: ColumnMetadata[] | { [key: string]: ColumnMetadata }): boolean\n  /**\n   * @private\n   */\n  emit(event: 'prepared'): boolean\n  /**\n   * @private\n   */\n  emit(event: 'error', err: Error): boolean\n  /**\n   * @private\n   */\n  emit(event: 'row', columns: any): boolean\n  /**\n   * @private\n   */\n  emit(event: 'done', rowCount: number | undefined, more: boolean, rst?: any[]): boolean\n  /**\n   * @private\n   */\n  emit(event: 'doneInProc', rowCount: number | undefined, more: boolean, rst?: any[]): boolean\n  /**\n   * @private\n   */\n  emit(event: 'doneProc', rowCount: number | undefined, more: boolean, procReturnStatusValue: number, rst?: any[]): boolean\n  /**\n   * @private\n   */\n  emit(event: 'returnValue', parameterName: string, value: unknown, metadata: Metadata): boolean\n  /**\n   * @private\n   */\n  emit(event: 'requestCompleted'): boolean\n  /**\n   * @private\n   */\n  emit(event: 'cancel'): boolean\n  /**\n   * @private\n   */\n  emit(event: 'pause'): boolean\n  /**\n   * @private\n   */\n  emit(event: 'resume'): boolean\n  /**\n   * @private\n   */\n  emit(event: 'order', orderColumns: number[]): boolean\n  emit(event: string | symbol, ...args: any[]) {\n    return super.emit(event, ...args);\n  }\n\n  /**\n   * @param sqlTextOrProcedure\n   *   The SQL statement to be executed\n   *\n   * @param callback\n   *   The callback to execute once the request has been fully completed.\n   */\n  constructor(sqlTextOrProcedure: string | undefined, callback: CompletionCallback, options?: RequestOptions) {\n    super();\n\n    this.sqlTextOrProcedure = sqlTextOrProcedure;\n    this.parameters = [];\n    this.parametersByName = {};\n    this.preparing = false;\n    this.handle = undefined;\n    this.canceled = false;\n    this.paused = false;\n    this.error = undefined;\n    this.connection = undefined;\n    this.timeout = undefined;\n    this.userCallback = callback;\n    this.statementColumnEncryptionSetting = (options && options.statementColumnEncryptionSetting) || SQLServerStatementColumnEncryptionSetting.UseConnectionSetting;\n    this.cryptoMetadataLoaded = false;\n    this.callback = function(err: Error | undefined | null, rowCount?: number, rows?: any) {\n      if (this.preparing) {\n        this.preparing = false;\n        if (err) {\n          this.emit('error', err);\n        } else {\n          this.emit('prepared');\n        }\n      } else {\n        this.userCallback(err, rowCount, rows);\n        this.emit('requestCompleted');\n      }\n    };\n  }\n\n  /**\n   * @param name\n   *   The parameter name. This should correspond to a parameter in the SQL,\n   *   or a parameter that a called procedure expects. The name should not start with `@`.\n   *\n   * @param type\n   *   One of the supported data types.\n   *\n   * @param value\n   *   The value that the parameter is to be given. The Javascript type of the\n   *   argument should match that documented for data types.\n   *\n   * @param options\n   *   Additional type options. Optional.\n   */\n  // TODO: `type` must be a valid TDS value type\n  addParameter(name: string, type: DataType, value?: unknown, options?: Readonly<ParameterOptions> | null) {\n    const { output = false, length, precision, scale } = options ?? {};\n\n    const parameter: Parameter = {\n      type: type,\n      name: name,\n      value: value,\n      output: output,\n      length: length,\n      precision: precision,\n      scale: scale\n    };\n\n    this.parameters.push(parameter);\n    this.parametersByName[name] = parameter;\n  }\n\n  /**\n   * @param name\n   *   The parameter name. This should correspond to a parameter in the SQL,\n   *   or a parameter that a called procedure expects.\n   *\n   * @param type\n   *   One of the supported data types.\n   *\n   * @param value\n   *   The value that the parameter is to be given. The Javascript type of the\n   *   argument should match that documented for data types\n   *\n   * @param options\n   *   Additional type options. Optional.\n   */\n  addOutputParameter(name: string, type: DataType, value?: unknown, options?: Readonly<ParameterOptions> | null) {\n    this.addParameter(name, type, value, { ...options, output: true });\n  }\n\n  /**\n   * @private\n   */\n  makeParamsParameter(parameters: Parameter[]) {\n    let paramsParameter = '';\n    for (let i = 0, len = parameters.length; i < len; i++) {\n      const parameter = parameters[i];\n      if (paramsParameter.length > 0) {\n        paramsParameter += ', ';\n      }\n      paramsParameter += '@' + parameter.name + ' ';\n      paramsParameter += parameter.type.declaration(parameter);\n      if (parameter.output) {\n        paramsParameter += ' OUTPUT';\n      }\n    }\n    return paramsParameter;\n  }\n\n  /**\n   * @private\n   */\n  validateParameters(collation: Collation | undefined) {\n    for (let i = 0, len = this.parameters.length; i < len; i++) {\n      const parameter = this.parameters[i];\n\n      try {\n        parameter.value = parameter.type.validate(parameter.value, collation);\n      } catch (error: any) {\n        throw new RequestError('Validation failed for parameter \\'' + parameter.name + '\\'. ' + error.message, 'EPARAM', { cause: error });\n      }\n    }\n  }\n\n  /**\n   * Temporarily suspends the flow of data from the database. No more `row` events will be emitted until [[resume] is called.\n   * If this request is already in a paused state, calling [[pause]] has no effect.\n   */\n  pause() {\n    if (this.paused) {\n      return;\n    }\n    this.emit('pause');\n    this.paused = true;\n  }\n\n  /**\n   * Resumes the flow of data from the database.\n   * If this request is not in a paused state, calling [[resume]] has no effect.\n   */\n  resume() {\n    if (!this.paused) {\n      return;\n    }\n    this.paused = false;\n    this.emit('resume');\n  }\n\n  /**\n   * Cancels a request while waiting for a server response.\n   */\n  cancel() {\n    if (this.canceled) {\n      return;\n    }\n\n    this.canceled = true;\n    this.emit('cancel');\n  }\n\n  /**\n   * Sets a timeout for this request.\n   *\n   * @param timeout\n   *   The number of milliseconds before the request is considered failed,\n   *   or `0` for no timeout. When no timeout is set for the request,\n   *   the [[ConnectionOptions.requestTimeout]] of the [[Connection]] is used.\n   */\n  setTimeout(timeout?: number) {\n    this.timeout = timeout;\n  }\n}\n\nexport default Request;\nmodule.exports = Request;\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AAIA,IAAAE,MAAA,GAAAF,OAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AA2BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,OAAO,SAASC,oBAAY,CAAC;EACjC;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAGE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAGE;AACF;AACA;;EAQE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAOE;AACF;AACA;;EAGE;AACF;AACA;;EAGE;AACF;AACA;;EAoBE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAkBE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EAkBE;AACF;AACA;AACA;AACA;;EAkBE;AACF;AACA;AACA;;EAiBE;AACF;AACA;;EAmBEC,EAAEA,CAACC,KAAsB,EAAEC,QAAkC,EAAE;IAC7D,OAAO,KAAK,CAACF,EAAE,CAACC,KAAK,EAAEC,QAAQ,CAAC;EAClC;;EAEA;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEEC,IAAIA,CAACF,KAAsB,EAAE,GAAGG,IAAW,EAAE;IAC3C,OAAO,KAAK,CAACD,IAAI,CAACF,KAAK,EAAE,GAAGG,IAAI,CAAC;EACnC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,kBAAsC,EAAEC,QAA4B,EAAEC,OAAwB,EAAE;IAC1G,KAAK,CAAC,CAAC;IAEP,IAAI,CAACF,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACG,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,MAAM,GAAGC,SAAS;IACvB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,KAAK,GAAGH,SAAS;IACtB,IAAI,CAACI,UAAU,GAAGJ,SAAS;IAC3B,IAAI,CAACK,OAAO,GAAGL,SAAS;IACxB,IAAI,CAACM,YAAY,GAAGZ,QAAQ;IAC5B,IAAI,CAACa,gCAAgC,GAAIZ,OAAO,IAAIA,OAAO,CAACY,gCAAgC,IAAKC,gDAAyC,CAACC,oBAAoB;IAC/J,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAAChB,QAAQ,GAAG,UAASiB,GAA6B,EAAEC,QAAiB,EAAEC,IAAU,EAAE;MACrF,IAAI,IAAI,CAACf,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,GAAG,KAAK;QACtB,IAAIa,GAAG,EAAE;UACP,IAAI,CAACrB,IAAI,CAAC,OAAO,EAAEqB,GAAG,CAAC;QACzB,CAAC,MAAM;UACL,IAAI,CAACrB,IAAI,CAAC,UAAU,CAAC;QACvB;MACF,CAAC,MAAM;QACL,IAAI,CAACgB,YAAY,CAACK,GAAG,EAAEC,QAAQ,EAAEC,IAAI,CAAC;QACtC,IAAI,CAACvB,IAAI,CAAC,kBAAkB,CAAC;MAC/B;IACF,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE;EACAwB,YAAYA,CAACC,IAAY,EAAEC,IAAc,EAAEC,KAAe,EAAEtB,OAA2C,EAAE;IACvG,MAAM;MAAEuB,MAAM,GAAG,KAAK;MAAEC,MAAM;MAAEC,SAAS;MAAEC;IAAM,CAAC,GAAG1B,OAAO,IAAI,CAAC,CAAC;IAElE,MAAM2B,SAAoB,GAAG;MAC3BN,IAAI,EAAEA,IAAI;MACVD,IAAI,EAAEA,IAAI;MACVE,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA,MAAM;MACdC,MAAM,EAAEA,MAAM;MACdC,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEA;IACT,CAAC;IAED,IAAI,CAACzB,UAAU,CAAC2B,IAAI,CAACD,SAAS,CAAC;IAC/B,IAAI,CAACzB,gBAAgB,CAACkB,IAAI,CAAC,GAAGO,SAAS;EACzC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,kBAAkBA,CAACT,IAAY,EAAEC,IAAc,EAAEC,KAAe,EAAEtB,OAA2C,EAAE;IAC7G,IAAI,CAACmB,YAAY,CAACC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAE;MAAE,GAAGtB,OAAO;MAAEuB,MAAM,EAAE;IAAK,CAAC,CAAC;EACpE;;EAEA;AACF;AACA;EACEO,mBAAmBA,CAAC7B,UAAuB,EAAE;IAC3C,IAAI8B,eAAe,GAAG,EAAE;IACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGhC,UAAU,CAACuB,MAAM,EAAEQ,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACrD,MAAML,SAAS,GAAG1B,UAAU,CAAC+B,CAAC,CAAC;MAC/B,IAAID,eAAe,CAACP,MAAM,GAAG,CAAC,EAAE;QAC9BO,eAAe,IAAI,IAAI;MACzB;MACAA,eAAe,IAAI,GAAG,GAAGJ,SAAS,CAACP,IAAI,GAAG,GAAG;MAC7CW,eAAe,IAAIJ,SAAS,CAACN,IAAI,CAACa,WAAW,CAACP,SAAS,CAAC;MACxD,IAAIA,SAAS,CAACJ,MAAM,EAAE;QACpBQ,eAAe,IAAI,SAAS;MAC9B;IACF;IACA,OAAOA,eAAe;EACxB;;EAEA;AACF;AACA;EACEI,kBAAkBA,CAACC,SAAgC,EAAE;IACnD,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG,IAAI,CAAChC,UAAU,CAACuB,MAAM,EAAEQ,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC1D,MAAML,SAAS,GAAG,IAAI,CAAC1B,UAAU,CAAC+B,CAAC,CAAC;MAEpC,IAAI;QACFL,SAAS,CAACL,KAAK,GAAGK,SAAS,CAACN,IAAI,CAACgB,QAAQ,CAACV,SAAS,CAACL,KAAK,EAAEc,SAAS,CAAC;MACvE,CAAC,CAAC,OAAO5B,KAAU,EAAE;QACnB,MAAM,IAAI8B,oBAAY,CAAC,oCAAoC,GAAGX,SAAS,CAACP,IAAI,GAAG,MAAM,GAAGZ,KAAK,CAAC+B,OAAO,EAAE,QAAQ,EAAE;UAAEC,KAAK,EAAEhC;QAAM,CAAC,CAAC;MACpI;IACF;EACF;;EAEA;AACF;AACA;AACA;EACEiC,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAAClC,MAAM,EAAE;MACf;IACF;IACA,IAAI,CAACZ,IAAI,CAAC,OAAO,CAAC;IAClB,IAAI,CAACY,MAAM,GAAG,IAAI;EACpB;;EAEA;AACF;AACA;AACA;EACEmC,MAAMA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACnC,MAAM,EAAE;MAChB;IACF;IACA,IAAI,CAACA,MAAM,GAAG,KAAK;IACnB,IAAI,CAACZ,IAAI,CAAC,QAAQ,CAAC;EACrB;;EAEA;AACF;AACA;EACEgD,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACrC,QAAQ,EAAE;MACjB;IACF;IAEA,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACX,IAAI,CAAC,QAAQ,CAAC;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEiD,UAAUA,CAAClC,OAAgB,EAAE;IAC3B,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;AACF;AAAC,IAAAmC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEczD,OAAO;AACtB0D,MAAM,CAACF,OAAO,GAAGxD,OAAO"}
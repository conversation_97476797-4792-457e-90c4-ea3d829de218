{"version": 3, "file": "value-parser.js", "names": ["_metadataParser", "require", "_dataType", "_iconvLite", "_interopRequireDefault", "_sprintfJs", "_guid<PERSON><PERSON>er", "_helpers", "obj", "__esModule", "default", "NULL", "MAX", "THREE_AND_A_THIRD", "MONEY_DIVISOR", "PLP_NULL", "UNKNOWN_PLP_LEN", "DEFAULT_ENCODING", "readTinyInt", "buf", "offset", "readUInt8", "readSmallInt", "readInt16LE", "readInt", "readInt32LE", "readBigInt", "value", "readBigInt64LE", "Result", "toString", "readReal", "readFloatLE", "readFloat", "readDoubleLE", "readSmallMoney", "readMoney", "high", "low", "readUInt32LE", "readBit", "readValue", "metadata", "options", "type", "name", "dataLength", "Error", "codepage", "collation", "readUInt16LE", "readChars", "readNChars", "readBinary", "textPoint<PERSON><PERSON><PERSON><PERSON>", "readSmallDateTime", "useUTC", "readDateTime", "readTime", "scale", "readDate", "readDateTime2", "readDateTimeOffset", "readNumeric", "precision", "readUniqueIdentifier", "sprintf", "readVariant", "isPLPStream", "data", "lowerCaseGuids", "bufferToLowerCaseGuid", "bufferToUpperCaseGuid", "_precision", "sign", "readUNumeric64LE", "readUNumeric96LE", "readUNumeric128LE", "Math", "pow", "baseType", "TYPE", "propBytes", "readCollation", "length", "NotEnoughDataError", "slice", "iconv", "decode", "readPLPStream", "parser", "buffer", "position", "waitForChunk", "<PERSON><PERSON><PERSON><PERSON>", "readBigUInt64LE", "chunks", "<PERSON><PERSON><PERSON><PERSON>", "chunkLength", "push", "Number", "days", "minutes", "Date", "UTC", "threeHundredthsOfSecond", "milliseconds", "round", "readUInt24LE", "readUInt40LE", "i", "date", "Object", "defineProperty", "enumerable", "time", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "nanosecondsDelta", "module", "exports"], "sources": ["../src/value-parser.ts"], "sourcesContent": ["import Parser, { type ParserOptions } from './token/stream-parser';\nimport { type Metadata, readCollation } from './metadata-parser';\nimport { TYPE } from './data-type';\n\nimport iconv from 'iconv-lite';\nimport { sprintf } from 'sprintf-js';\nimport { bufferToLowerCaseGuid, bufferToUpperCaseGuid } from './guid-parser';\nimport { NotEnoughDataError, Result, readBigInt64LE, readDoubleLE, readFloatLE, readInt16LE, readInt32LE, readUInt16LE, readUInt32LE, readUInt8, readUInt24LE, readUInt40LE, readUNumeric64LE, readUNumeric96LE, readUNumeric128LE } from './token/helpers';\n\nconst NULL = (1 << 16) - 1;\nconst MAX = (1 << 16) - 1;\nconst THREE_AND_A_THIRD = 3 + (1 / 3);\nconst MONEY_DIVISOR = 10000;\nconst PLP_NULL = 0xFFFFFFFFFFFFFFFFn;\nconst UNKNOWN_PLP_LEN = 0xFFFFFFFFFFFFFFFEn;\nconst DEFAULT_ENCODING = 'utf8';\n\nfunction readTinyInt(buf: Buffer, offset: number): Result<number> {\n  return readUInt8(buf, offset);\n}\n\nfunction readSmallInt(buf: Buffer, offset: number): Result<number> {\n  return readInt16LE(buf, offset);\n}\n\nfunction readInt(buf: Buffer, offset: number): Result<number> {\n  return readInt32LE(buf, offset);\n}\n\nfunction readBigInt(buf: Buffer, offset: number): Result<string> {\n  let value;\n  ({ offset, value } = readBigInt64LE(buf, offset));\n\n  return new Result(value.toString(), offset);\n}\n\nfunction readReal(buf: Buffer, offset: number): Result<number> {\n  return readFloatLE(buf, offset);\n}\n\nfunction readFloat(buf: Buffer, offset: number): Result<number> {\n  return readDoubleLE(buf, offset);\n}\n\nfunction readSmallMoney(buf: Buffer, offset: number): Result<number> {\n  let value;\n  ({ offset, value } = readInt32LE(buf, offset));\n\n  return new Result(value / MONEY_DIVISOR, offset);\n}\n\nfunction readMoney(buf: Buffer, offset: number): Result<number> {\n  let high;\n  ({ offset, value: high } = readInt32LE(buf, offset));\n\n  let low;\n  ({ offset, value: low } = readUInt32LE(buf, offset));\n\n  return new Result((low + (0x100000000 * high)) / MONEY_DIVISOR, offset);\n}\n\nfunction readBit(buf: Buffer, offset: number): Result<boolean> {\n  let value;\n  ({ offset, value } = readUInt8(buf, offset));\n\n  return new Result(!!value, offset);\n}\n\nfunction readValue(buf: Buffer, offset: number, metadata: Metadata, options: ParserOptions): Result<unknown> {\n  const type = metadata.type;\n\n  switch (type.name) {\n    case 'Null':\n      return new Result(null, offset);\n\n    case 'TinyInt': {\n      return readTinyInt(buf, offset);\n    }\n\n    case 'SmallInt': {\n      return readSmallInt(buf, offset);\n    }\n\n    case 'Int': {\n      return readInt(buf, offset);\n    }\n\n    case 'BigInt': {\n      return readBigInt(buf, offset);\n    }\n\n    case 'IntN': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt8(buf, offset));\n\n      switch (dataLength) {\n        case 0:\n          return new Result(null, offset);\n\n        case 1:\n          return readTinyInt(buf, offset);\n        case 2:\n          return readSmallInt(buf, offset);\n        case 4:\n          return readInt(buf, offset);\n        case 8:\n          return readBigInt(buf, offset);\n\n        default:\n          throw new Error('Unsupported dataLength ' + dataLength + ' for IntN');\n      }\n    }\n\n    case 'Real': {\n      return readReal(buf, offset);\n    }\n\n    case 'Float': {\n      return readFloat(buf, offset);\n    }\n\n    case 'FloatN': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt8(buf, offset));\n\n      switch (dataLength) {\n        case 0:\n          return new Result(null, offset);\n\n        case 4:\n          return readReal(buf, offset);\n        case 8:\n          return readFloat(buf, offset);\n\n        default:\n          throw new Error('Unsupported dataLength ' + dataLength + ' for FloatN');\n      }\n    }\n\n    case 'SmallMoney': {\n      return readSmallMoney(buf, offset);\n    }\n\n    case 'Money':\n      return readMoney(buf, offset);\n\n    case 'MoneyN': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt8(buf, offset));\n\n      switch (dataLength) {\n        case 0:\n          return new Result(null, offset);\n\n        case 4:\n          return readSmallMoney(buf, offset);\n        case 8:\n          return readMoney(buf, offset);\n\n        default:\n          throw new Error('Unsupported dataLength ' + dataLength + ' for MoneyN');\n      }\n    }\n\n    case 'Bit': {\n      return readBit(buf, offset);\n    }\n\n    case 'BitN': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt8(buf, offset));\n\n      switch (dataLength) {\n        case 0:\n          return new Result(null, offset);\n\n        case 1:\n          return readBit(buf, offset);\n\n        default:\n          throw new Error('Unsupported dataLength ' + dataLength + ' for BitN');\n      }\n    }\n\n    case 'VarChar':\n    case 'Char': {\n      const codepage = metadata.collation!.codepage!;\n\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt16LE(buf, offset));\n\n      if (dataLength === NULL) {\n        return new Result(null, offset);\n      }\n\n      return readChars(buf, offset, dataLength, codepage);\n    }\n\n    case 'NVarChar':\n    case 'NChar': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt16LE(buf, offset));\n\n      if (dataLength === NULL) {\n        return new Result(null, offset);\n      }\n\n      return readNChars(buf, offset, dataLength);\n    }\n\n    case 'VarBinary':\n    case 'Binary': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt16LE(buf, offset));\n\n      if (dataLength === NULL) {\n        return new Result(null, offset);\n      }\n\n      return readBinary(buf, offset, dataLength);\n    }\n\n    case 'Text': {\n      let textPointerLength;\n      ({ offset, value: textPointerLength } = readUInt8(buf, offset));\n\n      if (textPointerLength === 0) {\n        return new Result(null, offset);\n      }\n\n      // Textpointer\n      ({ offset } = readBinary(buf, offset, textPointerLength));\n\n      // Timestamp\n      ({ offset } = readBinary(buf, offset, 8));\n\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt32LE(buf, offset));\n\n      return readChars(buf, offset, dataLength, metadata.collation!.codepage!);\n    }\n\n    case 'NText': {\n      let textPointerLength;\n      ({ offset, value: textPointerLength } = readUInt8(buf, offset));\n\n      if (textPointerLength === 0) {\n        return new Result(null, offset);\n      }\n\n      // Textpointer\n      ({ offset } = readBinary(buf, offset, textPointerLength));\n\n      // Timestamp\n      ({ offset } = readBinary(buf, offset, 8));\n\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt32LE(buf, offset));\n\n      return readNChars(buf, offset, dataLength);\n    }\n\n    case 'Image': {\n      let textPointerLength;\n      ({ offset, value: textPointerLength } = readUInt8(buf, offset));\n\n      if (textPointerLength === 0) {\n        return new Result(null, offset);\n      }\n\n      // Textpointer\n      ({ offset } = readBinary(buf, offset, textPointerLength));\n\n      // Timestamp\n      ({ offset } = readBinary(buf, offset, 8));\n\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt32LE(buf, offset));\n\n      return readBinary(buf, offset, dataLength);\n    }\n\n    case 'SmallDateTime': {\n      return readSmallDateTime(buf, offset, options.useUTC);\n    }\n\n    case 'DateTime': {\n      return readDateTime(buf, offset, options.useUTC);\n    }\n\n    case 'DateTimeN': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt8(buf, offset));\n\n      switch (dataLength) {\n        case 0:\n          return new Result(null, offset);\n\n        case 4:\n          return readSmallDateTime(buf, offset, options.useUTC);\n        case 8:\n          return readDateTime(buf, offset, options.useUTC);\n\n        default:\n          throw new Error('Unsupported dataLength ' + dataLength + ' for DateTimeN');\n      }\n    }\n\n    case 'Time': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt8(buf, offset));\n\n      if (dataLength === 0) {\n        return new Result(null, offset);\n      }\n\n      return readTime(buf, offset, dataLength, metadata.scale!, options.useUTC);\n    }\n\n    case 'Date': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt8(buf, offset));\n\n      if (dataLength === 0) {\n        return new Result(null, offset);\n      }\n\n      return readDate(buf, offset, options.useUTC);\n    }\n\n    case 'DateTime2': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt8(buf, offset));\n\n      if (dataLength === 0) {\n        return new Result(null, offset);\n      }\n\n      return readDateTime2(buf, offset, dataLength, metadata.scale!, options.useUTC);\n    }\n\n    case 'DateTimeOffset': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt8(buf, offset));\n\n      if (dataLength === 0) {\n        return new Result(null, offset);\n      }\n\n      return readDateTimeOffset(buf, offset, dataLength, metadata.scale!);\n    }\n\n    case 'NumericN':\n    case 'DecimalN': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt8(buf, offset));\n\n      if (dataLength === 0) {\n        return new Result(null, offset);\n      }\n\n      return readNumeric(buf, offset, dataLength, metadata.precision!, metadata.scale!);\n    }\n\n    case 'UniqueIdentifier': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt8(buf, offset));\n\n      switch (dataLength) {\n        case 0:\n          return new Result(null, offset);\n\n        case 0x10:\n          return readUniqueIdentifier(buf, offset, options);\n\n        default:\n          throw new Error(sprintf('Unsupported guid size %d', dataLength! - 1));\n      }\n    }\n\n    case 'Variant': {\n      let dataLength;\n      ({ offset, value: dataLength } = readUInt32LE(buf, offset));\n\n      if (dataLength === 0) {\n        return new Result(null, offset);\n      }\n\n      return readVariant(buf, offset, options, dataLength);\n    }\n\n    default: {\n      throw new Error('Invalid type!');\n    }\n  }\n}\n\nfunction isPLPStream(metadata: Metadata) {\n  switch (metadata.type.name) {\n    case 'VarChar':\n    case 'NVarChar':\n    case 'VarBinary': {\n      return metadata.dataLength === MAX;\n    }\n\n    case 'Xml': {\n      return true;\n    }\n\n    case 'UDT': {\n      return true;\n    }\n  }\n}\n\nfunction readUniqueIdentifier(buf: Buffer, offset: number, options: ParserOptions): Result<string> {\n  let data;\n  ({ value: data, offset } = readBinary(buf, offset, 0x10));\n\n  return new Result(options.lowerCaseGuids ? bufferToLowerCaseGuid(data) : bufferToUpperCaseGuid(data), offset);\n}\n\nfunction readNumeric(buf: Buffer, offset: number, dataLength: number, _precision: number, scale: number): Result<number> {\n  let sign;\n  ({ offset, value: sign } = readUInt8(buf, offset));\n\n  sign = sign === 1 ? 1 : -1;\n\n  let value;\n  if (dataLength === 5) {\n    ({ offset, value } = readUInt32LE(buf, offset));\n  } else if (dataLength === 9) {\n    ({ offset, value } = readUNumeric64LE(buf, offset));\n  } else if (dataLength === 13) {\n    ({ offset, value } = readUNumeric96LE(buf, offset));\n  } else if (dataLength === 17) {\n    ({ offset, value } = readUNumeric128LE(buf, offset));\n  } else {\n    throw new Error(sprintf('Unsupported numeric dataLength %d', dataLength));\n  }\n\n  return new Result((value * sign) / Math.pow(10, scale), offset);\n}\n\nfunction readVariant(buf: Buffer, offset: number, options: ParserOptions, dataLength: number): Result<unknown> {\n  let baseType;\n  ({ value: baseType, offset } = readUInt8(buf, offset));\n\n  const type = TYPE[baseType];\n\n  let propBytes;\n  ({ value: propBytes, offset } = readUInt8(buf, offset));\n\n  dataLength = dataLength - propBytes - 2;\n\n  switch (type.name) {\n    case 'UniqueIdentifier':\n      return readUniqueIdentifier(buf, offset, options);\n\n    case 'Bit':\n      return readBit(buf, offset);\n\n    case 'TinyInt':\n      return readTinyInt(buf, offset);\n\n    case 'SmallInt':\n      return readSmallInt(buf, offset);\n\n    case 'Int':\n      return readInt(buf, offset);\n\n    case 'BigInt':\n      return readBigInt(buf, offset);\n\n    case 'SmallDateTime':\n      return readSmallDateTime(buf, offset, options.useUTC);\n\n    case 'DateTime':\n      return readDateTime(buf, offset, options.useUTC);\n\n    case 'Real':\n      return readReal(buf, offset);\n\n    case 'Float':\n      return readFloat(buf, offset);\n\n    case 'SmallMoney':\n      return readSmallMoney(buf, offset);\n\n    case 'Money':\n      return readMoney(buf, offset);\n\n    case 'Date':\n      return readDate(buf, offset, options.useUTC);\n\n    case 'Time': {\n      let scale;\n      ({ value: scale, offset } = readUInt8(buf, offset));\n\n      return readTime(buf, offset, dataLength, scale, options.useUTC);\n    }\n\n    case 'DateTime2': {\n      let scale;\n      ({ value: scale, offset } = readUInt8(buf, offset));\n\n      return readDateTime2(buf, offset, dataLength, scale, options.useUTC);\n    }\n\n    case 'DateTimeOffset': {\n      let scale;\n      ({ value: scale, offset } = readUInt8(buf, offset));\n\n      return readDateTimeOffset(buf, offset, dataLength, scale);\n    }\n\n    case 'VarBinary':\n    case 'Binary': {\n      // maxLength (unused?)\n      ({ offset } = readUInt16LE(buf, offset));\n\n      return readBinary(buf, offset, dataLength);\n    }\n\n    case 'NumericN':\n    case 'DecimalN': {\n      let precision;\n      ({ value: precision, offset } = readUInt8(buf, offset));\n\n      let scale;\n      ({ value: scale, offset } = readUInt8(buf, offset));\n\n      return readNumeric(buf, offset, dataLength, precision, scale);\n    }\n\n    case 'VarChar':\n    case 'Char': {\n      // maxLength (unused?)\n      ({ offset } = readUInt16LE(buf, offset));\n\n      let collation;\n      ({ value: collation, offset } = readCollation(buf, offset));\n\n      return readChars(buf, offset, dataLength, collation.codepage!);\n    }\n\n    case 'NVarChar':\n    case 'NChar': {\n      // maxLength (unused?)\n      ({ offset } = readUInt16LE(buf, offset));\n\n      // collation (unused?)\n      ({ offset } = readCollation(buf, offset));\n\n      return readNChars(buf, offset, dataLength);\n    }\n\n    default:\n      throw new Error('Invalid type!');\n  }\n}\n\nfunction readBinary(buf: Buffer, offset: number, dataLength: number): Result<Buffer> {\n  if (buf.length < offset + dataLength) {\n    throw new NotEnoughDataError(offset + dataLength);\n  }\n\n  return new Result(buf.slice(offset, offset + dataLength), offset + dataLength);\n}\n\nfunction readChars(buf: Buffer, offset: number, dataLength: number, codepage: string): Result<string> {\n  if (buf.length < offset + dataLength) {\n    throw new NotEnoughDataError(offset + dataLength);\n  }\n\n  return new Result(iconv.decode(buf.slice(offset, offset + dataLength), codepage ?? DEFAULT_ENCODING), offset + dataLength);\n}\n\nfunction readNChars(buf: Buffer, offset: number, dataLength: number): Result<string> {\n  if (buf.length < offset + dataLength) {\n    throw new NotEnoughDataError(offset + dataLength);\n  }\n\n  return new Result(buf.toString('ucs2', offset, offset + dataLength), offset + dataLength);\n}\n\nasync function readPLPStream(parser: Parser): Promise<null | Buffer[]> {\n  while (parser.buffer.length < parser.position + 8) {\n    await parser.waitForChunk();\n  }\n\n  const expectedLength = parser.buffer.readBigUInt64LE(parser.position);\n  parser.position += 8;\n\n  if (expectedLength === PLP_NULL) {\n    return null;\n  }\n\n  const chunks: Buffer[] = [];\n  let currentLength = 0;\n\n  while (true) {\n    while (parser.buffer.length < parser.position + 4) {\n      await parser.waitForChunk();\n    }\n\n    const chunkLength = parser.buffer.readUInt32LE(parser.position);\n    parser.position += 4;\n\n    if (!chunkLength) {\n      break;\n    }\n\n    while (parser.buffer.length < parser.position + chunkLength) {\n      await parser.waitForChunk();\n    }\n\n    chunks.push(parser.buffer.slice(parser.position, parser.position + chunkLength));\n    parser.position += chunkLength;\n    currentLength += chunkLength;\n  }\n\n  if (expectedLength !== UNKNOWN_PLP_LEN) {\n    if (currentLength !== Number(expectedLength)) {\n      throw new Error('Partially Length-prefixed Bytes unmatched lengths : expected ' + expectedLength + ', but got ' + currentLength + ' bytes');\n    }\n  }\n\n  return chunks;\n}\n\nfunction readSmallDateTime(buf: Buffer, offset: number, useUTC: boolean): Result<Date> {\n  let days;\n  ({ offset, value: days } = readUInt16LE(buf, offset));\n\n  let minutes;\n  ({ offset, value: minutes } = readUInt16LE(buf, offset));\n\n  let value;\n  if (useUTC) {\n    value = new Date(Date.UTC(1900, 0, 1 + days, 0, minutes));\n  } else {\n    value = new Date(1900, 0, 1 + days, 0, minutes);\n  }\n\n  return new Result(value, offset);\n}\n\nfunction readDateTime(buf: Buffer, offset: number, useUTC: boolean): Result<Date> {\n  let days;\n  ({ offset, value: days } = readInt32LE(buf, offset));\n\n  let threeHundredthsOfSecond;\n  ({ offset, value: threeHundredthsOfSecond } = readInt32LE(buf, offset));\n\n  const milliseconds = Math.round(threeHundredthsOfSecond * THREE_AND_A_THIRD);\n\n  let value;\n  if (useUTC) {\n    value = new Date(Date.UTC(1900, 0, 1 + days, 0, 0, 0, milliseconds));\n  } else {\n    value = new Date(1900, 0, 1 + days, 0, 0, 0, milliseconds);\n  }\n\n  return new Result(value, offset);\n}\n\ninterface DateWithNanosecondsDelta extends Date {\n  nanosecondsDelta: number;\n}\n\nfunction readTime(buf: Buffer, offset: number, dataLength: number, scale: number, useUTC: boolean): Result<DateWithNanosecondsDelta> {\n  let value;\n\n  switch (dataLength) {\n    case 3: {\n      ({ value, offset } = readUInt24LE(buf, offset));\n      break;\n    }\n\n    case 4: {\n      ({ value, offset } = readUInt32LE(buf, offset));\n      break;\n    }\n\n    case 5: {\n      ({ value, offset } = readUInt40LE(buf, offset));\n      break;\n    }\n\n    default: {\n      throw new Error('unreachable');\n    }\n  }\n\n  if (scale < 7) {\n    for (let i = scale; i < 7; i++) {\n      value *= 10;\n    }\n  }\n\n  let date;\n  if (useUTC) {\n    date = new Date(Date.UTC(1970, 0, 1, 0, 0, 0, value / 10000)) as DateWithNanosecondsDelta;\n  } else {\n    date = new Date(1970, 0, 1, 0, 0, 0, value / 10000) as DateWithNanosecondsDelta;\n  }\n  Object.defineProperty(date, 'nanosecondsDelta', {\n    enumerable: false,\n    value: (value % 10000) / Math.pow(10, 7)\n  });\n\n  return new Result(date, offset);\n}\n\nfunction readDate(buf: Buffer, offset: number, useUTC: boolean): Result<Date> {\n  let days;\n  ({ offset, value: days } = readUInt24LE(buf, offset));\n\n  if (useUTC) {\n    return new Result(new Date(Date.UTC(2000, 0, days - 730118)), offset);\n  } else {\n    return new Result(new Date(2000, 0, days - 730118), offset);\n  }\n}\n\nfunction readDateTime2(buf: Buffer, offset: number, dataLength: number, scale: number, useUTC: boolean): Result<DateWithNanosecondsDelta> {\n  let time;\n  ({ offset, value: time } = readTime(buf, offset, dataLength - 3, scale, useUTC));\n\n  let days;\n  ({ offset, value: days } = readUInt24LE(buf, offset));\n\n  let date;\n  if (useUTC) {\n    date = new Date(Date.UTC(2000, 0, days - 730118, 0, 0, 0, +time)) as DateWithNanosecondsDelta;\n  } else {\n    date = new Date(2000, 0, days - 730118, time.getHours(), time.getMinutes(), time.getSeconds(), time.getMilliseconds()) as DateWithNanosecondsDelta;\n  }\n  Object.defineProperty(date, 'nanosecondsDelta', {\n    enumerable: false,\n    value: time.nanosecondsDelta\n  });\n\n  return new Result(date, offset);\n}\n\nfunction readDateTimeOffset(buf: Buffer, offset: number, dataLength: number, scale: number): Result<DateWithNanosecondsDelta> {\n  let time;\n  ({ offset, value: time } = readTime(buf, offset, dataLength - 5, scale, true));\n\n  let days;\n  ({ offset, value: days } = readUInt24LE(buf, offset));\n\n  // time offset?\n  ({ offset } = readUInt16LE(buf, offset));\n\n  const date = new Date(Date.UTC(2000, 0, days - 730118, 0, 0, 0, +time)) as DateWithNanosecondsDelta;\n  Object.defineProperty(date, 'nanosecondsDelta', {\n    enumerable: false,\n    value: time.nanosecondsDelta\n  });\n  return new Result(date, offset);\n}\n\nmodule.exports.readValue = readValue;\nmodule.exports.isPLPStream = isPLPStream;\nmodule.exports.readPLPStream = readPLPStream;\n\nexport { readValue, isPLPStream, readPLPStream };\n"], "mappings": ";;;;;;;;AACA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAEA,IAAAE,UAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AAA4P,SAAAG,uBAAAI,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE5P,MAAMG,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC;AAC1B,MAAMC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC;AACzB,MAAMC,iBAAiB,GAAG,CAAC,GAAI,CAAC,GAAG,CAAE;AACrC,MAAMC,aAAa,GAAG,KAAK;AAC3B,MAAMC,QAAQ,GAAG,mBAAmB;AACpC,MAAMC,eAAe,GAAG,mBAAmB;AAC3C,MAAMC,gBAAgB,GAAG,MAAM;AAE/B,SAASC,WAAWA,CAACC,GAAW,EAAEC,MAAc,EAAkB;EAChE,OAAO,IAAAC,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;AAC/B;AAEA,SAASE,YAAYA,CAACH,GAAW,EAAEC,MAAc,EAAkB;EACjE,OAAO,IAAAG,oBAAW,EAACJ,GAAG,EAAEC,MAAM,CAAC;AACjC;AAEA,SAASI,OAAOA,CAACL,GAAW,EAAEC,MAAc,EAAkB;EAC5D,OAAO,IAAAK,oBAAW,EAACN,GAAG,EAAEC,MAAM,CAAC;AACjC;AAEA,SAASM,UAAUA,CAACP,GAAW,EAAEC,MAAc,EAAkB;EAC/D,IAAIO,KAAK;EACT,CAAC;IAAEP,MAAM;IAAEO;EAAM,CAAC,GAAG,IAAAC,uBAAc,EAACT,GAAG,EAAEC,MAAM,CAAC;EAEhD,OAAO,IAAIS,eAAM,CAACF,KAAK,CAACG,QAAQ,CAAC,CAAC,EAAEV,MAAM,CAAC;AAC7C;AAEA,SAASW,QAAQA,CAACZ,GAAW,EAAEC,MAAc,EAAkB;EAC7D,OAAO,IAAAY,oBAAW,EAACb,GAAG,EAAEC,MAAM,CAAC;AACjC;AAEA,SAASa,SAASA,CAACd,GAAW,EAAEC,MAAc,EAAkB;EAC9D,OAAO,IAAAc,qBAAY,EAACf,GAAG,EAAEC,MAAM,CAAC;AAClC;AAEA,SAASe,cAAcA,CAAChB,GAAW,EAAEC,MAAc,EAAkB;EACnE,IAAIO,KAAK;EACT,CAAC;IAAEP,MAAM;IAAEO;EAAM,CAAC,GAAG,IAAAF,oBAAW,EAACN,GAAG,EAAEC,MAAM,CAAC;EAE7C,OAAO,IAAIS,eAAM,CAACF,KAAK,GAAGb,aAAa,EAAEM,MAAM,CAAC;AAClD;AAEA,SAASgB,SAASA,CAACjB,GAAW,EAAEC,MAAc,EAAkB;EAC9D,IAAIiB,IAAI;EACR,CAAC;IAAEjB,MAAM;IAAEO,KAAK,EAAEU;EAAK,CAAC,GAAG,IAAAZ,oBAAW,EAACN,GAAG,EAAEC,MAAM,CAAC;EAEnD,IAAIkB,GAAG;EACP,CAAC;IAAElB,MAAM;IAAEO,KAAK,EAAEW;EAAI,CAAC,GAAG,IAAAC,qBAAY,EAACpB,GAAG,EAAEC,MAAM,CAAC;EAEnD,OAAO,IAAIS,eAAM,CAAC,CAACS,GAAG,GAAI,WAAW,GAAGD,IAAK,IAAIvB,aAAa,EAAEM,MAAM,CAAC;AACzE;AAEA,SAASoB,OAAOA,CAACrB,GAAW,EAAEC,MAAc,EAAmB;EAC7D,IAAIO,KAAK;EACT,CAAC;IAAEP,MAAM;IAAEO;EAAM,CAAC,GAAG,IAAAN,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;EAE3C,OAAO,IAAIS,eAAM,CAAC,CAAC,CAACF,KAAK,EAAEP,MAAM,CAAC;AACpC;AAEA,SAASqB,SAASA,CAACtB,GAAW,EAAEC,MAAc,EAAEsB,QAAkB,EAAEC,OAAsB,EAAmB;EAC3G,MAAMC,IAAI,GAAGF,QAAQ,CAACE,IAAI;EAE1B,QAAQA,IAAI,CAACC,IAAI;IACf,KAAK,MAAM;MACT,OAAO,IAAIhB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;IAEjC,KAAK,SAAS;MAAE;QACd,OAAOF,WAAW,CAACC,GAAG,EAAEC,MAAM,CAAC;MACjC;IAEA,KAAK,UAAU;MAAE;QACf,OAAOE,YAAY,CAACH,GAAG,EAAEC,MAAM,CAAC;MAClC;IAEA,KAAK,KAAK;MAAE;QACV,OAAOI,OAAO,CAACL,GAAG,EAAEC,MAAM,CAAC;MAC7B;IAEA,KAAK,QAAQ;MAAE;QACb,OAAOM,UAAU,CAACP,GAAG,EAAEC,MAAM,CAAC;MAChC;IAEA,KAAK,MAAM;MAAE;QACX,IAAI0B,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAzB,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAEvD,QAAQ0B,UAAU;UAChB,KAAK,CAAC;YACJ,OAAO,IAAIjB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;UAEjC,KAAK,CAAC;YACJ,OAAOF,WAAW,CAACC,GAAG,EAAEC,MAAM,CAAC;UACjC,KAAK,CAAC;YACJ,OAAOE,YAAY,CAACH,GAAG,EAAEC,MAAM,CAAC;UAClC,KAAK,CAAC;YACJ,OAAOI,OAAO,CAACL,GAAG,EAAEC,MAAM,CAAC;UAC7B,KAAK,CAAC;YACJ,OAAOM,UAAU,CAACP,GAAG,EAAEC,MAAM,CAAC;UAEhC;YACE,MAAM,IAAI2B,KAAK,CAAC,yBAAyB,GAAGD,UAAU,GAAG,WAAW,CAAC;QACzE;MACF;IAEA,KAAK,MAAM;MAAE;QACX,OAAOf,QAAQ,CAACZ,GAAG,EAAEC,MAAM,CAAC;MAC9B;IAEA,KAAK,OAAO;MAAE;QACZ,OAAOa,SAAS,CAACd,GAAG,EAAEC,MAAM,CAAC;MAC/B;IAEA,KAAK,QAAQ;MAAE;QACb,IAAI0B,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAzB,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAEvD,QAAQ0B,UAAU;UAChB,KAAK,CAAC;YACJ,OAAO,IAAIjB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;UAEjC,KAAK,CAAC;YACJ,OAAOW,QAAQ,CAACZ,GAAG,EAAEC,MAAM,CAAC;UAC9B,KAAK,CAAC;YACJ,OAAOa,SAAS,CAACd,GAAG,EAAEC,MAAM,CAAC;UAE/B;YACE,MAAM,IAAI2B,KAAK,CAAC,yBAAyB,GAAGD,UAAU,GAAG,aAAa,CAAC;QAC3E;MACF;IAEA,KAAK,YAAY;MAAE;QACjB,OAAOX,cAAc,CAAChB,GAAG,EAAEC,MAAM,CAAC;MACpC;IAEA,KAAK,OAAO;MACV,OAAOgB,SAAS,CAACjB,GAAG,EAAEC,MAAM,CAAC;IAE/B,KAAK,QAAQ;MAAE;QACb,IAAI0B,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAzB,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAEvD,QAAQ0B,UAAU;UAChB,KAAK,CAAC;YACJ,OAAO,IAAIjB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;UAEjC,KAAK,CAAC;YACJ,OAAOe,cAAc,CAAChB,GAAG,EAAEC,MAAM,CAAC;UACpC,KAAK,CAAC;YACJ,OAAOgB,SAAS,CAACjB,GAAG,EAAEC,MAAM,CAAC;UAE/B;YACE,MAAM,IAAI2B,KAAK,CAAC,yBAAyB,GAAGD,UAAU,GAAG,aAAa,CAAC;QAC3E;MACF;IAEA,KAAK,KAAK;MAAE;QACV,OAAON,OAAO,CAACrB,GAAG,EAAEC,MAAM,CAAC;MAC7B;IAEA,KAAK,MAAM;MAAE;QACX,IAAI0B,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAzB,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAEvD,QAAQ0B,UAAU;UAChB,KAAK,CAAC;YACJ,OAAO,IAAIjB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;UAEjC,KAAK,CAAC;YACJ,OAAOoB,OAAO,CAACrB,GAAG,EAAEC,MAAM,CAAC;UAE7B;YACE,MAAM,IAAI2B,KAAK,CAAC,yBAAyB,GAAGD,UAAU,GAAG,WAAW,CAAC;QACzE;MACF;IAEA,KAAK,SAAS;IACd,KAAK,MAAM;MAAE;QACX,MAAME,QAAQ,GAAGN,QAAQ,CAACO,SAAS,CAAED,QAAS;QAE9C,IAAIF,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAI,qBAAY,EAAC/B,GAAG,EAAEC,MAAM,CAAC;QAE1D,IAAI0B,UAAU,KAAKnC,IAAI,EAAE;UACvB,OAAO,IAAIkB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;QACjC;QAEA,OAAO+B,SAAS,CAAChC,GAAG,EAAEC,MAAM,EAAE0B,UAAU,EAAEE,QAAQ,CAAC;MACrD;IAEA,KAAK,UAAU;IACf,KAAK,OAAO;MAAE;QACZ,IAAIF,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAI,qBAAY,EAAC/B,GAAG,EAAEC,MAAM,CAAC;QAE1D,IAAI0B,UAAU,KAAKnC,IAAI,EAAE;UACvB,OAAO,IAAIkB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;QACjC;QAEA,OAAOgC,UAAU,CAACjC,GAAG,EAAEC,MAAM,EAAE0B,UAAU,CAAC;MAC5C;IAEA,KAAK,WAAW;IAChB,KAAK,QAAQ;MAAE;QACb,IAAIA,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAI,qBAAY,EAAC/B,GAAG,EAAEC,MAAM,CAAC;QAE1D,IAAI0B,UAAU,KAAKnC,IAAI,EAAE;UACvB,OAAO,IAAIkB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;QACjC;QAEA,OAAOiC,UAAU,CAAClC,GAAG,EAAEC,MAAM,EAAE0B,UAAU,CAAC;MAC5C;IAEA,KAAK,MAAM;MAAE;QACX,IAAIQ,iBAAiB;QACrB,CAAC;UAAElC,MAAM;UAAEO,KAAK,EAAE2B;QAAkB,CAAC,GAAG,IAAAjC,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAE9D,IAAIkC,iBAAiB,KAAK,CAAC,EAAE;UAC3B,OAAO,IAAIzB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;QACjC;;QAEA;QACA,CAAC;UAAEA;QAAO,CAAC,GAAGiC,UAAU,CAAClC,GAAG,EAAEC,MAAM,EAAEkC,iBAAiB,CAAC;;QAExD;QACA,CAAC;UAAElC;QAAO,CAAC,GAAGiC,UAAU,CAAClC,GAAG,EAAEC,MAAM,EAAE,CAAC,CAAC;QAExC,IAAI0B,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAP,qBAAY,EAACpB,GAAG,EAAEC,MAAM,CAAC;QAE1D,OAAO+B,SAAS,CAAChC,GAAG,EAAEC,MAAM,EAAE0B,UAAU,EAAEJ,QAAQ,CAACO,SAAS,CAAED,QAAS,CAAC;MAC1E;IAEA,KAAK,OAAO;MAAE;QACZ,IAAIM,iBAAiB;QACrB,CAAC;UAAElC,MAAM;UAAEO,KAAK,EAAE2B;QAAkB,CAAC,GAAG,IAAAjC,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAE9D,IAAIkC,iBAAiB,KAAK,CAAC,EAAE;UAC3B,OAAO,IAAIzB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;QACjC;;QAEA;QACA,CAAC;UAAEA;QAAO,CAAC,GAAGiC,UAAU,CAAClC,GAAG,EAAEC,MAAM,EAAEkC,iBAAiB,CAAC;;QAExD;QACA,CAAC;UAAElC;QAAO,CAAC,GAAGiC,UAAU,CAAClC,GAAG,EAAEC,MAAM,EAAE,CAAC,CAAC;QAExC,IAAI0B,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAP,qBAAY,EAACpB,GAAG,EAAEC,MAAM,CAAC;QAE1D,OAAOgC,UAAU,CAACjC,GAAG,EAAEC,MAAM,EAAE0B,UAAU,CAAC;MAC5C;IAEA,KAAK,OAAO;MAAE;QACZ,IAAIQ,iBAAiB;QACrB,CAAC;UAAElC,MAAM;UAAEO,KAAK,EAAE2B;QAAkB,CAAC,GAAG,IAAAjC,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAE9D,IAAIkC,iBAAiB,KAAK,CAAC,EAAE;UAC3B,OAAO,IAAIzB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;QACjC;;QAEA;QACA,CAAC;UAAEA;QAAO,CAAC,GAAGiC,UAAU,CAAClC,GAAG,EAAEC,MAAM,EAAEkC,iBAAiB,CAAC;;QAExD;QACA,CAAC;UAAElC;QAAO,CAAC,GAAGiC,UAAU,CAAClC,GAAG,EAAEC,MAAM,EAAE,CAAC,CAAC;QAExC,IAAI0B,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAP,qBAAY,EAACpB,GAAG,EAAEC,MAAM,CAAC;QAE1D,OAAOiC,UAAU,CAAClC,GAAG,EAAEC,MAAM,EAAE0B,UAAU,CAAC;MAC5C;IAEA,KAAK,eAAe;MAAE;QACpB,OAAOS,iBAAiB,CAACpC,GAAG,EAAEC,MAAM,EAAEuB,OAAO,CAACa,MAAM,CAAC;MACvD;IAEA,KAAK,UAAU;MAAE;QACf,OAAOC,YAAY,CAACtC,GAAG,EAAEC,MAAM,EAAEuB,OAAO,CAACa,MAAM,CAAC;MAClD;IAEA,KAAK,WAAW;MAAE;QAChB,IAAIV,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAzB,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAEvD,QAAQ0B,UAAU;UAChB,KAAK,CAAC;YACJ,OAAO,IAAIjB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;UAEjC,KAAK,CAAC;YACJ,OAAOmC,iBAAiB,CAACpC,GAAG,EAAEC,MAAM,EAAEuB,OAAO,CAACa,MAAM,CAAC;UACvD,KAAK,CAAC;YACJ,OAAOC,YAAY,CAACtC,GAAG,EAAEC,MAAM,EAAEuB,OAAO,CAACa,MAAM,CAAC;UAElD;YACE,MAAM,IAAIT,KAAK,CAAC,yBAAyB,GAAGD,UAAU,GAAG,gBAAgB,CAAC;QAC9E;MACF;IAEA,KAAK,MAAM;MAAE;QACX,IAAIA,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAzB,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAEvD,IAAI0B,UAAU,KAAK,CAAC,EAAE;UACpB,OAAO,IAAIjB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;QACjC;QAEA,OAAOsC,QAAQ,CAACvC,GAAG,EAAEC,MAAM,EAAE0B,UAAU,EAAEJ,QAAQ,CAACiB,KAAK,EAAGhB,OAAO,CAACa,MAAM,CAAC;MAC3E;IAEA,KAAK,MAAM;MAAE;QACX,IAAIV,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAzB,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAEvD,IAAI0B,UAAU,KAAK,CAAC,EAAE;UACpB,OAAO,IAAIjB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;QACjC;QAEA,OAAOwC,QAAQ,CAACzC,GAAG,EAAEC,MAAM,EAAEuB,OAAO,CAACa,MAAM,CAAC;MAC9C;IAEA,KAAK,WAAW;MAAE;QAChB,IAAIV,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAzB,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAEvD,IAAI0B,UAAU,KAAK,CAAC,EAAE;UACpB,OAAO,IAAIjB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;QACjC;QAEA,OAAOyC,aAAa,CAAC1C,GAAG,EAAEC,MAAM,EAAE0B,UAAU,EAAEJ,QAAQ,CAACiB,KAAK,EAAGhB,OAAO,CAACa,MAAM,CAAC;MAChF;IAEA,KAAK,gBAAgB;MAAE;QACrB,IAAIV,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAzB,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAEvD,IAAI0B,UAAU,KAAK,CAAC,EAAE;UACpB,OAAO,IAAIjB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;QACjC;QAEA,OAAO0C,kBAAkB,CAAC3C,GAAG,EAAEC,MAAM,EAAE0B,UAAU,EAAEJ,QAAQ,CAACiB,KAAM,CAAC;MACrE;IAEA,KAAK,UAAU;IACf,KAAK,UAAU;MAAE;QACf,IAAIb,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAzB,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAEvD,IAAI0B,UAAU,KAAK,CAAC,EAAE;UACpB,OAAO,IAAIjB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;QACjC;QAEA,OAAO2C,WAAW,CAAC5C,GAAG,EAAEC,MAAM,EAAE0B,UAAU,EAAEJ,QAAQ,CAACsB,SAAS,EAAGtB,QAAQ,CAACiB,KAAM,CAAC;MACnF;IAEA,KAAK,kBAAkB;MAAE;QACvB,IAAIb,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAzB,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAEvD,QAAQ0B,UAAU;UAChB,KAAK,CAAC;YACJ,OAAO,IAAIjB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;UAEjC,KAAK,IAAI;YACP,OAAO6C,oBAAoB,CAAC9C,GAAG,EAAEC,MAAM,EAAEuB,OAAO,CAAC;UAEnD;YACE,MAAM,IAAII,KAAK,CAAC,IAAAmB,kBAAO,EAAC,0BAA0B,EAAEpB,UAAU,GAAI,CAAC,CAAC,CAAC;QACzE;MACF;IAEA,KAAK,SAAS;MAAE;QACd,IAAIA,UAAU;QACd,CAAC;UAAE1B,MAAM;UAAEO,KAAK,EAAEmB;QAAW,CAAC,GAAG,IAAAP,qBAAY,EAACpB,GAAG,EAAEC,MAAM,CAAC;QAE1D,IAAI0B,UAAU,KAAK,CAAC,EAAE;UACpB,OAAO,IAAIjB,eAAM,CAAC,IAAI,EAAET,MAAM,CAAC;QACjC;QAEA,OAAO+C,WAAW,CAAChD,GAAG,EAAEC,MAAM,EAAEuB,OAAO,EAAEG,UAAU,CAAC;MACtD;IAEA;MAAS;QACP,MAAM,IAAIC,KAAK,CAAC,eAAe,CAAC;MAClC;EACF;AACF;AAEA,SAASqB,WAAWA,CAAC1B,QAAkB,EAAE;EACvC,QAAQA,QAAQ,CAACE,IAAI,CAACC,IAAI;IACxB,KAAK,SAAS;IACd,KAAK,UAAU;IACf,KAAK,WAAW;MAAE;QAChB,OAAOH,QAAQ,CAACI,UAAU,KAAKlC,GAAG;MACpC;IAEA,KAAK,KAAK;MAAE;QACV,OAAO,IAAI;MACb;IAEA,KAAK,KAAK;MAAE;QACV,OAAO,IAAI;MACb;EACF;AACF;AAEA,SAASqD,oBAAoBA,CAAC9C,GAAW,EAAEC,MAAc,EAAEuB,OAAsB,EAAkB;EACjG,IAAI0B,IAAI;EACR,CAAC;IAAE1C,KAAK,EAAE0C,IAAI;IAAEjD;EAAO,CAAC,GAAGiC,UAAU,CAAClC,GAAG,EAAEC,MAAM,EAAE,IAAI,CAAC;EAExD,OAAO,IAAIS,eAAM,CAACc,OAAO,CAAC2B,cAAc,GAAG,IAAAC,iCAAqB,EAACF,IAAI,CAAC,GAAG,IAAAG,iCAAqB,EAACH,IAAI,CAAC,EAAEjD,MAAM,CAAC;AAC/G;AAEA,SAAS2C,WAAWA,CAAC5C,GAAW,EAAEC,MAAc,EAAE0B,UAAkB,EAAE2B,UAAkB,EAAEd,KAAa,EAAkB;EACvH,IAAIe,IAAI;EACR,CAAC;IAAEtD,MAAM;IAAEO,KAAK,EAAE+C;EAAK,CAAC,GAAG,IAAArD,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;EAEjDsD,IAAI,GAAGA,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAE1B,IAAI/C,KAAK;EACT,IAAImB,UAAU,KAAK,CAAC,EAAE;IACpB,CAAC;MAAE1B,MAAM;MAAEO;IAAM,CAAC,GAAG,IAAAY,qBAAY,EAACpB,GAAG,EAAEC,MAAM,CAAC;EAChD,CAAC,MAAM,IAAI0B,UAAU,KAAK,CAAC,EAAE;IAC3B,CAAC;MAAE1B,MAAM;MAAEO;IAAM,CAAC,GAAG,IAAAgD,yBAAgB,EAACxD,GAAG,EAAEC,MAAM,CAAC;EACpD,CAAC,MAAM,IAAI0B,UAAU,KAAK,EAAE,EAAE;IAC5B,CAAC;MAAE1B,MAAM;MAAEO;IAAM,CAAC,GAAG,IAAAiD,yBAAgB,EAACzD,GAAG,EAAEC,MAAM,CAAC;EACpD,CAAC,MAAM,IAAI0B,UAAU,KAAK,EAAE,EAAE;IAC5B,CAAC;MAAE1B,MAAM;MAAEO;IAAM,CAAC,GAAG,IAAAkD,0BAAiB,EAAC1D,GAAG,EAAEC,MAAM,CAAC;EACrD,CAAC,MAAM;IACL,MAAM,IAAI2B,KAAK,CAAC,IAAAmB,kBAAO,EAAC,mCAAmC,EAAEpB,UAAU,CAAC,CAAC;EAC3E;EAEA,OAAO,IAAIjB,eAAM,CAAEF,KAAK,GAAG+C,IAAI,GAAII,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEpB,KAAK,CAAC,EAAEvC,MAAM,CAAC;AACjE;AAEA,SAAS+C,WAAWA,CAAChD,GAAW,EAAEC,MAAc,EAAEuB,OAAsB,EAAEG,UAAkB,EAAmB;EAC7G,IAAIkC,QAAQ;EACZ,CAAC;IAAErD,KAAK,EAAEqD,QAAQ;IAAE5D;EAAO,CAAC,GAAG,IAAAC,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;EAErD,MAAMwB,IAAI,GAAGqC,cAAI,CAACD,QAAQ,CAAC;EAE3B,IAAIE,SAAS;EACb,CAAC;IAAEvD,KAAK,EAAEuD,SAAS;IAAE9D;EAAO,CAAC,GAAG,IAAAC,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;EAEtD0B,UAAU,GAAGA,UAAU,GAAGoC,SAAS,GAAG,CAAC;EAEvC,QAAQtC,IAAI,CAACC,IAAI;IACf,KAAK,kBAAkB;MACrB,OAAOoB,oBAAoB,CAAC9C,GAAG,EAAEC,MAAM,EAAEuB,OAAO,CAAC;IAEnD,KAAK,KAAK;MACR,OAAOH,OAAO,CAACrB,GAAG,EAAEC,MAAM,CAAC;IAE7B,KAAK,SAAS;MACZ,OAAOF,WAAW,CAACC,GAAG,EAAEC,MAAM,CAAC;IAEjC,KAAK,UAAU;MACb,OAAOE,YAAY,CAACH,GAAG,EAAEC,MAAM,CAAC;IAElC,KAAK,KAAK;MACR,OAAOI,OAAO,CAACL,GAAG,EAAEC,MAAM,CAAC;IAE7B,KAAK,QAAQ;MACX,OAAOM,UAAU,CAACP,GAAG,EAAEC,MAAM,CAAC;IAEhC,KAAK,eAAe;MAClB,OAAOmC,iBAAiB,CAACpC,GAAG,EAAEC,MAAM,EAAEuB,OAAO,CAACa,MAAM,CAAC;IAEvD,KAAK,UAAU;MACb,OAAOC,YAAY,CAACtC,GAAG,EAAEC,MAAM,EAAEuB,OAAO,CAACa,MAAM,CAAC;IAElD,KAAK,MAAM;MACT,OAAOzB,QAAQ,CAACZ,GAAG,EAAEC,MAAM,CAAC;IAE9B,KAAK,OAAO;MACV,OAAOa,SAAS,CAACd,GAAG,EAAEC,MAAM,CAAC;IAE/B,KAAK,YAAY;MACf,OAAOe,cAAc,CAAChB,GAAG,EAAEC,MAAM,CAAC;IAEpC,KAAK,OAAO;MACV,OAAOgB,SAAS,CAACjB,GAAG,EAAEC,MAAM,CAAC;IAE/B,KAAK,MAAM;MACT,OAAOwC,QAAQ,CAACzC,GAAG,EAAEC,MAAM,EAAEuB,OAAO,CAACa,MAAM,CAAC;IAE9C,KAAK,MAAM;MAAE;QACX,IAAIG,KAAK;QACT,CAAC;UAAEhC,KAAK,EAAEgC,KAAK;UAAEvC;QAAO,CAAC,GAAG,IAAAC,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAElD,OAAOsC,QAAQ,CAACvC,GAAG,EAAEC,MAAM,EAAE0B,UAAU,EAAEa,KAAK,EAAEhB,OAAO,CAACa,MAAM,CAAC;MACjE;IAEA,KAAK,WAAW;MAAE;QAChB,IAAIG,KAAK;QACT,CAAC;UAAEhC,KAAK,EAAEgC,KAAK;UAAEvC;QAAO,CAAC,GAAG,IAAAC,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAElD,OAAOyC,aAAa,CAAC1C,GAAG,EAAEC,MAAM,EAAE0B,UAAU,EAAEa,KAAK,EAAEhB,OAAO,CAACa,MAAM,CAAC;MACtE;IAEA,KAAK,gBAAgB;MAAE;QACrB,IAAIG,KAAK;QACT,CAAC;UAAEhC,KAAK,EAAEgC,KAAK;UAAEvC;QAAO,CAAC,GAAG,IAAAC,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAElD,OAAO0C,kBAAkB,CAAC3C,GAAG,EAAEC,MAAM,EAAE0B,UAAU,EAAEa,KAAK,CAAC;MAC3D;IAEA,KAAK,WAAW;IAChB,KAAK,QAAQ;MAAE;QACb;QACA,CAAC;UAAEvC;QAAO,CAAC,GAAG,IAAA8B,qBAAY,EAAC/B,GAAG,EAAEC,MAAM,CAAC;QAEvC,OAAOiC,UAAU,CAAClC,GAAG,EAAEC,MAAM,EAAE0B,UAAU,CAAC;MAC5C;IAEA,KAAK,UAAU;IACf,KAAK,UAAU;MAAE;QACf,IAAIkB,SAAS;QACb,CAAC;UAAErC,KAAK,EAAEqC,SAAS;UAAE5C;QAAO,CAAC,GAAG,IAAAC,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAEtD,IAAIuC,KAAK;QACT,CAAC;UAAEhC,KAAK,EAAEgC,KAAK;UAAEvC;QAAO,CAAC,GAAG,IAAAC,kBAAS,EAACF,GAAG,EAAEC,MAAM,CAAC;QAElD,OAAO2C,WAAW,CAAC5C,GAAG,EAAEC,MAAM,EAAE0B,UAAU,EAAEkB,SAAS,EAAEL,KAAK,CAAC;MAC/D;IAEA,KAAK,SAAS;IACd,KAAK,MAAM;MAAE;QACX;QACA,CAAC;UAAEvC;QAAO,CAAC,GAAG,IAAA8B,qBAAY,EAAC/B,GAAG,EAAEC,MAAM,CAAC;QAEvC,IAAI6B,SAAS;QACb,CAAC;UAAEtB,KAAK,EAAEsB,SAAS;UAAE7B;QAAO,CAAC,GAAG,IAAA+D,6BAAa,EAAChE,GAAG,EAAEC,MAAM,CAAC;QAE1D,OAAO+B,SAAS,CAAChC,GAAG,EAAEC,MAAM,EAAE0B,UAAU,EAAEG,SAAS,CAACD,QAAS,CAAC;MAChE;IAEA,KAAK,UAAU;IACf,KAAK,OAAO;MAAE;QACZ;QACA,CAAC;UAAE5B;QAAO,CAAC,GAAG,IAAA8B,qBAAY,EAAC/B,GAAG,EAAEC,MAAM,CAAC;;QAEvC;QACA,CAAC;UAAEA;QAAO,CAAC,GAAG,IAAA+D,6BAAa,EAAChE,GAAG,EAAEC,MAAM,CAAC;QAExC,OAAOgC,UAAU,CAACjC,GAAG,EAAEC,MAAM,EAAE0B,UAAU,CAAC;MAC5C;IAEA;MACE,MAAM,IAAIC,KAAK,CAAC,eAAe,CAAC;EACpC;AACF;AAEA,SAASM,UAAUA,CAAClC,GAAW,EAAEC,MAAc,EAAE0B,UAAkB,EAAkB;EACnF,IAAI3B,GAAG,CAACiE,MAAM,GAAGhE,MAAM,GAAG0B,UAAU,EAAE;IACpC,MAAM,IAAIuC,2BAAkB,CAACjE,MAAM,GAAG0B,UAAU,CAAC;EACnD;EAEA,OAAO,IAAIjB,eAAM,CAACV,GAAG,CAACmE,KAAK,CAAClE,MAAM,EAAEA,MAAM,GAAG0B,UAAU,CAAC,EAAE1B,MAAM,GAAG0B,UAAU,CAAC;AAChF;AAEA,SAASK,SAASA,CAAChC,GAAW,EAAEC,MAAc,EAAE0B,UAAkB,EAAEE,QAAgB,EAAkB;EACpG,IAAI7B,GAAG,CAACiE,MAAM,GAAGhE,MAAM,GAAG0B,UAAU,EAAE;IACpC,MAAM,IAAIuC,2BAAkB,CAACjE,MAAM,GAAG0B,UAAU,CAAC;EACnD;EAEA,OAAO,IAAIjB,eAAM,CAAC0D,kBAAK,CAACC,MAAM,CAACrE,GAAG,CAACmE,KAAK,CAAClE,MAAM,EAAEA,MAAM,GAAG0B,UAAU,CAAC,EAAEE,QAAQ,IAAI/B,gBAAgB,CAAC,EAAEG,MAAM,GAAG0B,UAAU,CAAC;AAC5H;AAEA,SAASM,UAAUA,CAACjC,GAAW,EAAEC,MAAc,EAAE0B,UAAkB,EAAkB;EACnF,IAAI3B,GAAG,CAACiE,MAAM,GAAGhE,MAAM,GAAG0B,UAAU,EAAE;IACpC,MAAM,IAAIuC,2BAAkB,CAACjE,MAAM,GAAG0B,UAAU,CAAC;EACnD;EAEA,OAAO,IAAIjB,eAAM,CAACV,GAAG,CAACW,QAAQ,CAAC,MAAM,EAAEV,MAAM,EAAEA,MAAM,GAAG0B,UAAU,CAAC,EAAE1B,MAAM,GAAG0B,UAAU,CAAC;AAC3F;AAEA,eAAe2C,aAAaA,CAACC,MAAc,EAA4B;EACrE,OAAOA,MAAM,CAACC,MAAM,CAACP,MAAM,GAAGM,MAAM,CAACE,QAAQ,GAAG,CAAC,EAAE;IACjD,MAAMF,MAAM,CAACG,YAAY,CAAC,CAAC;EAC7B;EAEA,MAAMC,cAAc,GAAGJ,MAAM,CAACC,MAAM,CAACI,eAAe,CAACL,MAAM,CAACE,QAAQ,CAAC;EACrEF,MAAM,CAACE,QAAQ,IAAI,CAAC;EAEpB,IAAIE,cAAc,KAAK/E,QAAQ,EAAE;IAC/B,OAAO,IAAI;EACb;EAEA,MAAMiF,MAAgB,GAAG,EAAE;EAC3B,IAAIC,aAAa,GAAG,CAAC;EAErB,OAAO,IAAI,EAAE;IACX,OAAOP,MAAM,CAACC,MAAM,CAACP,MAAM,GAAGM,MAAM,CAACE,QAAQ,GAAG,CAAC,EAAE;MACjD,MAAMF,MAAM,CAACG,YAAY,CAAC,CAAC;IAC7B;IAEA,MAAMK,WAAW,GAAGR,MAAM,CAACC,MAAM,CAACpD,YAAY,CAACmD,MAAM,CAACE,QAAQ,CAAC;IAC/DF,MAAM,CAACE,QAAQ,IAAI,CAAC;IAEpB,IAAI,CAACM,WAAW,EAAE;MAChB;IACF;IAEA,OAAOR,MAAM,CAACC,MAAM,CAACP,MAAM,GAAGM,MAAM,CAACE,QAAQ,GAAGM,WAAW,EAAE;MAC3D,MAAMR,MAAM,CAACG,YAAY,CAAC,CAAC;IAC7B;IAEAG,MAAM,CAACG,IAAI,CAACT,MAAM,CAACC,MAAM,CAACL,KAAK,CAACI,MAAM,CAACE,QAAQ,EAAEF,MAAM,CAACE,QAAQ,GAAGM,WAAW,CAAC,CAAC;IAChFR,MAAM,CAACE,QAAQ,IAAIM,WAAW;IAC9BD,aAAa,IAAIC,WAAW;EAC9B;EAEA,IAAIJ,cAAc,KAAK9E,eAAe,EAAE;IACtC,IAAIiF,aAAa,KAAKG,MAAM,CAACN,cAAc,CAAC,EAAE;MAC5C,MAAM,IAAI/C,KAAK,CAAC,+DAA+D,GAAG+C,cAAc,GAAG,YAAY,GAAGG,aAAa,GAAG,QAAQ,CAAC;IAC7I;EACF;EAEA,OAAOD,MAAM;AACf;AAEA,SAASzC,iBAAiBA,CAACpC,GAAW,EAAEC,MAAc,EAAEoC,MAAe,EAAgB;EACrF,IAAI6C,IAAI;EACR,CAAC;IAAEjF,MAAM;IAAEO,KAAK,EAAE0E;EAAK,CAAC,GAAG,IAAAnD,qBAAY,EAAC/B,GAAG,EAAEC,MAAM,CAAC;EAEpD,IAAIkF,OAAO;EACX,CAAC;IAAElF,MAAM;IAAEO,KAAK,EAAE2E;EAAQ,CAAC,GAAG,IAAApD,qBAAY,EAAC/B,GAAG,EAAEC,MAAM,CAAC;EAEvD,IAAIO,KAAK;EACT,IAAI6B,MAAM,EAAE;IACV7B,KAAK,GAAG,IAAI4E,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAGH,IAAI,EAAE,CAAC,EAAEC,OAAO,CAAC,CAAC;EAC3D,CAAC,MAAM;IACL3E,KAAK,GAAG,IAAI4E,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAGF,IAAI,EAAE,CAAC,EAAEC,OAAO,CAAC;EACjD;EAEA,OAAO,IAAIzE,eAAM,CAACF,KAAK,EAAEP,MAAM,CAAC;AAClC;AAEA,SAASqC,YAAYA,CAACtC,GAAW,EAAEC,MAAc,EAAEoC,MAAe,EAAgB;EAChF,IAAI6C,IAAI;EACR,CAAC;IAAEjF,MAAM;IAAEO,KAAK,EAAE0E;EAAK,CAAC,GAAG,IAAA5E,oBAAW,EAACN,GAAG,EAAEC,MAAM,CAAC;EAEnD,IAAIqF,uBAAuB;EAC3B,CAAC;IAAErF,MAAM;IAAEO,KAAK,EAAE8E;EAAwB,CAAC,GAAG,IAAAhF,oBAAW,EAACN,GAAG,EAAEC,MAAM,CAAC;EAEtE,MAAMsF,YAAY,GAAG5B,IAAI,CAAC6B,KAAK,CAACF,uBAAuB,GAAG5F,iBAAiB,CAAC;EAE5E,IAAIc,KAAK;EACT,IAAI6B,MAAM,EAAE;IACV7B,KAAK,GAAG,IAAI4E,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAGH,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEK,YAAY,CAAC,CAAC;EACtE,CAAC,MAAM;IACL/E,KAAK,GAAG,IAAI4E,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAGF,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEK,YAAY,CAAC;EAC5D;EAEA,OAAO,IAAI7E,eAAM,CAACF,KAAK,EAAEP,MAAM,CAAC;AAClC;AAMA,SAASsC,QAAQA,CAACvC,GAAW,EAAEC,MAAc,EAAE0B,UAAkB,EAAEa,KAAa,EAAEH,MAAe,EAAoC;EACnI,IAAI7B,KAAK;EAET,QAAQmB,UAAU;IAChB,KAAK,CAAC;MAAE;QACN,CAAC;UAAEnB,KAAK;UAAEP;QAAO,CAAC,GAAG,IAAAwF,qBAAY,EAACzF,GAAG,EAAEC,MAAM,CAAC;QAC9C;MACF;IAEA,KAAK,CAAC;MAAE;QACN,CAAC;UAAEO,KAAK;UAAEP;QAAO,CAAC,GAAG,IAAAmB,qBAAY,EAACpB,GAAG,EAAEC,MAAM,CAAC;QAC9C;MACF;IAEA,KAAK,CAAC;MAAE;QACN,CAAC;UAAEO,KAAK;UAAEP;QAAO,CAAC,GAAG,IAAAyF,qBAAY,EAAC1F,GAAG,EAAEC,MAAM,CAAC;QAC9C;MACF;IAEA;MAAS;QACP,MAAM,IAAI2B,KAAK,CAAC,aAAa,CAAC;MAChC;EACF;EAEA,IAAIY,KAAK,GAAG,CAAC,EAAE;IACb,KAAK,IAAImD,CAAC,GAAGnD,KAAK,EAAEmD,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9BnF,KAAK,IAAI,EAAE;IACb;EACF;EAEA,IAAIoF,IAAI;EACR,IAAIvD,MAAM,EAAE;IACVuD,IAAI,GAAG,IAAIR,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE7E,KAAK,GAAG,KAAK,CAAC,CAA6B;EAC3F,CAAC,MAAM;IACLoF,IAAI,GAAG,IAAIR,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE5E,KAAK,GAAG,KAAK,CAA6B;EACjF;EACAqF,MAAM,CAACC,cAAc,CAACF,IAAI,EAAE,kBAAkB,EAAE;IAC9CG,UAAU,EAAE,KAAK;IACjBvF,KAAK,EAAGA,KAAK,GAAG,KAAK,GAAImD,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE,CAAC;EACzC,CAAC,CAAC;EAEF,OAAO,IAAIlD,eAAM,CAACkF,IAAI,EAAE3F,MAAM,CAAC;AACjC;AAEA,SAASwC,QAAQA,CAACzC,GAAW,EAAEC,MAAc,EAAEoC,MAAe,EAAgB;EAC5E,IAAI6C,IAAI;EACR,CAAC;IAAEjF,MAAM;IAAEO,KAAK,EAAE0E;EAAK,CAAC,GAAG,IAAAO,qBAAY,EAACzF,GAAG,EAAEC,MAAM,CAAC;EAEpD,IAAIoC,MAAM,EAAE;IACV,OAAO,IAAI3B,eAAM,CAAC,IAAI0E,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAEH,IAAI,GAAG,MAAM,CAAC,CAAC,EAAEjF,MAAM,CAAC;EACvE,CAAC,MAAM;IACL,OAAO,IAAIS,eAAM,CAAC,IAAI0E,IAAI,CAAC,IAAI,EAAE,CAAC,EAAEF,IAAI,GAAG,MAAM,CAAC,EAAEjF,MAAM,CAAC;EAC7D;AACF;AAEA,SAASyC,aAAaA,CAAC1C,GAAW,EAAEC,MAAc,EAAE0B,UAAkB,EAAEa,KAAa,EAAEH,MAAe,EAAoC;EACxI,IAAI2D,IAAI;EACR,CAAC;IAAE/F,MAAM;IAAEO,KAAK,EAAEwF;EAAK,CAAC,GAAGzD,QAAQ,CAACvC,GAAG,EAAEC,MAAM,EAAE0B,UAAU,GAAG,CAAC,EAAEa,KAAK,EAAEH,MAAM,CAAC;EAE/E,IAAI6C,IAAI;EACR,CAAC;IAAEjF,MAAM;IAAEO,KAAK,EAAE0E;EAAK,CAAC,GAAG,IAAAO,qBAAY,EAACzF,GAAG,EAAEC,MAAM,CAAC;EAEpD,IAAI2F,IAAI;EACR,IAAIvD,MAAM,EAAE;IACVuD,IAAI,GAAG,IAAIR,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAEH,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAACc,IAAI,CAAC,CAA6B;EAC/F,CAAC,MAAM;IACLJ,IAAI,GAAG,IAAIR,IAAI,CAAC,IAAI,EAAE,CAAC,EAAEF,IAAI,GAAG,MAAM,EAAEc,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAED,IAAI,CAACE,UAAU,CAAC,CAAC,EAAEF,IAAI,CAACG,UAAU,CAAC,CAAC,EAAEH,IAAI,CAACI,eAAe,CAAC,CAAC,CAA6B;EACpJ;EACAP,MAAM,CAACC,cAAc,CAACF,IAAI,EAAE,kBAAkB,EAAE;IAC9CG,UAAU,EAAE,KAAK;IACjBvF,KAAK,EAAEwF,IAAI,CAACK;EACd,CAAC,CAAC;EAEF,OAAO,IAAI3F,eAAM,CAACkF,IAAI,EAAE3F,MAAM,CAAC;AACjC;AAEA,SAAS0C,kBAAkBA,CAAC3C,GAAW,EAAEC,MAAc,EAAE0B,UAAkB,EAAEa,KAAa,EAAoC;EAC5H,IAAIwD,IAAI;EACR,CAAC;IAAE/F,MAAM;IAAEO,KAAK,EAAEwF;EAAK,CAAC,GAAGzD,QAAQ,CAACvC,GAAG,EAAEC,MAAM,EAAE0B,UAAU,GAAG,CAAC,EAAEa,KAAK,EAAE,IAAI,CAAC;EAE7E,IAAI0C,IAAI;EACR,CAAC;IAAEjF,MAAM;IAAEO,KAAK,EAAE0E;EAAK,CAAC,GAAG,IAAAO,qBAAY,EAACzF,GAAG,EAAEC,MAAM,CAAC;;EAEpD;EACA,CAAC;IAAEA;EAAO,CAAC,GAAG,IAAA8B,qBAAY,EAAC/B,GAAG,EAAEC,MAAM,CAAC;EAEvC,MAAM2F,IAAI,GAAG,IAAIR,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAEH,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAACc,IAAI,CAAC,CAA6B;EACnGH,MAAM,CAACC,cAAc,CAACF,IAAI,EAAE,kBAAkB,EAAE;IAC9CG,UAAU,EAAE,KAAK;IACjBvF,KAAK,EAAEwF,IAAI,CAACK;EACd,CAAC,CAAC;EACF,OAAO,IAAI3F,eAAM,CAACkF,IAAI,EAAE3F,MAAM,CAAC;AACjC;AAEAqG,MAAM,CAACC,OAAO,CAACjF,SAAS,GAAGA,SAAS;AACpCgF,MAAM,CAACC,OAAO,CAACtD,WAAW,GAAGA,WAAW;AACxCqD,MAAM,CAACC,OAAO,CAACjC,aAAa,GAAGA,aAAa"}
{"version": 3, "file": "pipeline.js", "sourceRoot": "", "sources": ["../../src/pipeline.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAgGlC,kDAEC;AA/FD,+DAA0F;AAyF1F;;;GAGG;AACH,SAAgB,mBAAmB;IACjC,OAAO,IAAA,qCAAsB,GAAc,CAAC;AAC9C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient, PipelineRequest, PipelineResponse, SendRequest } from \"./interfaces.js\";\nimport { createEmptyPipeline as tspCreateEmptyPipeline } from \"@typespec/ts-http-runtime\";\n\n/**\n * Policies are executed in phases.\n * The execution order is:\n * 1. Serialize Phase\n * 2. Policies not in a phase\n * 3. Deserialize Phase\n * 4. Retry Phase\n * 5. Sign Phase\n */\nexport type PipelinePhase = \"Deserialize\" | \"Serialize\" | \"Retry\" | \"Sign\";\n\n/**\n * Options when adding a policy to the pipeline.\n * Used to express dependencies on other policies.\n */\nexport interface AddPolicyOptions {\n  /**\n   * Policies that this policy must come before.\n   */\n  beforePolicies?: string[];\n  /**\n   * Policies that this policy must come after.\n   */\n  afterPolicies?: string[];\n  /**\n   * The phase that this policy must come after.\n   */\n  afterPhase?: PipelinePhase;\n  /**\n   * The phase this policy belongs to.\n   */\n  phase?: PipelinePhase;\n}\n\n/**\n * A pipeline policy manipulates a request as it travels through the pipeline.\n * It is conceptually a middleware that is allowed to modify the request before\n * it is made as well as the response when it is received.\n */\nexport interface PipelinePolicy {\n  /**\n   * The policy name. Must be a unique string in the pipeline.\n   */\n  name: string;\n  /**\n   * The main method to implement that manipulates a request/response.\n   * @param request - The request being performed.\n   * @param next - The next policy in the pipeline. Must be called to continue the pipeline.\n   */\n  sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse>;\n}\n\n/**\n * Represents a pipeline for making a HTTP request to a URL.\n * Pipelines can have multiple policies to manage manipulating each request\n * before and after it is made to the server.\n */\nexport interface Pipeline {\n  /**\n   * Add a new policy to the pipeline.\n   * @param policy - A policy that manipulates a request.\n   * @param options - A set of options for when the policy should run.\n   */\n  addPolicy(policy: PipelinePolicy, options?: AddPolicyOptions): void;\n  /**\n   * Remove a policy from the pipeline.\n   * @param options - Options that let you specify which policies to remove.\n   */\n  removePolicy(options: { name?: string; phase?: PipelinePhase }): PipelinePolicy[];\n  /**\n   * Uses the pipeline to make a HTTP request.\n   * @param httpClient - The HttpClient that actually performs the request.\n   * @param request - The request to be made.\n   */\n  sendRequest(httpClient: HttpClient, request: PipelineRequest): Promise<PipelineResponse>;\n  /**\n   * Returns the current set of policies in the pipeline in the order in which\n   * they will be applied to the request. Later in the list is closer to when\n   * the request is performed.\n   */\n  getOrderedPolicies(): PipelinePolicy[];\n  /**\n   * Duplicates this pipeline to allow for modifying an existing one without mutating it.\n   */\n  clone(): Pipeline;\n}\n\n/**\n * Creates a totally empty pipeline.\n * Useful for testing or creating a custom one.\n */\nexport function createEmptyPipeline(): Pipeline {\n  return tspCreateEmptyPipeline() as Pipeline;\n}\n"]}
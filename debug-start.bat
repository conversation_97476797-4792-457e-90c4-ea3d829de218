@echo off
echo ========================================
echo    RC Dashboard Debug Startup
echo ========================================
echo.

echo [1/4] Stopping any existing processes...
taskkill /f /im node.exe >nul 2>&1

echo [2/4] Starting Backend Server...
start "RC Dashboard Backend" cmd /k "cd /d backend && npm start"

echo [3/4] Waiting for backend to be ready...
:wait_loop
timeout /t 3 /nobreak >nul
curl -s http://localhost:5001/api/health >nul 2>&1
if %errorlevel% neq 0 (
    echo Backend starting, please wait...
    goto wait_loop
)

echo [4/4] Backend is ready! Opening test pages...
timeout /t 1 /nobreak >nul

echo.
echo ========================================
echo    Debug Mode Active
echo ========================================
echo.
echo Backend API: http://localhost:5001
echo.

echo Opening API test page...
start test-api.html

timeout /t 2 /nobreak >nul

echo Opening main dashboard...
start simple-dashboard.html

echo.
echo ========================================
echo    Debug Instructions
echo ========================================
echo.
echo 1. First, test APIs in the API test page
echo 2. If APIs work, the main dashboard should work too
echo 3. If APIs fail, check the console for CORS errors
echo 4. Use the Debug button in the main dashboard for more info
echo.
echo Press any key to close this window...
pause >nul

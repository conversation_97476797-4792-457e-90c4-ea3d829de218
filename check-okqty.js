const fetch = require('node-fetch');

async function checkOkQty() {
    try {
        console.log('Fetching RC data...');
        const response = await fetch('http://localhost:5001/api/rc-pending');
        const data = await response.json();
        
        if (data.success && data.data) {
            console.log(`\nFound ${data.data.length} records\n`);
            console.log('Work Order\t\tWO Qty\t\tOK Qty\t\tDifference');
            console.log('='.repeat(70));
            
            data.data.slice(0, 10).forEach(item => {
                const woQty = item.WO_Qty || 0;
                const okQty = item.TotalOkQty || 0;
                const diff = woQty - okQty;
                
                console.log(`${item.WoNo}\t\t${woQty}\t\t${okQty}\t\t${diff}`);
            });
            
            // Check if all OK quantities are 0
            const zeroOkQty = data.data.filter(item => (item.TotalOkQty || 0) === 0);
            const nonZeroOkQty = data.data.filter(item => (item.TotalOkQty || 0) > 0);
            
            console.log(`\nSummary:`);
            console.log(`- Records with OK Qty = 0: ${zeroOkQty.length}`);
            console.log(`- Records with OK Qty > 0: ${nonZeroOkQty.length}`);
            
            if (zeroOkQty.length === data.data.length) {
                console.log('\n⚠️  All OK quantities are 0 - this indicates:');
                console.log('   1. No production data in ProdnForgingStages table');
                console.log('   2. Wrong table/column names in the query');
                console.log('   3. Data not linked properly between tables');
            }
            
        } else {
            console.log('Error:', data.error || 'No data received');
        }
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

checkOkQty();

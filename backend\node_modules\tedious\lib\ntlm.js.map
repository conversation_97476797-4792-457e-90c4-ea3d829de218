{"version": 3, "file": "ntlm.js", "names": ["NTLMFlags", "NTLM_NegotiateUnicode", "NTLM_NegotiateOEM", "NTLM_RequestTarget", "NTLM_Unknown9", "NTLM_NegotiateSign", "NTLM_NegotiateSeal", "NTLM_NegotiateDatagram", "NTLM_NegotiateLanManagerKey", "NTLM_Unknown8", "NTLM_NegotiateNTLM", "NTLM_NegotiateNTOnly", "NTLM_Anonymous", "NTLM_NegotiateOemDomainSupplied", "NTLM_NegotiateOemWorkstationSupplied", "NTLM_Unknown6", "NTLM_NegotiateAlwaysSign", "NTLM_TargetTypeDomain", "NTLM_TargetTypeServer", "NTLM_TargetTypeShare", "NTLM_NegotiateExtendedSecurity", "NTLM_NegotiateIdentify", "NTLM_Unknown5", "NTLM_RequestNonNTSessionKey", "NTLM_NegotiateTargetInfo", "NTLM_Unknown4", "NTLM_NegotiateVersion", "NTLM_Unknown3", "NTLM_Unknown2", "NTLM_Unknown1", "NTLM_Negotiate128", "NTLM_NegotiateKeyExchange", "NTLM_Negotiate56", "createNTLMRequest", "options", "domain", "escape", "toUpperCase", "workstation", "type1flags", "fixedData", "<PERSON><PERSON><PERSON>", "alloc", "buffers", "offset", "write", "writeUInt8", "writeUInt32LE", "writeUInt16LE", "length", "push", "from", "concat"], "sources": ["../src/ntlm.ts"], "sourcesContent": ["const NTLMFlags = {\n  NTLM_NegotiateUnicode: 0x00000001,\n  NTLM_NegotiateOEM: 0x00000002,\n  NTLM_RequestTarget: 0x00000004,\n  NTLM_Unknown9: 0x00000008,\n  NTLM_NegotiateSign: 0x00000010,\n  NTLM_NegotiateSeal: 0x00000020,\n  NTLM_NegotiateDatagram: 0x00000040,\n  NTLM_NegotiateLanManagerKey: 0x00000080,\n  NTLM_Unknown8: 0x00000100,\n  NTLM_NegotiateNTLM: 0x00000200,\n  NTLM_NegotiateNTOnly: 0x00000400,\n  NTLM_Anonymous: 0x00000800,\n  NTLM_NegotiateOemDomainSupplied: 0x00001000,\n  NTLM_NegotiateOemWorkstationSupplied: 0x00002000,\n  NTLM_Unknown6: 0x00004000,\n  NTLM_NegotiateAlwaysSign: 0x00008000,\n  NTLM_TargetTypeDomain: 0x00010000,\n  NTLM_TargetTypeServer: 0x00020000,\n  NTLM_TargetTypeShare: 0x00040000,\n  NTLM_NegotiateExtendedSecurity: 0x00080000,\n  NTLM_NegotiateIdentify: 0x00100000,\n  NTLM_Unknown5: 0x00200000,\n  NTLM_RequestNonNTSessionKey: 0x00400000,\n  NTLM_NegotiateTargetInfo: 0x00800000,\n  NTLM_Unknown4: 0x01000000,\n  NTLM_NegotiateVersion: 0x02000000,\n  NTLM_Unknown3: 0x04000000,\n  NTLM_Unknown2: 0x08000000,\n  NTLM_Unknown1: 0x10000000,\n  NTLM_Negotiate128: 0x20000000,\n  NTLM_NegotiateKeyExchange: 0x40000000,\n  NTLM_Negotiate56: 0x80000000\n};\n\nexport function createNTLMRequest(options: { domain: string, workstation?: string }) {\n  const domain = escape(options.domain.toUpperCase());\n  const workstation = options.workstation ? escape(options.workstation.toUpperCase()) : '';\n\n  let type1flags = NTLMFlags.NTLM_NegotiateUnicode + NTLMFlags.NTLM_NegotiateOEM + NTLMFlags.NTLM_RequestTarget + NTLMFlags.NTLM_NegotiateNTLM + NTLMFlags.NTLM_NegotiateOemDomainSupplied + NTLMFlags.NTLM_NegotiateOemWorkstationSupplied + NTLMFlags.NTLM_NegotiateAlwaysSign + NTLMFlags.NTLM_NegotiateVersion + NTLMFlags.NTLM_NegotiateExtendedSecurity + NTLMFlags.NTLM_Negotiate128 + NTLMFlags.NTLM_Negotiate56;\n  if (workstation === '') {\n    type1flags -= NTLMFlags.NTLM_NegotiateOemWorkstationSupplied;\n  }\n\n  const fixedData = Buffer.alloc(40);\n  const buffers = [fixedData];\n  let offset = 0;\n\n  offset += fixedData.write('NTLMSSP', offset, 7, 'ascii');\n  offset = fixedData.writeUInt8(0, offset);\n  offset = fixedData.writeUInt32LE(1, offset);\n  offset = fixedData.writeUInt32LE(type1flags, offset);\n  offset = fixedData.writeUInt16LE(domain.length, offset);\n  offset = fixedData.writeUInt16LE(domain.length, offset);\n  offset = fixedData.writeUInt32LE(fixedData.length + workstation.length, offset);\n  offset = fixedData.writeUInt16LE(workstation.length, offset);\n  offset = fixedData.writeUInt16LE(workstation.length, offset);\n  offset = fixedData.writeUInt32LE(fixedData.length, offset);\n  offset = fixedData.writeUInt8(5, offset);\n  offset = fixedData.writeUInt8(0, offset);\n  offset = fixedData.writeUInt16LE(2195, offset);\n  offset = fixedData.writeUInt8(0, offset);\n  offset = fixedData.writeUInt8(0, offset);\n  offset = fixedData.writeUInt8(0, offset);\n  fixedData.writeUInt8(15, offset);\n\n  buffers.push(Buffer.from(workstation, 'ascii'));\n  buffers.push(Buffer.from(domain, 'ascii'));\n\n  return Buffer.concat(buffers);\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,SAAS,GAAG;EAChBC,qBAAqB,EAAE,UAAU;EACjCC,iBAAiB,EAAE,UAAU;EAC7BC,kBAAkB,EAAE,UAAU;EAC9BC,aAAa,EAAE,UAAU;EACzBC,kBAAkB,EAAE,UAAU;EAC9BC,kBAAkB,EAAE,UAAU;EAC9BC,sBAAsB,EAAE,UAAU;EAClCC,2BAA2B,EAAE,UAAU;EACvCC,aAAa,EAAE,UAAU;EACzBC,kBAAkB,EAAE,UAAU;EAC9BC,oBAAoB,EAAE,UAAU;EAChCC,cAAc,EAAE,UAAU;EAC1BC,+BAA+B,EAAE,UAAU;EAC3CC,oCAAoC,EAAE,UAAU;EAChDC,aAAa,EAAE,UAAU;EACzBC,wBAAwB,EAAE,UAAU;EACpCC,qBAAqB,EAAE,UAAU;EACjCC,qBAAqB,EAAE,UAAU;EACjCC,oBAAoB,EAAE,UAAU;EAChCC,8BAA8B,EAAE,UAAU;EAC1CC,sBAAsB,EAAE,UAAU;EAClCC,aAAa,EAAE,UAAU;EACzBC,2BAA2B,EAAE,UAAU;EACvCC,wBAAwB,EAAE,UAAU;EACpCC,aAAa,EAAE,UAAU;EACzBC,qBAAqB,EAAE,UAAU;EACjCC,aAAa,EAAE,UAAU;EACzBC,aAAa,EAAE,UAAU;EACzBC,aAAa,EAAE,UAAU;EACzBC,iBAAiB,EAAE,UAAU;EAC7BC,yBAAyB,EAAE,UAAU;EACrCC,gBAAgB,EAAE;AACpB,CAAC;AAEM,SAASC,iBAAiBA,CAACC,OAAiD,EAAE;EACnF,MAAMC,MAAM,GAAGC,MAAM,CAACF,OAAO,CAACC,MAAM,CAACE,WAAW,CAAC,CAAC,CAAC;EACnD,MAAMC,WAAW,GAAGJ,OAAO,CAACI,WAAW,GAAGF,MAAM,CAACF,OAAO,CAACI,WAAW,CAACD,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE;EAExF,IAAIE,UAAU,GAAGvC,SAAS,CAACC,qBAAqB,GAAGD,SAAS,CAACE,iBAAiB,GAAGF,SAAS,CAACG,kBAAkB,GAAGH,SAAS,CAACU,kBAAkB,GAAGV,SAAS,CAACa,+BAA+B,GAAGb,SAAS,CAACc,oCAAoC,GAAGd,SAAS,CAACgB,wBAAwB,GAAGhB,SAAS,CAAC0B,qBAAqB,GAAG1B,SAAS,CAACoB,8BAA8B,GAAGpB,SAAS,CAAC8B,iBAAiB,GAAG9B,SAAS,CAACgC,gBAAgB;EACtZ,IAAIM,WAAW,KAAK,EAAE,EAAE;IACtBC,UAAU,IAAIvC,SAAS,CAACc,oCAAoC;EAC9D;EAEA,MAAM0B,SAAS,GAAGC,MAAM,CAACC,KAAK,CAAC,EAAE,CAAC;EAClC,MAAMC,OAAO,GAAG,CAACH,SAAS,CAAC;EAC3B,IAAII,MAAM,GAAG,CAAC;EAEdA,MAAM,IAAIJ,SAAS,CAACK,KAAK,CAAC,SAAS,EAAED,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC;EACxDA,MAAM,GAAGJ,SAAS,CAACM,UAAU,CAAC,CAAC,EAAEF,MAAM,CAAC;EACxCA,MAAM,GAAGJ,SAAS,CAACO,aAAa,CAAC,CAAC,EAAEH,MAAM,CAAC;EAC3CA,MAAM,GAAGJ,SAAS,CAACO,aAAa,CAACR,UAAU,EAAEK,MAAM,CAAC;EACpDA,MAAM,GAAGJ,SAAS,CAACQ,aAAa,CAACb,MAAM,CAACc,MAAM,EAAEL,MAAM,CAAC;EACvDA,MAAM,GAAGJ,SAAS,CAACQ,aAAa,CAACb,MAAM,CAACc,MAAM,EAAEL,MAAM,CAAC;EACvDA,MAAM,GAAGJ,SAAS,CAACO,aAAa,CAACP,SAAS,CAACS,MAAM,GAAGX,WAAW,CAACW,MAAM,EAAEL,MAAM,CAAC;EAC/EA,MAAM,GAAGJ,SAAS,CAACQ,aAAa,CAACV,WAAW,CAACW,MAAM,EAAEL,MAAM,CAAC;EAC5DA,MAAM,GAAGJ,SAAS,CAACQ,aAAa,CAACV,WAAW,CAACW,MAAM,EAAEL,MAAM,CAAC;EAC5DA,MAAM,GAAGJ,SAAS,CAACO,aAAa,CAACP,SAAS,CAACS,MAAM,EAAEL,MAAM,CAAC;EAC1DA,MAAM,GAAGJ,SAAS,CAACM,UAAU,CAAC,CAAC,EAAEF,MAAM,CAAC;EACxCA,MAAM,GAAGJ,SAAS,CAACM,UAAU,CAAC,CAAC,EAAEF,MAAM,CAAC;EACxCA,MAAM,GAAGJ,SAAS,CAACQ,aAAa,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAC9CA,MAAM,GAAGJ,SAAS,CAACM,UAAU,CAAC,CAAC,EAAEF,MAAM,CAAC;EACxCA,MAAM,GAAGJ,SAAS,CAACM,UAAU,CAAC,CAAC,EAAEF,MAAM,CAAC;EACxCA,MAAM,GAAGJ,SAAS,CAACM,UAAU,CAAC,CAAC,EAAEF,MAAM,CAAC;EACxCJ,SAAS,CAACM,UAAU,CAAC,EAAE,EAAEF,MAAM,CAAC;EAEhCD,OAAO,CAACO,IAAI,CAACT,MAAM,CAACU,IAAI,CAACb,WAAW,EAAE,OAAO,CAAC,CAAC;EAC/CK,OAAO,CAACO,IAAI,CAACT,MAAM,CAACU,IAAI,CAAChB,MAAM,EAAE,OAAO,CAAC,CAAC;EAE1C,OAAOM,MAAM,CAACW,MAAM,CAACT,OAAO,CAAC;AAC/B"}
{"version": 3, "file": "multipart.js", "sourceRoot": "", "sources": ["../../../src/client/multipart.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAgLlC,sCAkBC;AAED,gDAEC;AAnMD,kDAA4C;AAC5C,sDAAsD;AACtD,+DAA8D;AAC9D,yDAAqD;AAkDrD;;GAEG;AACH,SAAS,cAAc,CAAC,UAA0B,EAAE,UAAkB;IACpE,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;QACvB,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAC3D,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,WAAW,EAAE,CACpD,CAAC;QACF,IAAI,gBAAgB,EAAE,CAAC;YACrB,OAAO,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,kBAAkB,CAAC,UAA0B;IACpD,MAAM,iBAAiB,GAAG,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;IACrE,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,4DAA4D;IAC5D,IAAI,UAAU,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;QACpC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3B,OAAO,UAAU,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC;IAE5B,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACxC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE,CAAC;QACtF,OAAO,2BAA2B,CAAC;IACrC,CAAC;IAED,IAAI,IAAI,YAAY,IAAI,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,IAAI,IAAI,0BAA0B,CAAC;IACjD,CAAC;IAED,IAAI,IAAA,4BAAY,EAAC,IAAI,CAAC,EAAE,CAAC;QACvB,OAAO,0BAA0B,CAAC;IACpC,CAAC;IAED,6GAA6G;IAC7G,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAED;;GAEG;AACH,SAAS,sBAAsB,CAAC,KAAa;IAC3C,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,qBAAqB,CAAC,UAA0B;;IACvD,MAAM,wBAAwB,GAAG,cAAc,CAAC,UAAU,EAAE,qBAAqB,CAAC,CAAC;IACnF,IAAI,wBAAwB,EAAE,CAAC;QAC7B,OAAO,wBAAwB,CAAC;IAClC,CAAC;IAED,IACE,UAAU,CAAC,eAAe,KAAK,SAAS;QACxC,UAAU,CAAC,IAAI,KAAK,SAAS;QAC7B,UAAU,CAAC,QAAQ,KAAK,SAAS,EACjC,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,eAAe,GAAG,MAAA,UAAU,CAAC,eAAe,mCAAI,WAAW,CAAC;IAElE,IAAI,WAAW,GAAG,eAAe,CAAC;IAClC,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC;QACpB,WAAW,IAAI,UAAU,sBAAsB,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;IACrE,CAAC;IAED,IAAI,QAAQ,GAAuB,SAAS,CAAC;IAC7C,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;IACjC,CAAC;SAAM,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,UAAU,CAAC,IAAI,YAAY,IAAI,EAAE,CAAC;QAC1E,MAAM,gBAAgB,GAAI,UAAU,CAAC,IAAa,CAAC,IAAI,CAAC;QACxD,IAAI,gBAAgB,KAAK,EAAE,EAAE,CAAC;YAC5B,QAAQ,GAAG,gBAAgB,CAAC;QAC9B,CAAC;IACH,CAAC;IAED,IAAI,QAAQ,EAAE,CAAC;QACb,WAAW,IAAI,cAAc,sBAAsB,CAAC,QAAQ,CAAC,EAAE,CAAC;IAClE,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,aAAa,CAAC,IAAc,EAAE,WAAyB;IAC9D,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,mBAAmB;QACnB,OAAO,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED,kFAAkF;IAClF,IAAI,IAAA,4BAAY,EAAC,IAAI,CAAC,EAAE,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE,CAAC;QACtF,OAAO,IAAA,qCAAkB,EAAC,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,0KAA0K;IAC1K,IAAI,WAAW,IAAI,iCAAiC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC;QAC/E,OAAO,IAAA,qCAAkB,EAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED,MAAM,IAAI,wBAAS,CAAC,8CAA8C,IAAI,KAAK,WAAW,EAAE,CAAC,CAAC;AAC5F,CAAC;AAED,SAAgB,aAAa,CAAC,UAA0B;;IACtD,MAAM,WAAW,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;IACnD,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC;IAC7D,MAAM,OAAO,GAAG,IAAA,kCAAiB,EAAC,MAAA,UAAU,CAAC,OAAO,mCAAI,EAAE,CAAC,CAAC;IAE5D,IAAI,WAAW,EAAE,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;IAC3C,CAAC;IACD,IAAI,kBAAkB,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,CAAC;IACzD,CAAC;IAED,MAAM,IAAI,GAAG,aAAa,CAAC,UAAU,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAEzD,OAAO;QACL,OAAO;QACP,IAAI;KACL,CAAC;AACJ,CAAC;AAED,SAAgB,kBAAkB,CAAC,KAAuB;IACxD,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;AAC7C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { BodyPart, MultipartRequestBody, RawHttpHeadersInput } from \"../interfaces.js\";\nimport { RestError } from \"../restError.js\";\nimport { createHttpHeaders } from \"../httpHeaders.js\";\nimport { stringToUint8Array } from \"../util/bytesEncoding.js\";\nimport { isBinaryBody } from \"../util/typeGuards.js\";\n\n/**\n * Describes a single part in a multipart body.\n */\nexport interface PartDescriptor {\n  /**\n   * Content type of this part. If set, this value will be used to set the Content-Type MIME header for this part, although explicitly\n   * setting the Content-Type header in the headers bag will override this value. If set to `null`, no content type will be inferred from\n   * the body field. Otherwise, the value of the Content-Type MIME header will be inferred based on the type of the body.\n   */\n  contentType?: string | null;\n\n  /**\n   * The disposition type of this part (for example, \"form-data\" for parts making up a multipart/form-data request). If set, this value\n   * will be used to set the Content-Disposition MIME header for this part, in addition to the `name` and `filename` properties.\n   * If the `name` or `filename` properties are set while `dispositionType` is left undefined, `dispositionType` will default to \"form-data\".\n   *\n   * Explicitly setting the Content-Disposition header in the headers bag will override this value.\n   */\n  dispositionType?: string;\n\n  /**\n   * The field name associated with this part. This value will be used to construct the Content-Disposition header,\n   * along with the `dispositionType` and `filename` properties, if the header has not been set in the `headers` bag.\n   */\n  name?: string;\n\n  /**\n   * The file name of the content if it is a file. This value will be used to construct the Content-Disposition header,\n   * along with the `dispositionType` and `name` properties, if the header has not been set in the `headers` bag.\n   */\n  filename?: string;\n\n  /**\n   * The multipart headers for this part of the multipart body. Values of the Content-Type and Content-Disposition headers set in the headers bag\n   * will take precedence over those computed from the request body or the contentType, dispositionType, name, and filename fields on this object.\n   */\n  headers?: RawHttpHeadersInput;\n\n  /**\n   * The body of this part of the multipart request.\n   */\n  body?: unknown;\n}\n\ntype MultipartBodyType = BodyPart[\"body\"];\n\ntype HeaderValue = RawHttpHeadersInput[string];\n\n/**\n * Get value of a header in the part descriptor ignoring case\n */\nfunction getHeaderValue(descriptor: PartDescriptor, headerName: string): HeaderValue | undefined {\n  if (descriptor.headers) {\n    const actualHeaderName = Object.keys(descriptor.headers).find(\n      (x) => x.toLowerCase() === headerName.toLowerCase(),\n    );\n    if (actualHeaderName) {\n      return descriptor.headers[actualHeaderName];\n    }\n  }\n\n  return undefined;\n}\n\nfunction getPartContentType(descriptor: PartDescriptor): HeaderValue | undefined {\n  const contentTypeHeader = getHeaderValue(descriptor, \"content-type\");\n  if (contentTypeHeader) {\n    return contentTypeHeader;\n  }\n\n  // Special value of null means content type is to be omitted\n  if (descriptor.contentType === null) {\n    return undefined;\n  }\n\n  if (descriptor.contentType) {\n    return descriptor.contentType;\n  }\n\n  const { body } = descriptor;\n\n  if (body === null || body === undefined) {\n    return undefined;\n  }\n\n  if (typeof body === \"string\" || typeof body === \"number\" || typeof body === \"boolean\") {\n    return \"text/plain; charset=UTF-8\";\n  }\n\n  if (body instanceof Blob) {\n    return body.type || \"application/octet-stream\";\n  }\n\n  if (isBinaryBody(body)) {\n    return \"application/octet-stream\";\n  }\n\n  // arbitrary non-text object -> generic JSON content type by default. We will try to JSON.stringify the body.\n  return \"application/json\";\n}\n\n/**\n * Enclose value in quotes and escape special characters, for use in the Content-Disposition header\n */\nfunction escapeDispositionField(value: string): string {\n  return JSON.stringify(value);\n}\n\nfunction getContentDisposition(descriptor: PartDescriptor): HeaderValue | undefined {\n  const contentDispositionHeader = getHeaderValue(descriptor, \"content-disposition\");\n  if (contentDispositionHeader) {\n    return contentDispositionHeader;\n  }\n\n  if (\n    descriptor.dispositionType === undefined &&\n    descriptor.name === undefined &&\n    descriptor.filename === undefined\n  ) {\n    return undefined;\n  }\n\n  const dispositionType = descriptor.dispositionType ?? \"form-data\";\n\n  let disposition = dispositionType;\n  if (descriptor.name) {\n    disposition += `; name=${escapeDispositionField(descriptor.name)}`;\n  }\n\n  let filename: string | undefined = undefined;\n  if (descriptor.filename) {\n    filename = descriptor.filename;\n  } else if (typeof File !== \"undefined\" && descriptor.body instanceof File) {\n    const filenameFromFile = (descriptor.body as File).name;\n    if (filenameFromFile !== \"\") {\n      filename = filenameFromFile;\n    }\n  }\n\n  if (filename) {\n    disposition += `; filename=${escapeDispositionField(filename)}`;\n  }\n\n  return disposition;\n}\n\nfunction normalizeBody(body?: unknown, contentType?: HeaderValue): MultipartBodyType {\n  if (body === undefined) {\n    // zero-length body\n    return new Uint8Array([]);\n  }\n\n  // binary and primitives should go straight on the wire regardless of content type\n  if (isBinaryBody(body)) {\n    return body;\n  }\n  if (typeof body === \"string\" || typeof body === \"number\" || typeof body === \"boolean\") {\n    return stringToUint8Array(String(body), \"utf-8\");\n  }\n\n  // stringify objects for JSON-ish content types e.g. application/json, application/merge-patch+json, application/vnd.oci.manifest.v1+json, application.json; charset=UTF-8\n  if (contentType && /application\\/(.+\\+)?json(;.+)?/i.test(String(contentType))) {\n    return stringToUint8Array(JSON.stringify(body), \"utf-8\");\n  }\n\n  throw new RestError(`Unsupported body/content-type combination: ${body}, ${contentType}`);\n}\n\nexport function buildBodyPart(descriptor: PartDescriptor): BodyPart {\n  const contentType = getPartContentType(descriptor);\n  const contentDisposition = getContentDisposition(descriptor);\n  const headers = createHttpHeaders(descriptor.headers ?? {});\n\n  if (contentType) {\n    headers.set(\"content-type\", contentType);\n  }\n  if (contentDisposition) {\n    headers.set(\"content-disposition\", contentDisposition);\n  }\n\n  const body = normalizeBody(descriptor.body, contentType);\n\n  return {\n    headers,\n    body,\n  };\n}\n\nexport function buildMultipartBody(parts: PartDescriptor[]): MultipartRequestBody {\n  return { parts: parts.map(buildBodyPart) };\n}\n"]}
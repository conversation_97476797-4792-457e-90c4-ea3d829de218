{"version": 3, "file": "packet.js", "names": ["_sprintfJs", "require", "HEADER_LENGTH", "exports", "TYPE", "SQL_BATCH", "RPC_REQUEST", "TABULAR_RESULT", "ATTENTION", "BULK_LOAD", "TRANSACTION_MANAGER", "LOGIN7", "NTLMAUTH_PKT", "PRELOGIN", "FEDAUTH_TOKEN", "typeByValue", "name", "STATUS", "NORMAL", "EOM", "IGNORE", "RESETCONNECTION", "RESETCONNECTIONSKIPTRAN", "OFFSET", "Type", "Status", "Length", "SPID", "PacketID", "Window", "DEFAULT_SPID", "DEFAULT_PACKETID", "DEFAULT_WINDOW", "NL", "Packet", "constructor", "typeOrBuffer", "<PERSON><PERSON><PERSON>", "buffer", "type", "alloc", "writeUInt8", "writeUInt16BE", "<PERSON><PERSON><PERSON><PERSON>", "length", "readUInt16BE", "resetConnection", "reset", "status", "readUInt8", "last", "arguments", "isLast", "ignore", "packetId", "addData", "data", "concat", "slice", "statusAsString", "statuses", "value", "push", "undefined", "join", "trim", "headerToString", "indent", "text", "sprintf", "dataToString", "BYTES_PER_GROUP", "CHARS_PER_GROUP", "BYTES_PER_LINE", "dataDump", "chars", "offset", "String", "fromCharCode", "toString", "payloadString", "isPacketComplete", "potentialPacketBuffer", "packetLength"], "sources": ["../src/packet.ts"], "sourcesContent": ["import { sprintf } from 'sprintf-js';\n\nexport const HEADER_LENGTH = 8;\n\nexport const TYPE: { [key: string]: number } = {\n  SQL_BATCH: 0x01,\n  RPC_REQUEST: 0x03,\n  TABULAR_RESULT: 0x04,\n  ATTENTION: 0x06,\n  BULK_LOAD: 0x07,\n  TRANSACTION_MANAGER: 0x0E,\n  LOGIN7: 0x10,\n  NTLMAUTH_PKT: 0x11,\n  PRELOGIN: 0x12,\n  FEDAUTH_TOKEN: 0x08\n};\n\nconst typeByValue: { [key: number]: string } = {};\n\nfor (const name in TYPE) {\n  typeByValue[TYPE[name]] = name;\n}\n\nconst STATUS: { [key: string]: number } = {\n  NORMAL: 0x00,\n  EOM: 0x01,\n  IGNORE: 0x02,\n  RESETCONNECTION: 0x08,\n  RESETCONNECTIONSKIPTRAN: 0x10\n};\n\nexport const OFFSET = {\n  Type: 0,\n  Status: 1,\n  Length: 2,\n  SPID: 4,\n  PacketID: 6,\n  Window: 7\n};\n\nconst DEFAULT_SPID = 0;\n\nconst DEFAULT_PACKETID = 1;\n\nconst DEFAULT_WINDOW = 0;\n\nconst NL = '\\n';\n\nexport class Packet {\n  declare buffer: Buffer;\n\n  constructor(typeOrBuffer: Buffer | number) {\n    if (typeOrBuffer instanceof Buffer) {\n      this.buffer = typeOrBuffer;\n    } else {\n      const type = typeOrBuffer;\n      this.buffer = Buffer.alloc(HEADER_LENGTH, 0);\n      this.buffer.writeUInt8(type, OFFSET.Type);\n      this.buffer.writeUInt8(STATUS.NORMAL, OFFSET.Status);\n      this.buffer.writeUInt16BE(DEFAULT_SPID, OFFSET.SPID);\n      this.buffer.writeUInt8(DEFAULT_PACKETID, OFFSET.PacketID);\n      this.buffer.writeUInt8(DEFAULT_WINDOW, OFFSET.Window);\n      this.setLength();\n    }\n  }\n\n  setLength() {\n    this.buffer.writeUInt16BE(this.buffer.length, OFFSET.Length);\n  }\n\n  length() {\n    return this.buffer.readUInt16BE(OFFSET.Length);\n  }\n\n  resetConnection(reset: boolean) {\n    let status = this.buffer.readUInt8(OFFSET.Status);\n    if (reset) {\n      status |= STATUS.RESETCONNECTION;\n    } else {\n      status &= 0xFF - STATUS.RESETCONNECTION;\n    }\n    this.buffer.writeUInt8(status, OFFSET.Status);\n  }\n\n  last(last?: boolean) {\n    let status = this.buffer.readUInt8(OFFSET.Status);\n    if (arguments.length > 0) {\n      if (last) {\n        status |= STATUS.EOM;\n      } else {\n        status &= 0xFF - STATUS.EOM;\n      }\n      this.buffer.writeUInt8(status, OFFSET.Status);\n    }\n    return this.isLast();\n  }\n\n  ignore(last: boolean) {\n    let status = this.buffer.readUInt8(OFFSET.Status);\n    if (last) {\n      status |= STATUS.IGNORE;\n    } else {\n      status &= 0xFF - STATUS.IGNORE;\n    }\n    this.buffer.writeUInt8(status, OFFSET.Status);\n  }\n\n  isLast() {\n    return !!(this.buffer.readUInt8(OFFSET.Status) & STATUS.EOM);\n  }\n\n  packetId(packetId?: number) {\n    if (packetId) {\n      this.buffer.writeUInt8(packetId % 256, OFFSET.PacketID);\n    }\n    return this.buffer.readUInt8(OFFSET.PacketID);\n  }\n\n  addData(data: Buffer) {\n    this.buffer = Buffer.concat([this.buffer, data]);\n    this.setLength();\n    return this;\n  }\n\n  data() {\n    return this.buffer.slice(HEADER_LENGTH);\n  }\n\n  type() {\n    return this.buffer.readUInt8(OFFSET.Type);\n  }\n\n  statusAsString() {\n    const status = this.buffer.readUInt8(OFFSET.Status);\n    const statuses = [];\n\n    for (const name in STATUS) {\n      const value = STATUS[name];\n\n      if (status & value) {\n        statuses.push(name);\n      } else {\n        statuses.push(undefined);\n      }\n    }\n\n    return statuses.join(' ').trim();\n  }\n\n  headerToString(indent = '') {\n    const text = sprintf('type:0x%02X(%s), status:0x%02X(%s), length:0x%04X, spid:0x%04X, packetId:0x%02X, window:0x%02X', this.buffer.readUInt8(OFFSET.Type), typeByValue[this.buffer.readUInt8(OFFSET.Type)], this.buffer.readUInt8(OFFSET.Status), this.statusAsString(), this.buffer.readUInt16BE(OFFSET.Length), this.buffer.readUInt16BE(OFFSET.SPID), this.buffer.readUInt8(OFFSET.PacketID), this.buffer.readUInt8(OFFSET.Window));\n    return indent + text;\n  }\n\n  dataToString(indent = '') {\n    const BYTES_PER_GROUP = 0x04;\n    const CHARS_PER_GROUP = 0x08;\n    const BYTES_PER_LINE = 0x20;\n    const data = this.data();\n\n    let dataDump = '';\n    let chars = '';\n\n\n    for (let offset = 0; offset < data.length; offset++) {\n      if (offset % BYTES_PER_LINE === 0) {\n        dataDump += indent;\n        dataDump += sprintf('%04X  ', offset);\n      }\n\n      if (data[offset] < 0x20 || data[offset] > 0x7E) {\n        chars += '.';\n        if (((offset + 1) % CHARS_PER_GROUP === 0) && !((offset + 1) % BYTES_PER_LINE === 0)) {\n          chars += ' ';\n        }\n      } else {\n        chars += String.fromCharCode(data[offset]);\n      }\n\n      if (data[offset] != null) {\n        dataDump += sprintf('%02X', data[offset]);\n      }\n\n      if (((offset + 1) % BYTES_PER_GROUP === 0) && !((offset + 1) % BYTES_PER_LINE === 0)) {\n        dataDump += ' ';\n      }\n\n      if ((offset + 1) % BYTES_PER_LINE === 0) {\n        dataDump += '  ' + chars;\n        chars = '';\n        if (offset < data.length - 1) {\n          dataDump += NL;\n        }\n      }\n    }\n\n    if (chars.length) {\n      dataDump += '  ' + chars;\n    }\n\n    return dataDump;\n  }\n\n  toString(indent = '') {\n    return this.headerToString(indent) + '\\n' + this.dataToString(indent + indent);\n  }\n\n  payloadString() {\n    return '';\n  }\n}\n\nexport function isPacketComplete(potentialPacketBuffer: Buffer) {\n  if (potentialPacketBuffer.length < HEADER_LENGTH) {\n    return false;\n  } else {\n    return potentialPacketBuffer.length >= potentialPacketBuffer.readUInt16BE(OFFSET.Length);\n  }\n}\n\nexport function packetLength(potentialPacketBuffer: Buffer) {\n  return potentialPacketBuffer.readUInt16BE(OFFSET.Length);\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEO,MAAMC,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAG,CAAC;AAEvB,MAAME,IAA+B,GAAAD,OAAA,CAAAC,IAAA,GAAG;EAC7CC,SAAS,EAAE,IAAI;EACfC,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAE,IAAI;EACpBC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,IAAI;EACfC,mBAAmB,EAAE,IAAI;EACzBC,MAAM,EAAE,IAAI;EACZC,YAAY,EAAE,IAAI;EAClBC,QAAQ,EAAE,IAAI;EACdC,aAAa,EAAE;AACjB,CAAC;AAED,MAAMC,WAAsC,GAAG,CAAC,CAAC;AAEjD,KAAK,MAAMC,IAAI,IAAIZ,IAAI,EAAE;EACvBW,WAAW,CAACX,IAAI,CAACY,IAAI,CAAC,CAAC,GAAGA,IAAI;AAChC;AAEA,MAAMC,MAAiC,GAAG;EACxCC,MAAM,EAAE,IAAI;EACZC,GAAG,EAAE,IAAI;EACTC,MAAM,EAAE,IAAI;EACZC,eAAe,EAAE,IAAI;EACrBC,uBAAuB,EAAE;AAC3B,CAAC;AAEM,MAAMC,MAAM,GAAApB,OAAA,CAAAoB,MAAA,GAAG;EACpBC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,QAAQ,EAAE,CAAC;EACXC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,YAAY,GAAG,CAAC;AAEtB,MAAMC,gBAAgB,GAAG,CAAC;AAE1B,MAAMC,cAAc,GAAG,CAAC;AAExB,MAAMC,EAAE,GAAG,IAAI;AAER,MAAMC,MAAM,CAAC;EAGlBC,WAAWA,CAACC,YAA6B,EAAE;IACzC,IAAIA,YAAY,YAAYC,MAAM,EAAE;MAClC,IAAI,CAACC,MAAM,GAAGF,YAAY;IAC5B,CAAC,MAAM;MACL,MAAMG,IAAI,GAAGH,YAAY;MACzB,IAAI,CAACE,MAAM,GAAGD,MAAM,CAACG,KAAK,CAACtC,aAAa,EAAE,CAAC,CAAC;MAC5C,IAAI,CAACoC,MAAM,CAACG,UAAU,CAACF,IAAI,EAAEhB,MAAM,CAACC,IAAI,CAAC;MACzC,IAAI,CAACc,MAAM,CAACG,UAAU,CAACxB,MAAM,CAACC,MAAM,EAAEK,MAAM,CAACE,MAAM,CAAC;MACpD,IAAI,CAACa,MAAM,CAACI,aAAa,CAACZ,YAAY,EAAEP,MAAM,CAACI,IAAI,CAAC;MACpD,IAAI,CAACW,MAAM,CAACG,UAAU,CAACV,gBAAgB,EAAER,MAAM,CAACK,QAAQ,CAAC;MACzD,IAAI,CAACU,MAAM,CAACG,UAAU,CAACT,cAAc,EAAET,MAAM,CAACM,MAAM,CAAC;MACrD,IAAI,CAACc,SAAS,CAAC,CAAC;IAClB;EACF;EAEAA,SAASA,CAAA,EAAG;IACV,IAAI,CAACL,MAAM,CAACI,aAAa,CAAC,IAAI,CAACJ,MAAM,CAACM,MAAM,EAAErB,MAAM,CAACG,MAAM,CAAC;EAC9D;EAEAkB,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACN,MAAM,CAACO,YAAY,CAACtB,MAAM,CAACG,MAAM,CAAC;EAChD;EAEAoB,eAAeA,CAACC,KAAc,EAAE;IAC9B,IAAIC,MAAM,GAAG,IAAI,CAACV,MAAM,CAACW,SAAS,CAAC1B,MAAM,CAACE,MAAM,CAAC;IACjD,IAAIsB,KAAK,EAAE;MACTC,MAAM,IAAI/B,MAAM,CAACI,eAAe;IAClC,CAAC,MAAM;MACL2B,MAAM,IAAI,IAAI,GAAG/B,MAAM,CAACI,eAAe;IACzC;IACA,IAAI,CAACiB,MAAM,CAACG,UAAU,CAACO,MAAM,EAAEzB,MAAM,CAACE,MAAM,CAAC;EAC/C;EAEAyB,IAAIA,CAACA,IAAc,EAAE;IACnB,IAAIF,MAAM,GAAG,IAAI,CAACV,MAAM,CAACW,SAAS,CAAC1B,MAAM,CAACE,MAAM,CAAC;IACjD,IAAI0B,SAAS,CAACP,MAAM,GAAG,CAAC,EAAE;MACxB,IAAIM,IAAI,EAAE;QACRF,MAAM,IAAI/B,MAAM,CAACE,GAAG;MACtB,CAAC,MAAM;QACL6B,MAAM,IAAI,IAAI,GAAG/B,MAAM,CAACE,GAAG;MAC7B;MACA,IAAI,CAACmB,MAAM,CAACG,UAAU,CAACO,MAAM,EAAEzB,MAAM,CAACE,MAAM,CAAC;IAC/C;IACA,OAAO,IAAI,CAAC2B,MAAM,CAAC,CAAC;EACtB;EAEAC,MAAMA,CAACH,IAAa,EAAE;IACpB,IAAIF,MAAM,GAAG,IAAI,CAACV,MAAM,CAACW,SAAS,CAAC1B,MAAM,CAACE,MAAM,CAAC;IACjD,IAAIyB,IAAI,EAAE;MACRF,MAAM,IAAI/B,MAAM,CAACG,MAAM;IACzB,CAAC,MAAM;MACL4B,MAAM,IAAI,IAAI,GAAG/B,MAAM,CAACG,MAAM;IAChC;IACA,IAAI,CAACkB,MAAM,CAACG,UAAU,CAACO,MAAM,EAAEzB,MAAM,CAACE,MAAM,CAAC;EAC/C;EAEA2B,MAAMA,CAAA,EAAG;IACP,OAAO,CAAC,EAAE,IAAI,CAACd,MAAM,CAACW,SAAS,CAAC1B,MAAM,CAACE,MAAM,CAAC,GAAGR,MAAM,CAACE,GAAG,CAAC;EAC9D;EAEAmC,QAAQA,CAACA,QAAiB,EAAE;IAC1B,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAAChB,MAAM,CAACG,UAAU,CAACa,QAAQ,GAAG,GAAG,EAAE/B,MAAM,CAACK,QAAQ,CAAC;IACzD;IACA,OAAO,IAAI,CAACU,MAAM,CAACW,SAAS,CAAC1B,MAAM,CAACK,QAAQ,CAAC;EAC/C;EAEA2B,OAAOA,CAACC,IAAY,EAAE;IACpB,IAAI,CAAClB,MAAM,GAAGD,MAAM,CAACoB,MAAM,CAAC,CAAC,IAAI,CAACnB,MAAM,EAAEkB,IAAI,CAAC,CAAC;IAChD,IAAI,CAACb,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI;EACb;EAEAa,IAAIA,CAAA,EAAG;IACL,OAAO,IAAI,CAAClB,MAAM,CAACoB,KAAK,CAACxD,aAAa,CAAC;EACzC;EAEAqC,IAAIA,CAAA,EAAG;IACL,OAAO,IAAI,CAACD,MAAM,CAACW,SAAS,CAAC1B,MAAM,CAACC,IAAI,CAAC;EAC3C;EAEAmC,cAAcA,CAAA,EAAG;IACf,MAAMX,MAAM,GAAG,IAAI,CAACV,MAAM,CAACW,SAAS,CAAC1B,MAAM,CAACE,MAAM,CAAC;IACnD,MAAMmC,QAAQ,GAAG,EAAE;IAEnB,KAAK,MAAM5C,IAAI,IAAIC,MAAM,EAAE;MACzB,MAAM4C,KAAK,GAAG5C,MAAM,CAACD,IAAI,CAAC;MAE1B,IAAIgC,MAAM,GAAGa,KAAK,EAAE;QAClBD,QAAQ,CAACE,IAAI,CAAC9C,IAAI,CAAC;MACrB,CAAC,MAAM;QACL4C,QAAQ,CAACE,IAAI,CAACC,SAAS,CAAC;MAC1B;IACF;IAEA,OAAOH,QAAQ,CAACI,IAAI,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;EAClC;EAEAC,cAAcA,CAACC,MAAM,GAAG,EAAE,EAAE;IAC1B,MAAMC,IAAI,GAAG,IAAAC,kBAAO,EAAC,gGAAgG,EAAE,IAAI,CAAC/B,MAAM,CAACW,SAAS,CAAC1B,MAAM,CAACC,IAAI,CAAC,EAAET,WAAW,CAAC,IAAI,CAACuB,MAAM,CAACW,SAAS,CAAC1B,MAAM,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACc,MAAM,CAACW,SAAS,CAAC1B,MAAM,CAACE,MAAM,CAAC,EAAE,IAAI,CAACkC,cAAc,CAAC,CAAC,EAAE,IAAI,CAACrB,MAAM,CAACO,YAAY,CAACtB,MAAM,CAACG,MAAM,CAAC,EAAE,IAAI,CAACY,MAAM,CAACO,YAAY,CAACtB,MAAM,CAACI,IAAI,CAAC,EAAE,IAAI,CAACW,MAAM,CAACW,SAAS,CAAC1B,MAAM,CAACK,QAAQ,CAAC,EAAE,IAAI,CAACU,MAAM,CAACW,SAAS,CAAC1B,MAAM,CAACM,MAAM,CAAC,CAAC;IACta,OAAOsC,MAAM,GAAGC,IAAI;EACtB;EAEAE,YAAYA,CAACH,MAAM,GAAG,EAAE,EAAE;IACxB,MAAMI,eAAe,GAAG,IAAI;IAC5B,MAAMC,eAAe,GAAG,IAAI;IAC5B,MAAMC,cAAc,GAAG,IAAI;IAC3B,MAAMjB,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;IAExB,IAAIkB,QAAQ,GAAG,EAAE;IACjB,IAAIC,KAAK,GAAG,EAAE;IAGd,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGpB,IAAI,CAACZ,MAAM,EAAEgC,MAAM,EAAE,EAAE;MACnD,IAAIA,MAAM,GAAGH,cAAc,KAAK,CAAC,EAAE;QACjCC,QAAQ,IAAIP,MAAM;QAClBO,QAAQ,IAAI,IAAAL,kBAAO,EAAC,QAAQ,EAAEO,MAAM,CAAC;MACvC;MAEA,IAAIpB,IAAI,CAACoB,MAAM,CAAC,GAAG,IAAI,IAAIpB,IAAI,CAACoB,MAAM,CAAC,GAAG,IAAI,EAAE;QAC9CD,KAAK,IAAI,GAAG;QACZ,IAAK,CAACC,MAAM,GAAG,CAAC,IAAIJ,eAAe,KAAK,CAAC,IAAK,EAAE,CAACI,MAAM,GAAG,CAAC,IAAIH,cAAc,KAAK,CAAC,CAAC,EAAE;UACpFE,KAAK,IAAI,GAAG;QACd;MACF,CAAC,MAAM;QACLA,KAAK,IAAIE,MAAM,CAACC,YAAY,CAACtB,IAAI,CAACoB,MAAM,CAAC,CAAC;MAC5C;MAEA,IAAIpB,IAAI,CAACoB,MAAM,CAAC,IAAI,IAAI,EAAE;QACxBF,QAAQ,IAAI,IAAAL,kBAAO,EAAC,MAAM,EAAEb,IAAI,CAACoB,MAAM,CAAC,CAAC;MAC3C;MAEA,IAAK,CAACA,MAAM,GAAG,CAAC,IAAIL,eAAe,KAAK,CAAC,IAAK,EAAE,CAACK,MAAM,GAAG,CAAC,IAAIH,cAAc,KAAK,CAAC,CAAC,EAAE;QACpFC,QAAQ,IAAI,GAAG;MACjB;MAEA,IAAI,CAACE,MAAM,GAAG,CAAC,IAAIH,cAAc,KAAK,CAAC,EAAE;QACvCC,QAAQ,IAAI,IAAI,GAAGC,KAAK;QACxBA,KAAK,GAAG,EAAE;QACV,IAAIC,MAAM,GAAGpB,IAAI,CAACZ,MAAM,GAAG,CAAC,EAAE;UAC5B8B,QAAQ,IAAIzC,EAAE;QAChB;MACF;IACF;IAEA,IAAI0C,KAAK,CAAC/B,MAAM,EAAE;MAChB8B,QAAQ,IAAI,IAAI,GAAGC,KAAK;IAC1B;IAEA,OAAOD,QAAQ;EACjB;EAEAK,QAAQA,CAACZ,MAAM,GAAG,EAAE,EAAE;IACpB,OAAO,IAAI,CAACD,cAAc,CAACC,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,CAACG,YAAY,CAACH,MAAM,GAAGA,MAAM,CAAC;EAChF;EAEAa,aAAaA,CAAA,EAAG;IACd,OAAO,EAAE;EACX;AACF;AAAC7E,OAAA,CAAA+B,MAAA,GAAAA,MAAA;AAEM,SAAS+C,gBAAgBA,CAACC,qBAA6B,EAAE;EAC9D,IAAIA,qBAAqB,CAACtC,MAAM,GAAG1C,aAAa,EAAE;IAChD,OAAO,KAAK;EACd,CAAC,MAAM;IACL,OAAOgF,qBAAqB,CAACtC,MAAM,IAAIsC,qBAAqB,CAACrC,YAAY,CAACtB,MAAM,CAACG,MAAM,CAAC;EAC1F;AACF;AAEO,SAASyD,YAAYA,CAACD,qBAA6B,EAAE;EAC1D,OAAOA,qBAAqB,CAACrC,YAAY,CAACtB,MAAM,CAACG,MAAM,CAAC;AAC1D"}
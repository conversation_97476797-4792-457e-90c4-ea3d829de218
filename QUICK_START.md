# RC Dashboard - Quick Start Guide

## 🎯 What You Have

A complete **Route Card (RC) Dashboard** system with:
- ✅ Professional web dashboard with your exact SQL query
- ✅ Real-time data from your SQL Server database
- ✅ Auto-refresh every 5 minutes
- ✅ Auto-slide through data pages
- ✅ Location filtering (All Locations, PRK Sunkudkatte, PRK Machohalli)
- ✅ Aging analysis (≤15, 16-45, 46-90, >90 days)
- ✅ Easy-to-use batch files for management

## 🚀 How to Use (3 Simple Steps)

### Step 1: Install (One-time setup)
```
Double-click: install-rc-dashboard.bat
```
This installs all required dependencies.

### Step 2: Start Dashboard
```
Double-click: start-rc-dashboard.bat
```
This starts the server and opens the dashboard in your browser.

### Step 3: View Dashboard
The dashboard will automatically open at: `http://localhost:5001/rc-dashboard.html`

## 📁 Batch Files Explained

| File | Purpose | When to Use |
|------|---------|-------------|
| `install-rc-dashboard.bat` | Install dependencies | First time setup |
| `start-rc-dashboard.bat` | Start the dashboard | Every time you want to use it |
| `stop-rc-dashboard.bat` | Stop the dashboard | When you're done |
| `test-rc-dashboard.bat` | Test the system | If something isn't working |

## 🎛️ Dashboard Features

### Statistics Cards (Top Row)
- **Pending RCs**: Total count of pending route cards
- **≤ 15 Days**: Recent route cards (Green)
- **16-45 Days**: Moderate aging (Orange) 
- **46-90 Days**: Critical aging (Red)
- **> 90 Days**: Overdue (Purple)

### Data Table (Bottom Section)
- **Auto-scrolling**: Smoothly scrolls through data
- **Auto-sliding**: Changes pages every 5 seconds
- **Location Filter**: Dropdown to filter by location
- **Real-time Data**: Updates every 5 minutes

### Columns Displayed
1. Work Order Number
2. Product Name
3. WO Date
4. WO Quantity
5. OK Quantity (from your TotalOkQty)
6. Stock Quantity (from your TotalStockQty)
7. Rejection Quantity
8. Dispatch Quantity
9. Aging (Days) - Color coded
10. Location

## 🔧 Your SQL Query Integration

The dashboard uses your exact SQL query with all CTEs:
- ✅ ProcessDetails
- ✅ StatusDetails (with TotalOkQty and TotalStockQty)
- ✅ RejectionSummary
- ✅ DespatchSummary
- ✅ StockSummary

**Database Connection**: WIN-PRK-SRV-01, ICsoft database, sa/PRK@1234

## 🌐 URLs

- **Main Dashboard**: http://localhost:5001/rc-dashboard.html
- **Debug Version**: http://localhost:5001/debug-rc-dashboard.html
- **API Health**: http://localhost:5001/api/health

## ⚡ Quick Troubleshooting

### Problem: Dashboard won't start
**Solution**: Run `test-rc-dashboard.bat` to diagnose issues

### Problem: No data showing
**Solution**: 
1. Check if SQL Server is running
2. Verify database connection in `backend/server.js`
3. Use debug version: `http://localhost:5001/debug-rc-dashboard.html`

### Problem: "Node.js not found"
**Solution**: Install Node.js from https://nodejs.org/

## 🎨 Customization

### Change Refresh Interval
Edit `rc-dashboard.html`, find:
```javascript
5 * 60 * 1000  // 5 minutes
```

### Change Slide Interval
Edit `rc-dashboard.html`, find:
```javascript
5000  // 5 seconds
```

### Change Database Connection
Edit `backend/server.js`, update:
```javascript
const dbConfig = {
    server: 'WIN-PRK-SRV-01',
    database: 'ICsoft',
    user: 'sa',
    password: 'PRK@1234'
};
```

## 📊 What the Dashboard Shows

Based on your SQL query, the dashboard displays:
- **Only Pending RCs** (WHERE sd.WOStatus = 'Pending')
- **All Locations** by default, with filtering option
- **Proper aging calculation** using DATEDIFF
- **Complete dispatch details** with invoice numbers
- **Stock quantities** from all stages
- **Rejection summaries** by work order

## 🔄 Daily Usage

1. **Morning**: Run `start-rc-dashboard.bat`
2. **During Day**: Dashboard auto-refreshes, no action needed
3. **Evening**: Run `stop-rc-dashboard.bat` (optional)

The dashboard is designed to run continuously and will automatically:
- Refresh data every 5 minutes
- Slide through pages every 5 seconds
- Handle large datasets efficiently
- Show real-time aging calculations

---

**You're all set!** 🎉 Your professional RC Dashboard is ready to use with your exact SQL query and database.

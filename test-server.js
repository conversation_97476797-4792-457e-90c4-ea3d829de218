const express = require('express');
const sql = require('mssql');

const app = express();
const PORT = 5002;

// Database configuration
const dbConfig = {
    server: 'WIN-PRK-SRV-01',
    database: 'ICsoft',
    user: 'sa',
    password: 'PRK@1234',
    options: {
        encrypt: false,
        trustServerCertificate: true,
        enableArithAbort: true
    },
    pool: {
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000
    }
};

let pool;

async function initializeDatabase() {
    try {
        console.log('🔄 Connecting to database...');
        console.log('Server:', dbConfig.server);
        console.log('Database:', dbConfig.database);
        
        pool = await sql.connect(dbConfig);
        console.log('✅ Database connected successfully');
        
        // Test query
        const result = await pool.request().query('SELECT TOP 1 * FROM WorkOrder');
        console.log('✅ Test query successful, found', result.recordset.length, 'records');
        
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        console.error('Full error:', error);
        throw error;
    }
}

app.get('/test', async (req, res) => {
    try {
        const result = await pool.request().query('SELECT COUNT(*) as count FROM WorkOrder');
        res.json({
            success: true,
            message: 'Database test successful',
            count: result.recordset[0].count
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.get('/health', (req, res) => {
    res.json({ success: true, message: 'Server is running' });
});

async function startServer() {
    try {
        console.log('🚀 Starting test server...');
        await initializeDatabase();
        
        app.listen(PORT, () => {
            console.log(`✅ Test server running on port ${PORT}`);
            console.log(`🔗 Test URL: http://localhost:${PORT}/test`);
            console.log(`🔗 Health URL: http://localhost:${PORT}/health`);
        });
    } catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
}

startServer();

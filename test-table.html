<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Table Data Test</h1>
    
    <div>
        <button onclick="loadTableData()">Load Table Data</button>
        <button onclick="showRawData()">Show Raw API Data</button>
        <button onclick="clearTable()">Clear Table</button>
    </div>
    
    <div id="rawData" style="display: none;">
        <h3>Raw API Response:</h3>
        <pre id="rawDataContent"></pre>
    </div>
    
    <div id="tableContainer">
        <div class="loading">Click "Load Table Data" to test</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        let rawApiData = null;
        
        function getAgingColor(days) {
            if (days <= 15) return '#22c55e';
            if (days <= 45) return '#f59e0b';
            if (days <= 90) return '#dc2626';
            return '#7f1d1d';
        }
        
        function getLocationName(locationId) {
            const locationMap = {
                '2': 'PRK Sunkudkatte',
                '3': 'PRK Machohalli'
            };
            return locationMap[locationId] || `Location ${locationId}`;
        }
        
        async function loadTableData() {
            const container = document.getElementById('tableContainer');
            container.innerHTML = '<div class="loading">Loading data...</div>';
            
            try {
                console.log('Loading table data...');
                const url = `${API_BASE}/rc-pending`;
                console.log('URL:', url);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Data received:', data);
                rawApiData = data;
                
                if (data.success && data.data && data.data.length > 0) {
                    const tableData = data.data.slice(0, 10); // Show first 10 records
                    
                    const table = `
                        <h3>Table Data (${data.data.length} total records, showing first 10)</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>Work Order</th>
                                    <th>Part & Product</th>
                                    <th>WO Date</th>
                                    <th>Status</th>
                                    <th>WO Qty</th>
                                    <th>OK Qty</th>
                                    <th>Stock Qty</th>
                                    <th>Rej Qty</th>
                                    <th>Desp Qty</th>
                                    <th>Close Date</th>
                                    <th>Aging</th>
                                    <th>Location</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${tableData.map(item => `
                                    <tr>
                                        <td><strong style="color: #3b82f6;">${item.WoNo || 'N/A'}</strong></td>
                                        <td>
                                            <div style="font-weight: bold; font-size: 14px;">
                                                ${item.PartNoAndProductName?.split(' | ')[1] || 'Unknown Product'}
                                            </div>
                                            <div style="font-size: 12px; color: #6b7280;">
                                                ${item.PartNoAndProductName?.split(' | ')[0] || 'Unknown Part'}
                                            </div>
                                        </td>
                                        <td>${item.WODate ? new Date(item.WODate).toLocaleDateString() : 'N/A'}</td>
                                        <td>
                                            <span style="background: #fef2f2; color: #ef4444; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                                RC Pending
                                            </span>
                                        </td>
                                        <td>${item.WO_Qty?.toLocaleString() || 'N/A'}</td>
                                        <td style="color: #22c55e; font-weight: bold;">
                                            ${item.TotalOkQty?.toLocaleString() || 'N/A'}
                                        </td>
                                        <td style="color: ${getAgingColor(item.AgingInDays)}; font-weight: bold;">
                                            ${item.AgingInDays || 0}d
                                        </td>
                                        <td>
                                            <div>
                                                <div style="font-weight: bold; font-size: 12px;">
                                                    ${item.LocationName || getLocationName(item.LocationID)}
                                                </div>
                                                <div style="font-size: 10px; color: #6b7280;">
                                                    ID: ${item.LocationID || 'N/A'}
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;
                    
                    container.innerHTML = table;
                    console.log('Table rendered successfully');
                    
                } else {
                    container.innerHTML = '<div class="error">No data found or API returned empty result</div>';
                }
                
            } catch (error) {
                console.error('Error loading table data:', error);
                container.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        function showRawData() {
            const rawDiv = document.getElementById('rawData');
            const rawContent = document.getElementById('rawDataContent');
            
            if (rawApiData) {
                rawContent.textContent = JSON.stringify(rawApiData, null, 2);
                rawDiv.style.display = 'block';
            } else {
                alert('No data loaded yet. Click "Load Table Data" first.');
            }
        }
        
        function clearTable() {
            document.getElementById('tableContainer').innerHTML = '<div class="loading">Click "Load Table Data" to test</div>';
            document.getElementById('rawData').style.display = 'none';
            rawApiData = null;
        }
    </script>
</body>
</html>

@echo off
title RC Dashboard - Installation
color 0E

echo.
echo ========================================
echo    RC DASHBOARD - INSTALLATION
echo ========================================
echo.

echo [1/4] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Node.js is not installed
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo Recommended version: LTS (Long Term Support)
    echo.
    echo After installing Node.js, run this script again.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✓ Node.js is installed: %NODE_VERSION%
)

echo.
echo [2/4] Checking npm installation...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: npm is not available
    echo npm should come with Node.js installation
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo ✓ npm is available: %NPM_VERSION%
)

echo.
echo [3/4] Installing backend dependencies...
cd /d "%~dp0backend"
if not exist "package.json" (
    echo ❌ ERROR: package.json not found in backend folder
    echo Please ensure the backend folder structure is correct
    pause
    exit /b 1
)

echo Installing Node.js packages...
npm install
if errorlevel 1 (
    echo ❌ ERROR: Failed to install dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
) else (
    echo ✓ Backend dependencies installed successfully
)

echo.
echo [4/4] Verifying installation...
if not exist "node_modules" (
    echo ❌ ERROR: node_modules folder not created
    pause
    exit /b 1
) else (
    echo ✓ Installation verified
)

echo.
echo ========================================
echo    INSTALLATION COMPLETED SUCCESSFULLY
echo ========================================
echo.
echo RC Dashboard has been installed successfully!
echo.
echo Next steps:
echo 1. Configure your SQL Server connection in backend/server.js
echo 2. Run: test-rc-dashboard.bat (to test the system)
echo 3. Run: start-rc-dashboard.bat (to start the dashboard)
echo.
echo Files created:
echo - start-rc-dashboard.bat (Start the dashboard)
echo - stop-rc-dashboard.bat  (Stop the dashboard)
echo - test-rc-dashboard.bat  (Test the system)
echo - rc-dashboard.html      (Main dashboard)
echo.
echo Database Configuration:
echo Edit backend/server.js and update the database connection settings:
echo - server: 'WIN-PRK-SRV-01'
echo - database: 'ICsoft'
echo - user: 'sa'
echo - password: 'PRK@1234'
echo.
pause

# 🔧 RC Dashboard Troubleshooting Guide

## 🚀 Quick Start (Recommended)

### Option 1: Simple HTML Dashboard (Most Reliable)
```cmd
# 1. Start backend only
cd backend
npm start

# 2. Open HTML dashboard
start simple-dashboard.html
```

### Option 2: Use Simple Batch File
```cmd
start-simple.bat
```

## 🔍 Common Issues & Solutions

### ❌ "Can't connect to host" Error

**Cause:** Backend server not running or not ready yet.

**Solutions:**
1. **Check if backend is running:**
   ```cmd
   quick-test.bat
   ```

2. **Start backend manually:**
   ```cmd
   cd backend
   npm start
   ```

3. **Wait for backend to be ready:**
   - Look for "✅ Database connected successfully" message
   - Test with: `curl http://localhost:5001/api/health`

### ❌ Frontend Server Issues

**Cause:** React dev server conflicts or port issues.

**Solutions:**
1. **Use HTML dashboard instead:**
   ```cmd
   start simple-dashboard.html
   ```

2. **Kill all Node processes:**
   ```cmd
   taskkill /f /im node.exe
   ```

3. **Restart backend only:**
   ```cmd
   cd backend
   npm start
   ```

### ❌ Port Already in Use

**Cause:** Previous processes still running.

**Solutions:**
1. **Stop all processes:**
   ```cmd
   stop-dashboard.bat
   ```

2. **Kill specific ports:**
   ```cmd
   # Kill port 5001 (backend)
   for /f "tokens=5" %a in ('netstat -aon ^| findstr ":5001"') do taskkill /f /pid %a

   # Kill port 5174 (frontend)
   for /f "tokens=5" %a in ('netstat -aon ^| findstr ":5174"') do taskkill /f /pid %a
   ```

### ❌ Database Connection Issues

**Symptoms:** API returns errors or empty data.

**Solutions:**
1. **Check SQL Server connection:**
   - Ensure SQL Server is running
   - Verify server name: `WIN-PRK-SRV-01`
   - Verify database: `ICsoft`

2. **Test database connection:**
   ```cmd
   curl http://localhost:5001/api/locations
   ```

3. **Fallback to demo mode:**
   - Dashboard will show mock data if database fails
   - Look for `"demo": true` in API responses

## 📋 Step-by-Step Startup Process

### Method 1: Automatic (Recommended)
```cmd
start-simple.bat
```

### Method 2: Manual
```cmd
# 1. Stop existing processes
stop-dashboard.bat

# 2. Start backend
cd backend
npm start

# 3. Wait for "Database connected successfully"

# 4. Open dashboard
start simple-dashboard.html
```

### Method 3: Test First
```cmd
# 1. Test current status
quick-test.bat

# 2. If backend running, just open dashboard
start simple-dashboard.html

# 3. If backend not running, start it
cd backend
npm start
```

## 🌐 Dashboard Access Options

### 1. HTML Dashboard (Recommended)
- **File:** `simple-dashboard.html`
- **Pros:** Most reliable, works offline, no server needed
- **Cons:** Static file, manual refresh needed

### 2. React Dashboard
- **URL:** `http://localhost:5174`
- **Pros:** Hot reload, modern interface
- **Cons:** Requires frontend server, more complex

### 3. Direct API Access
- **Health:** `http://localhost:5001/api/health`
- **Stats:** `http://localhost:5001/api/dashboard-stats`
- **Data:** `http://localhost:5001/api/rc-pending`

## 🔧 Useful Commands

### Check What's Running
```cmd
# Check ports
netstat -aon | findstr ":5001"
netstat -aon | findstr ":5174"

# Check Node processes
tasklist | findstr node.exe
```

### Test APIs
```cmd
# Test backend health
curl http://localhost:5001/api/health

# Test dashboard stats
curl http://localhost:5001/api/dashboard-stats

# Test locations
curl http://localhost:5001/api/locations
```

### Clean Restart
```cmd
# 1. Kill everything
taskkill /f /im node.exe

# 2. Wait a moment
timeout /t 3

# 3. Start fresh
cd backend
npm start
```

## 📞 Quick Help

### ✅ Everything Working
- Backend: `http://localhost:5001/api/health` returns OK
- Dashboard: `simple-dashboard.html` loads and shows data
- Auto-slide: Works with smooth transitions

### ⚠️ Partial Working
- Backend: OK, but dashboard shows errors
- Solution: Refresh dashboard, check CORS

### ❌ Nothing Working
- Backend: Not responding
- Solution: Restart backend, check database connection

## 🎯 Best Practices

1. **Always use `start-simple.bat` for startup**
2. **Use `quick-test.bat` to check status**
3. **Prefer HTML dashboard over React for reliability**
4. **Wait for backend to fully start before opening dashboard**
5. **Use `stop-dashboard.bat` before restarting**

## 📊 Expected Data

When working correctly, you should see:
- **1,739 Pending RCs**
- **314 RCs ≤ 15 days**
- **312 RCs 16-45 days**
- **201 RCs 46-90 days**
- **912 RCs > 90 days**
- **Locations:** PRK Sunkudkatte, PRK Machohalli

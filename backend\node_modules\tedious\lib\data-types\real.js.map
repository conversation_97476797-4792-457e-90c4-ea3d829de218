{"version": 3, "file": "real.js", "names": ["_floatn", "_interopRequireDefault", "require", "obj", "__esModule", "default", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "DATA_LENGTH", "Real", "id", "type", "name", "declaration", "generateTypeInfo", "FloatN", "generateParameterLength", "parameter", "options", "value", "generateParameterData", "buffer", "alloc", "writeFloatLE", "parseFloat", "validate", "isNaN", "TypeError", "_default", "exports", "module"], "sources": ["../../src/data-types/real.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport FloatN from './floatn';\n\nconst NULL_LENGTH = Buffer.from([0x00]);\nconst DATA_LENGTH = Buffer.from([0x04]);\n\nconst Real: DataType = {\n  id: 0x3B,\n  type: 'FLT4',\n  name: 'Real',\n\n  declaration: function() {\n    return 'real';\n  },\n\n  generateTypeInfo() {\n    return Buffer.from([FloatN.id, 0x04]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    return DATA_LENGTH;\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const buffer = Buffer.alloc(4);\n    buffer.writeFloatLE(parseFloat(parameter.value), 0);\n    yield buffer;\n  },\n\n  validate: function(value): null | number {\n    if (value == null) {\n      return null;\n    }\n    value = parseFloat(value);\n    if (isNaN(value)) {\n      throw new TypeError('Invalid number.');\n    }\n    return value;\n  }\n};\n\nexport default Real;\nmodule.exports = Real;\n"], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA8B,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE9B,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,MAAMC,WAAW,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAME,IAAc,GAAG;EACrBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EAEZC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,MAAM;EACf,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,OAAOR,MAAM,CAACC,IAAI,CAAC,CAACQ,eAAM,CAACL,EAAE,EAAE,IAAI,CAAC,CAAC;EACvC,CAAC;EAEDM,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOd,WAAW;IACpB;IAEA,OAAOG,WAAW;EACpB,CAAC;EAED,CAAEY,qBAAqBA,CAACH,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAME,MAAM,GAAGf,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,YAAY,CAACC,UAAU,CAACP,SAAS,CAACE,KAAK,CAAC,EAAE,CAAC,CAAC;IACnD,MAAME,MAAM;EACd,CAAC;EAEDI,QAAQ,EAAE,SAAAA,CAASN,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACAA,KAAK,GAAGK,UAAU,CAACL,KAAK,CAAC;IACzB,IAAIO,KAAK,CAACP,KAAK,CAAC,EAAE;MAChB,MAAM,IAAIQ,SAAS,CAAC,iBAAiB,CAAC;IACxC;IACA,OAAOR,KAAK;EACd;AACF,CAAC;AAAC,IAAAS,QAAA,GAAAC,OAAA,CAAAzB,OAAA,GAEaK,IAAI;AACnBqB,MAAM,CAACD,OAAO,GAAGpB,IAAI"}
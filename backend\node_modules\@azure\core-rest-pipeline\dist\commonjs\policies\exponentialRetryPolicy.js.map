{"version": 3, "file": "exponentialRetryPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/exponentialRetryPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAyClC,wDAIC;AAzCD,0EAGqD;AAErD;;GAEG;AACU,QAAA,0BAA0B,GAAG,qCAA6B,CAAC;AAyBxE;;;GAGG;AACH,SAAgB,sBAAsB,CACpC,UAAyC,EAAE;IAE3C,OAAO,IAAA,iCAAyB,EAAC,OAAO,CAAC,CAAC;AAC5C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\nimport {\n  exponentialRetryPolicyName as tspExponentialRetryPolicyName,\n  exponentialRetryPolicy as tspExponentialRetryPolicy,\n} from \"@typespec/ts-http-runtime/internal/policies\";\n\n/**\n * The programmatic identifier of the exponentialRetryPolicy.\n */\nexport const exponentialRetryPolicyName = tspExponentialRetryPolicyName;\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface ExponentialRetryPolicyOptions {\n  /**\n   * The maximum number of retry attempts. Defaults to 3.\n   */\n  maxRetries?: number;\n\n  /**\n   * The amount of delay in milliseconds between retry attempts. Defaults to 1000\n   * (1 second.) The delay increases exponentially with each retry up to a maximum\n   * specified by maxRetryDelayInMs.\n   */\n  retryDelayInMs?: number;\n\n  /**\n   * The maximum delay in milliseconds allowed before retrying an operation. Defaults\n   * to 64000 (64 seconds).\n   */\n  maxRetryDelayInMs?: number;\n}\n\n/**\n * A policy that attempts to retry requests while introducing an exponentially increasing delay.\n * @param options - Options that configure retry logic.\n */\nexport function exponentialRetryPolicy(\n  options: ExponentialRetryPolicyOptions = {},\n): PipelinePolicy {\n  return tspExponentialRetryPolicy(options);\n}\n"]}
{"version": 3, "file": "urlHelpers.js", "sourceRoot": "", "sources": ["../../../src/client/urlHelpers.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAqClC,SAAS,2BAA2B,CAAC,CAAU;IAC7C,MAAM,KAAK,GAAI,CAA+B,CAAC,KAAY,CAAC;IAC5D,OAAO,CACL,KAAK,KAAK,SAAS,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,IAAI,OAAO,KAAK,CAAC,QAAQ,KAAK,UAAU,CAC5F,CAAC;AACJ,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,eAAe,CAC7B,QAAgB,EAChB,SAAiB,EACjB,cAA8D,EAC9D,UAA6B,EAAE;IAE/B,IAAI,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QACxE,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC3C,SAAS,GAAG,cAAc,CAAC,SAAS,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IAC/D,MAAM,UAAU,GAAG,iBAAiB,CAAC,GAAG,QAAQ,IAAI,SAAS,EAAE,EAAE,OAAO,CAAC,CAAC;IAC1E,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;IAEhC,OAAO,CACL,GAAG;SACA,QAAQ,EAAE;QACX,gCAAgC;SAC/B,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,CACjC,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CACzB,GAAW,EACX,aAAsB,EACtB,KAA0B,EAC1B,KAAU;IAEV,IAAI,SAAiB,CAAC;IACtB,IAAI,KAAK,KAAK,eAAe,EAAE,CAAC;QAC9B,SAAS,GAAG,GAAG,CAAC;IAClB,CAAC;SAAM,IAAI,KAAK,KAAK,gBAAgB,EAAE,CAAC;QACtC,SAAS,GAAG,KAAK,CAAC;IACpB,CAAC;SAAM,CAAC;QACN,SAAS,GAAG,GAAG,CAAC;IAClB,CAAC;IAED,IAAI,WAAkB,CAAC;IACvB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,WAAW,GAAG,KAAK,CAAC;IACtB,CAAC;SAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,QAAQ,KAAK,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;QACrF,wFAAwF;QACxF,yFAAyF;QACzF,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;IAC7C,CAAC;SAAM,CAAC;QACN,WAAW,GAAG,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;IAED,MAAM,KAAK,GAAG,WAAW;SACtB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;QACT,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE,CAAC;YAClC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC,CAAC,CAAC,QAAQ,IAAI,OAAO,CAAC,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,8DAA8D,GAAG,QAAQ,CAAC,CAAC;QAC7F,CAAC;QAED,MAAM,QAAQ,GAAG,CAAC,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC9E,OAAO,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IACjE,CAAC,CAAC;SACD,IAAI,CAAC,SAAS,CAAC,CAAC;IAEnB,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC;AACrE,CAAC;AAED,SAAS,iBAAiB,CAAC,GAAW,EAAE,UAA6B,EAAE;;IACrE,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QAC7B,OAAO,GAAG,CAAC;IACb,CAAC;IACD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC;IAE5C,MAAM,YAAY,GAAa,EAAE,CAAC;IAClC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;QAC3C,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAQ,CAAC;QACtC,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1C,SAAS;QACX,CAAC;QAED,MAAM,WAAW,GAAG,2BAA2B,CAAC,KAAK,CAAC,CAAC;QACvD,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;QACnD,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,MAAA,KAAK,CAAC,OAAO,mCAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC/D,MAAM,KAAK,GAAG,WAAW,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;QAEhE,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;oBAC5B,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,MAAA,OAAO,CAAC,eAAe,mCAAI,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;gBAC5F,CAAC;YACH,CAAC;iBAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACxC,mGAAmG;gBACnG,KAAK,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC1D,YAAY,CAAC,IAAI,CACf,kBAAkB,CAAC,SAAS,EAAE,MAAA,OAAO,CAAC,eAAe,mCAAI,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAC9E,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,mDAAmD;gBACnD,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;aAAM,CAAC;YACN,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,MAAA,OAAO,CAAC,eAAe,mCAAI,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;QAChG,CAAC;IACH,CAAC;IAED,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;QAC5B,SAAS,CAAC,MAAM,IAAI,GAAG,CAAC;IAC1B,CAAC;IACD,SAAS,CAAC,MAAM,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3C,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,QAAgB,EAAE,OAA0B;;IACvE,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAC5B,OAAO,QAAQ,CAAC;IAClB,CAAC;IACD,MAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC;IAC1C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;QACtD,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,mBAAmB,GAAG,gCAAgC,CAAC,CAAC;QAC1E,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,OAAO,KAAK,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,6DAA6D,GAAG,QAAQ,CAAC,CAAC;QAC5F,CAAC;QACD,IAAI,KAAK,GAAG,KAAK,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAClF,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC7B,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;QACD,QAAQ,GAAG,MAAA,UAAU,CAAC,QAAQ,EAAE,IAAI,GAAG,GAAG,EAAE,KAAK,CAAC,mCAAI,EAAE,CAAC;IAC3D,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,cAAc,CACrB,SAAiB,EACjB,cAA8D,EAC9D,UAA6B,EAAE;;IAE/B,KAAK,MAAM,SAAS,IAAI,cAAc,EAAE,CAAC;QACvC,MAAM,aAAa,GAAG,OAAO,SAAS,KAAK,QAAQ,IAAI,CAAC,MAAA,SAAS,CAAC,aAAa,mCAAI,KAAK,CAAC,CAAC;QAC1F,IAAI,KAAK,GAAG,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;QAExE,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,CAAC,aAAa,EAAE,CAAC;YAC/C,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;QAED,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7D,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,UAAU,CACxB,KAAyB,EACzB,WAAmB,EACnB,YAAoB;IAEpB,OAAO,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;AAC5F,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PathParameterWithOptions, RequestParameters } from \"./common.js\";\n\ntype QueryParameterStyle = \"form\" | \"spaceDelimited\" | \"pipeDelimited\";\n\n/**\n * An object that can be passed as a query parameter, allowing for additional options to be set relating to how the parameter is encoded.\n */\ninterface QueryParameterWithOptions {\n  /**\n   * The value of the query parameter.\n   */\n  value: unknown;\n\n  /**\n   * If set to true, value must be an array. Setting this option to true will cause the array to be encoded as multiple query parameters.\n   * Setting it to false will cause the array values to be encoded as a single query parameter, with each value separated by a comma ','.\n   *\n   * For example, with `explode` set to true, a query parameter named \"foo\" with value [\"a\", \"b\", \"c\"] will be encoded as foo=a&foo=b&foo=c.\n   * If `explode` was set to false, the same example would instead be encouded as foo=a,b,c.\n   *\n   * Defaults to false.\n   */\n  explode?: boolean;\n\n  /**\n   * Style for encoding arrays. Three possible values:\n   * - \"form\": array values will be separated by a comma \",\" in the query parameter value.\n   * - \"spaceDelimited\": array values will be separated by a space (\" \", url-encoded to \"%20\").\n   * - \"pipeDelimited\": array values will be separated by a pipe (\"|\").\n   *\n   * Defaults to \"form\".\n   */\n  style?: QueryParameterStyle;\n}\n\nfunction isQueryParameterWithOptions(x: unknown): x is QueryParameterWithOptions {\n  const value = (x as QueryParameterWithOptions).value as any;\n  return (\n    value !== undefined && value.toString !== undefined && typeof value.toString === \"function\"\n  );\n}\n\n/**\n * Builds the request url, filling in query and path parameters\n * @param endpoint - base url which can be a template url\n * @param routePath - path to append to the endpoint\n * @param pathParameters - values of the path parameters\n * @param options - request parameters including query parameters\n * @returns a full url with path and query parameters\n */\nexport function buildRequestUrl(\n  endpoint: string,\n  routePath: string,\n  pathParameters: (string | number | PathParameterWithOptions)[],\n  options: RequestParameters = {},\n): string {\n  if (routePath.startsWith(\"https://\") || routePath.startsWith(\"http://\")) {\n    return routePath;\n  }\n  endpoint = buildBaseUrl(endpoint, options);\n  routePath = buildRoutePath(routePath, pathParameters, options);\n  const requestUrl = appendQueryParams(`${endpoint}/${routePath}`, options);\n  const url = new URL(requestUrl);\n\n  return (\n    url\n      .toString()\n      // Remove double forward slashes\n      .replace(/([^:]\\/)\\/+/g, \"$1\")\n  );\n}\n\nfunction getQueryParamValue(\n  key: string,\n  allowReserved: boolean,\n  style: QueryParameterStyle,\n  param: any,\n): string {\n  let separator: string;\n  if (style === \"pipeDelimited\") {\n    separator = \"|\";\n  } else if (style === \"spaceDelimited\") {\n    separator = \"%20\";\n  } else {\n    separator = \",\";\n  }\n\n  let paramValues: any[];\n  if (Array.isArray(param)) {\n    paramValues = param;\n  } else if (typeof param === \"object\" && param.toString === Object.prototype.toString) {\n    // If the parameter is an object without a custom toString implementation (e.g. a Date),\n    // then we should deconstruct the object into an array [key1, value1, key2, value2, ...].\n    paramValues = Object.entries(param).flat();\n  } else {\n    paramValues = [param];\n  }\n\n  const value = paramValues\n    .map((p) => {\n      if (p === null || p === undefined) {\n        return \"\";\n      }\n\n      if (!p.toString || typeof p.toString !== \"function\") {\n        throw new Error(`Query parameters must be able to be represented as string, ${key} can't`);\n      }\n\n      const rawValue = p.toISOString !== undefined ? p.toISOString() : p.toString();\n      return allowReserved ? rawValue : encodeURIComponent(rawValue);\n    })\n    .join(separator);\n\n  return `${allowReserved ? key : encodeURIComponent(key)}=${value}`;\n}\n\nfunction appendQueryParams(url: string, options: RequestParameters = {}): string {\n  if (!options.queryParameters) {\n    return url;\n  }\n  const parsedUrl = new URL(url);\n  const queryParams = options.queryParameters;\n\n  const paramStrings: string[] = [];\n  for (const key of Object.keys(queryParams)) {\n    const param = queryParams[key] as any;\n    if (param === undefined || param === null) {\n      continue;\n    }\n\n    const hasMetadata = isQueryParameterWithOptions(param);\n    const rawValue = hasMetadata ? param.value : param;\n    const explode = hasMetadata ? (param.explode ?? false) : false;\n    const style = hasMetadata && param.style ? param.style : \"form\";\n\n    if (explode) {\n      if (Array.isArray(rawValue)) {\n        for (const item of rawValue) {\n          paramStrings.push(getQueryParamValue(key, options.skipUrlEncoding ?? false, style, item));\n        }\n      } else if (typeof rawValue === \"object\") {\n        // For object explode, the name of the query parameter is ignored and we use the object key instead\n        for (const [actualKey, value] of Object.entries(rawValue)) {\n          paramStrings.push(\n            getQueryParamValue(actualKey, options.skipUrlEncoding ?? false, style, value),\n          );\n        }\n      } else {\n        // Explode doesn't really make sense for primitives\n        throw new Error(\"explode can only be set to true for objects and arrays\");\n      }\n    } else {\n      paramStrings.push(getQueryParamValue(key, options.skipUrlEncoding ?? false, style, rawValue));\n    }\n  }\n\n  if (parsedUrl.search !== \"\") {\n    parsedUrl.search += \"&\";\n  }\n  parsedUrl.search += paramStrings.join(\"&\");\n  return parsedUrl.toString();\n}\n\nexport function buildBaseUrl(endpoint: string, options: RequestParameters): string {\n  if (!options.pathParameters) {\n    return endpoint;\n  }\n  const pathParams = options.pathParameters;\n  for (const [key, param] of Object.entries(pathParams)) {\n    if (param === undefined || param === null) {\n      throw new Error(`Path parameters ${key} must not be undefined or null`);\n    }\n    if (!param.toString || typeof param.toString !== \"function\") {\n      throw new Error(`Path parameters must be able to be represented as string, ${key} can't`);\n    }\n    let value = param.toISOString !== undefined ? param.toISOString() : String(param);\n    if (!options.skipUrlEncoding) {\n      value = encodeURIComponent(param);\n    }\n    endpoint = replaceAll(endpoint, `{${key}}`, value) ?? \"\";\n  }\n  return endpoint;\n}\n\nfunction buildRoutePath(\n  routePath: string,\n  pathParameters: (string | number | PathParameterWithOptions)[],\n  options: RequestParameters = {},\n): string {\n  for (const pathParam of pathParameters) {\n    const allowReserved = typeof pathParam === \"object\" && (pathParam.allowReserved ?? false);\n    let value = typeof pathParam === \"object\" ? pathParam.value : pathParam;\n\n    if (!options.skipUrlEncoding && !allowReserved) {\n      value = encodeURIComponent(value);\n    }\n\n    routePath = routePath.replace(/\\{[\\w-]+\\}/, String(value));\n  }\n  return routePath;\n}\n\n/**\n * Replace all of the instances of searchValue in value with the provided replaceValue.\n * @param value - The value to search and replace in.\n * @param searchValue - The value to search for in the value argument.\n * @param replaceValue - The value to replace searchValue with in the value argument.\n * @returns The value where each instance of searchValue was replaced with replacedValue.\n */\nexport function replaceAll(\n  value: string | undefined,\n  searchValue: string,\n  replaceValue: string,\n): string | undefined {\n  return !value || !searchValue ? value : value.split(searchValue).join(replaceValue || \"\");\n}\n"]}
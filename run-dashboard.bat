@echo off
echo ========================================
echo    RC DASHBOARD - ENHANCED VERSION
echo ========================================
echo.

echo [1/2] Starting backend...
cd backend
start "RC Dashboard Backend" cmd /k "echo Starting RC Dashboard Backend... && npm start"

echo [2/2] Waiting and opening dashboard...
echo Please wait 15 seconds for backend to start...
timeout /t 15 /nobreak >nul

cd ..
start simple-dashboard.html

echo.
echo ========================================
echo    DASHBOARD FEATURES
echo ========================================
echo.
echo ✅ UNLIMITED ROWS - Shows ALL pending RCs
echo ✅ PERFECT AUTO-SLIDE - Scrolls through all data
echo ✅ ENHANCED AESTHETICS - Premium visual design
echo ✅ AUTO-REFRESH - Updates every 5 minutes
echo.
echo The dashboard will:
echo 1. Show ALL pending route cards (unlimited rows)
echo 2. Auto-scroll through data slowly (5 sec intervals)
echo 3. Move to next page when bottom is reached
echo 4. Continue cycling through all data
echo 5. Refresh automatically every 5 minutes
echo.
echo Keep the backend window open!
echo Press any key to close this window...
pause >nul

{"version": 3, "file": "login7-payload.js", "names": ["_sprintfJs", "require", "_tdsVersions", "FLAGS_1", "ENDIAN_LITTLE", "ENDIAN_BIG", "CHARSET_ASCII", "CHARSET_EBCDIC", "FLOAT_IEEE_754", "FLOAT_VAX", "FLOAT_ND5000", "BCP_DUMPLOAD_ON", "BCP_DUMPLOAD_OFF", "USE_DB_ON", "USE_DB_OFF", "INIT_DB_WARN", "INIT_DB_FATAL", "SET_LANG_WARN_OFF", "SET_LANG_WARN_ON", "FLAGS_2", "INIT_LANG_WARN", "INIT_LANG_FATAL", "ODBC_OFF", "ODBC_ON", "F_TRAN_BOUNDARY", "F_CACHE_CONNECT", "USER_NORMAL", "USER_SERVER", "USER_REMUSER", "USER_SQLREPL", "INTEGRATED_SECURITY_OFF", "INTEGRATED_SECURITY_ON", "TYPE_FLAGS", "SQL_DFLT", "SQL_TSQL", "OLEDB_OFF", "OLEDB_ON", "READ_WRITE_INTENT", "READ_ONLY_INTENT", "FLAGS_3", "CHANGE_PASSWORD_NO", "CHANGE_PASSWORD_YES", "BINARY_XML", "SPAWN_USER_INSTANCE", "UNKNOWN_COLLATION_HANDLING", "EXTENSION_USED", "FEDAUTH_OPTIONS", "FEATURE_ID", "LIBRARY_SECURITYTOKEN", "LIBRARY_ADAL", "FEDAUTH_YES_ECHO", "FEDAUTH_NO_ECHO", "ADAL_WORKFLOW_USER_PASS", "ADAL_WORKFLOW_INTEGRATED", "FEATURE_EXT_TERMINATOR", "Login7Payload", "constructor", "tdsVersion", "packetSize", "clientProgVer", "clientPid", "connectionId", "clientTimeZone", "clientLcid", "readOnlyIntent", "initDbFatal", "fedAuth", "undefined", "userName", "password", "serverName", "appName", "hostname", "libraryName", "language", "database", "clientId", "sspi", "attachDbFile", "changePassword", "<PERSON><PERSON><PERSON><PERSON>", "fixedData", "<PERSON><PERSON><PERSON>", "alloc", "buffers", "offset", "dataOffset", "length", "writeUInt32LE", "writeUInt8", "buildOptionFlags1", "buildOptionFlags2", "buildTypeFlags", "buildOptionFlags3", "writeInt32LE", "writeUInt16LE", "buffer", "from", "push", "scramblePassword", "extensions", "buildFeatureExt", "extensionOffset", "copy", "data", "concat", "flags1", "type", "echo", "workflow", "token", "fedAuthToken", "buf", "versions", "UTF8_SUPPORT_FEATURE_ID", "UTF8_SUPPORT_CLIENT_SUPPORTS_UTF8", "flags2", "typeFlags", "b", "len", "byte", "lowNibble", "highNibble", "toString", "indent", "sprintf", "_default", "exports", "default", "module"], "sources": ["../src/login7-payload.ts"], "sourcesContent": ["import { sprintf } from 'sprintf-js';\nimport { versions } from './tds-versions';\n\nconst FLAGS_1 = {\n  ENDIAN_LITTLE: 0x00,\n  ENDIAN_BIG: 0x01,\n  CHARSET_ASCII: 0x00,\n  CHARSET_EBCDIC: 0x02,\n  FLOAT_IEEE_754: 0x00,\n  FLOAT_VAX: 0x04,\n  FLOAT_ND5000: 0x08,\n  BCP_DUMPLOAD_ON: 0x00,\n  BCP_DUMPLOAD_OFF: 0x10,\n  USE_DB_ON: 0x00,\n  USE_DB_OFF: 0x20,\n  INIT_DB_WARN: 0x00,\n  INIT_DB_FATAL: 0x40,\n  SET_LANG_WARN_OFF: 0x00,\n  SET_LANG_WARN_ON: 0x80\n};\n\nconst FLAGS_2 = {\n  INIT_LANG_WARN: 0x00,\n  INIT_LANG_FATAL: 0x01,\n  ODBC_OFF: 0x00,\n  ODBC_ON: 0x02,\n  F_TRAN_BOUNDARY: 0x04,\n  F_CACHE_CONNECT: 0x08,\n  USER_NORMAL: 0x00,\n  USER_SERVER: 0x10,\n  USER_REMUSER: 0x20,\n  USER_SQLREPL: 0x40,\n  INTEGRATED_SECURITY_OFF: 0x00,\n  INTEGRATED_SECURITY_ON: 0x80\n};\n\nconst TYPE_FLAGS = {\n  SQL_DFLT: 0x00,\n  SQL_TSQL: 0x08,\n  OLEDB_OFF: 0x00,\n  OLEDB_ON: 0x10,\n  READ_WRITE_INTENT: 0x00,\n  READ_ONLY_INTENT: 0x20\n};\n\nconst FLAGS_3 = {\n  CHANGE_PASSWORD_NO: 0x00,\n  CHANGE_PASSWORD_YES: 0x01,\n  BINARY_XML: 0x02,\n  SPAWN_USER_INSTANCE: 0x04,\n  UNKNOWN_COLLATION_HANDLING: 0x08,\n  EXTENSION_USED: 0x10\n};\n\nconst FEDAUTH_OPTIONS = {\n  FEATURE_ID: 0x02,\n  LIBRARY_SECURITYTOKEN: 0x01,\n  LIBRARY_ADAL: 0x02,\n  FEDAUTH_YES_ECHO: 0x01,\n  FEDAUTH_NO_ECHO: 0x00,\n  ADAL_WORKFLOW_USER_PASS: 0x01,\n  ADAL_WORKFLOW_INTEGRATED: 0x02\n};\n\nconst FEATURE_EXT_TERMINATOR = 0xFF;\n\ninterface Options {\n  tdsVersion: number;\n  packetSize: number;\n  clientProgVer: number;\n  clientPid: number;\n  connectionId: number;\n  clientTimeZone: number;\n  clientLcid: number;\n}\n\n/*\n  s2.2.6.3\n */\nclass Login7Payload {\n  declare tdsVersion: number;\n  declare packetSize: number;\n  declare clientProgVer: number;\n  declare clientPid: number;\n  declare connectionId: number;\n  declare clientTimeZone: number;\n  declare clientLcid: number;\n\n  declare readOnlyIntent: boolean;\n  declare initDbFatal: boolean;\n\n  declare userName: string | undefined;\n  declare password: string | undefined;\n  declare serverName: string | undefined;\n  declare appName: string | undefined;\n  declare hostname: string | undefined;\n  declare libraryName: string | undefined;\n  declare language: string | undefined;\n  declare database: string | undefined;\n  declare clientId: Buffer | undefined;\n  declare sspi: Buffer | undefined;\n  declare attachDbFile: string | undefined;\n  declare changePassword: string | undefined;\n\n  declare fedAuth: { type: 'ADAL', echo: boolean, workflow: 'default' | 'integrated' } | { type: 'SECURITYTOKEN', echo: boolean, fedAuthToken: string } | undefined;\n\n  constructor({ tdsVersion, packetSize, clientProgVer, clientPid, connectionId, clientTimeZone, clientLcid }: Options) {\n    this.tdsVersion = tdsVersion;\n    this.packetSize = packetSize;\n    this.clientProgVer = clientProgVer;\n    this.clientPid = clientPid;\n    this.connectionId = connectionId;\n    this.clientTimeZone = clientTimeZone;\n    this.clientLcid = clientLcid;\n\n    this.readOnlyIntent = false;\n    this.initDbFatal = false;\n\n    this.fedAuth = undefined;\n\n    this.userName = undefined;\n    this.password = undefined;\n    this.serverName = undefined;\n    this.appName = undefined;\n    this.hostname = undefined;\n    this.libraryName = undefined;\n    this.language = undefined;\n    this.database = undefined;\n    this.clientId = undefined;\n    this.sspi = undefined;\n    this.attachDbFile = undefined;\n    this.changePassword = undefined;\n  }\n\n  toBuffer() {\n    const fixedData = Buffer.alloc(94);\n    const buffers = [fixedData];\n\n    let offset = 0;\n    let dataOffset = fixedData.length;\n\n    // Length: 4-byte\n    offset = fixedData.writeUInt32LE(0, offset);\n\n    // TDSVersion: 4-byte\n    offset = fixedData.writeUInt32LE(this.tdsVersion, offset);\n\n    // PacketSize: 4-byte\n    offset = fixedData.writeUInt32LE(this.packetSize, offset);\n\n    // ClientProgVer: 4-byte\n    offset = fixedData.writeUInt32LE(this.clientProgVer, offset);\n\n    // ClientPID: 4-byte\n    offset = fixedData.writeUInt32LE(this.clientPid, offset);\n\n    // ConnectionID: 4-byte\n    offset = fixedData.writeUInt32LE(this.connectionId, offset);\n\n    // OptionFlags1: 1-byte\n    offset = fixedData.writeUInt8(this.buildOptionFlags1(), offset);\n\n    // OptionFlags2: 1-byte\n    offset = fixedData.writeUInt8(this.buildOptionFlags2(), offset);\n\n    // TypeFlags: 1-byte\n    offset = fixedData.writeUInt8(this.buildTypeFlags(), offset);\n\n    // OptionFlags3: 1-byte\n    offset = fixedData.writeUInt8(this.buildOptionFlags3(), offset);\n\n    // ClientTimZone: 4-byte\n    offset = fixedData.writeInt32LE(this.clientTimeZone, offset);\n\n    // ClientLCID: 4-byte\n    offset = fixedData.writeUInt32LE(this.clientLcid, offset);\n\n    // ibHostName: 2-byte\n    offset = fixedData.writeUInt16LE(dataOffset, offset);\n\n    // cchHostName: 2-byte\n    if (this.hostname) {\n      const buffer = Buffer.from(this.hostname, 'ucs2');\n\n      offset = fixedData.writeUInt16LE(buffer.length / 2, offset);\n      dataOffset += buffer.length;\n\n      buffers.push(buffer);\n    } else {\n      offset = fixedData.writeUInt16LE(dataOffset, offset);\n    }\n\n    // ibUserName: 2-byte\n    offset = fixedData.writeUInt16LE(dataOffset, offset);\n\n    // cchUserName: 2-byte\n    if (this.userName) {\n      const buffer = Buffer.from(this.userName, 'ucs2');\n\n      offset = fixedData.writeUInt16LE(buffer.length / 2, offset);\n      dataOffset += buffer.length;\n\n      buffers.push(buffer);\n    } else {\n      offset = fixedData.writeUInt16LE(0, offset);\n    }\n\n    // ibPassword: 2-byte\n    offset = fixedData.writeUInt16LE(dataOffset, offset);\n\n    // cchPassword: 2-byte\n    if (this.password) {\n      const buffer = Buffer.from(this.password, 'ucs2');\n\n      offset = fixedData.writeUInt16LE(buffer.length / 2, offset);\n      dataOffset += buffer.length;\n\n      buffers.push(this.scramblePassword(buffer));\n    } else {\n      offset = fixedData.writeUInt16LE(0, offset);\n    }\n\n    // ibAppName: 2-byte\n    offset = fixedData.writeUInt16LE(dataOffset, offset);\n\n    // cchAppName: 2-byte\n    if (this.appName) {\n      const buffer = Buffer.from(this.appName, 'ucs2');\n\n      offset = fixedData.writeUInt16LE(buffer.length / 2, offset);\n      dataOffset += buffer.length;\n\n      buffers.push(buffer);\n    } else {\n      offset = fixedData.writeUInt16LE(0, offset);\n    }\n\n    // ibServerName: 2-byte\n    offset = fixedData.writeUInt16LE(dataOffset, offset);\n\n    // cchServerName: 2-byte\n    if (this.serverName) {\n      const buffer = Buffer.from(this.serverName, 'ucs2');\n\n      offset = fixedData.writeUInt16LE(buffer.length / 2, offset);\n      dataOffset += buffer.length;\n\n      buffers.push(buffer);\n    } else {\n      offset = fixedData.writeUInt16LE(0, offset);\n    }\n\n    // (ibUnused / ibExtension): 2-byte\n    offset = fixedData.writeUInt16LE(dataOffset, offset);\n\n    // (cchUnused / cbExtension): 2-byte\n    const extensions = this.buildFeatureExt();\n    offset = fixedData.writeUInt16LE(4, offset);\n    const extensionOffset = Buffer.alloc(4);\n    extensionOffset.writeUInt32LE(dataOffset += 4, 0);\n    dataOffset += extensions.length;\n    buffers.push(extensionOffset, extensions);\n\n    // ibCltIntName: 2-byte\n    offset = fixedData.writeUInt16LE(dataOffset, offset);\n\n    // cchCltIntName: 2-byte\n    if (this.libraryName) {\n      const buffer = Buffer.from(this.libraryName, 'ucs2');\n\n      offset = fixedData.writeUInt16LE(buffer.length / 2, offset);\n      dataOffset += buffer.length;\n\n      buffers.push(buffer);\n    } else {\n      offset = fixedData.writeUInt16LE(0, offset);\n    }\n\n    // ibLanguage: 2-byte\n    offset = fixedData.writeUInt16LE(dataOffset, offset);\n\n    // cchLanguage: 2-byte\n    if (this.language) {\n      const buffer = Buffer.from(this.language, 'ucs2');\n\n      offset = fixedData.writeUInt16LE(buffer.length / 2, offset);\n      dataOffset += buffer.length;\n\n      buffers.push(buffer);\n    } else {\n      offset = fixedData.writeUInt16LE(0, offset);\n    }\n\n    // ibDatabase: 2-byte\n    offset = fixedData.writeUInt16LE(dataOffset, offset);\n\n    // cchDatabase: 2-byte\n    if (this.database) {\n      const buffer = Buffer.from(this.database, 'ucs2');\n\n      offset = fixedData.writeUInt16LE(buffer.length / 2, offset);\n      dataOffset += buffer.length;\n\n      buffers.push(buffer);\n    } else {\n      offset = fixedData.writeUInt16LE(0, offset);\n    }\n\n    // ClientID: 6-byte\n    if (this.clientId) {\n      this.clientId.copy(fixedData, offset, 0, 6);\n    }\n    offset += 6;\n\n    // ibSSPI: 2-byte\n    offset = fixedData.writeUInt16LE(dataOffset, offset);\n\n    // cbSSPI: 2-byte\n    if (this.sspi) {\n      if (this.sspi.length > 65535) {\n        offset = fixedData.writeUInt16LE(65535, offset);\n      } else {\n        offset = fixedData.writeUInt16LE(this.sspi.length, offset);\n      }\n\n      buffers.push(this.sspi);\n    } else {\n      offset = fixedData.writeUInt16LE(0, offset);\n    }\n\n    // ibAtchDBFile: 2-byte\n    offset = fixedData.writeUInt16LE(dataOffset, offset);\n\n    // cchAtchDBFile: 2-byte\n    if (this.attachDbFile) {\n      const buffer = Buffer.from(this.attachDbFile, 'ucs2');\n\n      offset = fixedData.writeUInt16LE(buffer.length / 2, offset);\n      dataOffset += buffer.length;\n\n      buffers.push(buffer);\n    } else {\n      offset = fixedData.writeUInt16LE(0, offset);\n    }\n\n    // ibChangePassword: 2-byte\n    offset = fixedData.writeUInt16LE(dataOffset, offset);\n\n    // cchChangePassword: 2-byte\n    if (this.changePassword) {\n      const buffer = Buffer.from(this.changePassword, 'ucs2');\n\n      offset = fixedData.writeUInt16LE(buffer.length / 2, offset);\n      dataOffset += buffer.length;\n\n      buffers.push(buffer);\n    } else {\n      offset = fixedData.writeUInt16LE(0, offset);\n    }\n\n    // cbSSPILong: 4-byte\n    if (this.sspi && this.sspi.length > 65535) {\n      fixedData.writeUInt32LE(this.sspi.length, offset);\n    } else {\n      fixedData.writeUInt32LE(0, offset);\n    }\n\n    const data = Buffer.concat(buffers);\n    data.writeUInt32LE(data.length, 0);\n    return data;\n  }\n\n  buildOptionFlags1() {\n    let flags1 = FLAGS_1.ENDIAN_LITTLE | FLAGS_1.CHARSET_ASCII | FLAGS_1.FLOAT_IEEE_754 | FLAGS_1.BCP_DUMPLOAD_OFF | FLAGS_1.USE_DB_OFF | FLAGS_1.SET_LANG_WARN_ON;\n    if (this.initDbFatal) {\n      flags1 |= FLAGS_1.INIT_DB_FATAL;\n    } else {\n      flags1 |= FLAGS_1.INIT_DB_WARN;\n    }\n    return flags1;\n  }\n\n  buildFeatureExt() {\n    const buffers = [];\n\n    const fedAuth = this.fedAuth;\n    if (fedAuth) {\n      switch (fedAuth.type) {\n        case 'ADAL':\n          const buffer = Buffer.alloc(7);\n          buffer.writeUInt8(FEDAUTH_OPTIONS.FEATURE_ID, 0);\n          buffer.writeUInt32LE(2, 1);\n          buffer.writeUInt8((FEDAUTH_OPTIONS.LIBRARY_ADAL << 1) | (fedAuth.echo ? FEDAUTH_OPTIONS.FEDAUTH_YES_ECHO : FEDAUTH_OPTIONS.FEDAUTH_NO_ECHO), 5);\n          buffer.writeUInt8(fedAuth.workflow === 'integrated' ? 0x02 : FEDAUTH_OPTIONS.ADAL_WORKFLOW_USER_PASS, 6);\n          buffers.push(buffer);\n          break;\n\n        case 'SECURITYTOKEN':\n          const token = Buffer.from(fedAuth.fedAuthToken, 'ucs2');\n          const buf = Buffer.alloc(10);\n\n          let offset = 0;\n          offset = buf.writeUInt8(FEDAUTH_OPTIONS.FEATURE_ID, offset);\n          offset = buf.writeUInt32LE(token.length + 4 + 1, offset);\n          offset = buf.writeUInt8((FEDAUTH_OPTIONS.LIBRARY_SECURITYTOKEN << 1) | (fedAuth.echo ? FEDAUTH_OPTIONS.FEDAUTH_YES_ECHO : FEDAUTH_OPTIONS.FEDAUTH_NO_ECHO), offset);\n          buf.writeInt32LE(token.length, offset);\n\n          buffers.push(buf);\n          buffers.push(token);\n\n          break;\n      }\n    }\n\n    if (this.tdsVersion >= versions['7_4']) {\n      // Signal UTF-8 support: Value 0x0A, bit 0 must be set to 1. Added in TDS 7.4.\n      const UTF8_SUPPORT_FEATURE_ID = 0x0a;\n      const UTF8_SUPPORT_CLIENT_SUPPORTS_UTF8 = 0x01;\n      const buf = Buffer.alloc(6);\n      buf.writeUInt8(UTF8_SUPPORT_FEATURE_ID, 0);\n      buf.writeUInt32LE(1, 1);\n      buf.writeUInt8(UTF8_SUPPORT_CLIENT_SUPPORTS_UTF8, 5);\n      buffers.push(buf);\n    }\n\n    buffers.push(Buffer.from([FEATURE_EXT_TERMINATOR]));\n\n    return Buffer.concat(buffers);\n  }\n\n  buildOptionFlags2() {\n    let flags2 = FLAGS_2.INIT_LANG_WARN | FLAGS_2.ODBC_OFF | FLAGS_2.USER_NORMAL;\n    if (this.sspi) {\n      flags2 |= FLAGS_2.INTEGRATED_SECURITY_ON;\n    } else {\n      flags2 |= FLAGS_2.INTEGRATED_SECURITY_OFF;\n    }\n    return flags2;\n  }\n\n  buildTypeFlags() {\n    let typeFlags = TYPE_FLAGS.SQL_DFLT | TYPE_FLAGS.OLEDB_OFF;\n    if (this.readOnlyIntent) {\n      typeFlags |= TYPE_FLAGS.READ_ONLY_INTENT;\n    } else {\n      typeFlags |= TYPE_FLAGS.READ_WRITE_INTENT;\n    }\n    return typeFlags;\n  }\n\n  buildOptionFlags3() {\n    return FLAGS_3.CHANGE_PASSWORD_NO | FLAGS_3.UNKNOWN_COLLATION_HANDLING | FLAGS_3.EXTENSION_USED;\n  }\n\n  scramblePassword(password: Buffer) {\n    for (let b = 0, len = password.length; b < len; b++) {\n      let byte = password[b];\n      const lowNibble = byte & 0x0f;\n      const highNibble = byte >> 4;\n      byte = (lowNibble << 4) | highNibble;\n      byte = byte ^ 0xa5;\n      password[b] = byte;\n    }\n    return password;\n  }\n\n  toString(indent = '') {\n    return indent + 'Login7 - ' +\n      sprintf('TDS:0x%08X, PacketSize:0x%08X, ClientProgVer:0x%08X, ClientPID:0x%08X, ConnectionID:0x%08X',\n              this.tdsVersion, this.packetSize, this.clientProgVer, this.clientPid, this.connectionId\n      ) + '\\n' + indent + '         ' +\n      sprintf('Flags1:0x%02X, Flags2:0x%02X, TypeFlags:0x%02X, Flags3:0x%02X, ClientTimezone:%d, ClientLCID:0x%08X',\n              this.buildOptionFlags1(), this.buildOptionFlags2(), this.buildTypeFlags(), this.buildOptionFlags3(), this.clientTimeZone, this.clientLcid\n      ) + '\\n' + indent + '         ' +\n      sprintf(\"Hostname:'%s', Username:'%s', Password:'%s', AppName:'%s', ServerName:'%s', LibraryName:'%s'\",\n              this.hostname, this.userName, this.password, this.appName, this.serverName, this.libraryName\n      ) + '\\n' + indent + '         ' +\n      sprintf(\"Language:'%s', Database:'%s', SSPI:'%s', AttachDbFile:'%s', ChangePassword:'%s'\",\n              this.language, this.database, this.sspi, this.attachDbFile, this.changePassword\n      );\n  }\n}\n\nexport default Login7Payload;\nmodule.exports = Login7Payload;\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,MAAME,OAAO,GAAG;EACdC,aAAa,EAAE,IAAI;EACnBC,UAAU,EAAE,IAAI;EAChBC,aAAa,EAAE,IAAI;EACnBC,cAAc,EAAE,IAAI;EACpBC,cAAc,EAAE,IAAI;EACpBC,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE,IAAI;EAClBC,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE,IAAI;EACtBC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,IAAI;EACnBC,iBAAiB,EAAE,IAAI;EACvBC,gBAAgB,EAAE;AACpB,CAAC;AAED,MAAMC,OAAO,GAAG;EACdC,cAAc,EAAE,IAAI;EACpBC,eAAe,EAAE,IAAI;EACrBC,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,IAAI;EACbC,eAAe,EAAE,IAAI;EACrBC,eAAe,EAAE,IAAI;EACrBC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBC,YAAY,EAAE,IAAI;EAClBC,uBAAuB,EAAE,IAAI;EAC7BC,sBAAsB,EAAE;AAC1B,CAAC;AAED,MAAMC,UAAU,GAAG;EACjBC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,IAAI;EACdC,iBAAiB,EAAE,IAAI;EACvBC,gBAAgB,EAAE;AACpB,CAAC;AAED,MAAMC,OAAO,GAAG;EACdC,kBAAkB,EAAE,IAAI;EACxBC,mBAAmB,EAAE,IAAI;EACzBC,UAAU,EAAE,IAAI;EAChBC,mBAAmB,EAAE,IAAI;EACzBC,0BAA0B,EAAE,IAAI;EAChCC,cAAc,EAAE;AAClB,CAAC;AAED,MAAMC,eAAe,GAAG;EACtBC,UAAU,EAAE,IAAI;EAChBC,qBAAqB,EAAE,IAAI;EAC3BC,YAAY,EAAE,IAAI;EAClBC,gBAAgB,EAAE,IAAI;EACtBC,eAAe,EAAE,IAAI;EACrBC,uBAAuB,EAAE,IAAI;EAC7BC,wBAAwB,EAAE;AAC5B,CAAC;AAED,MAAMC,sBAAsB,GAAG,IAAI;AAYnC;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EA2BlBC,WAAWA,CAAC;IAAEC,UAAU;IAAEC,UAAU;IAAEC,aAAa;IAAEC,SAAS;IAAEC,YAAY;IAAEC,cAAc;IAAEC;EAAoB,CAAC,EAAE;IACnH,IAAI,CAACN,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAE5B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,WAAW,GAAG,KAAK;IAExB,IAAI,CAACC,OAAO,GAAGC,SAAS;IAExB,IAAI,CAACC,QAAQ,GAAGD,SAAS;IACzB,IAAI,CAACE,QAAQ,GAAGF,SAAS;IACzB,IAAI,CAACG,UAAU,GAAGH,SAAS;IAC3B,IAAI,CAACI,OAAO,GAAGJ,SAAS;IACxB,IAAI,CAACK,QAAQ,GAAGL,SAAS;IACzB,IAAI,CAACM,WAAW,GAAGN,SAAS;IAC5B,IAAI,CAACO,QAAQ,GAAGP,SAAS;IACzB,IAAI,CAACQ,QAAQ,GAAGR,SAAS;IACzB,IAAI,CAACS,QAAQ,GAAGT,SAAS;IACzB,IAAI,CAACU,IAAI,GAAGV,SAAS;IACrB,IAAI,CAACW,YAAY,GAAGX,SAAS;IAC7B,IAAI,CAACY,cAAc,GAAGZ,SAAS;EACjC;EAEAa,QAAQA,CAAA,EAAG;IACT,MAAMC,SAAS,GAAGC,MAAM,CAACC,KAAK,CAAC,EAAE,CAAC;IAClC,MAAMC,OAAO,GAAG,CAACH,SAAS,CAAC;IAE3B,IAAII,MAAM,GAAG,CAAC;IACd,IAAIC,UAAU,GAAGL,SAAS,CAACM,MAAM;;IAEjC;IACAF,MAAM,GAAGJ,SAAS,CAACO,aAAa,CAAC,CAAC,EAAEH,MAAM,CAAC;;IAE3C;IACAA,MAAM,GAAGJ,SAAS,CAACO,aAAa,CAAC,IAAI,CAAC/B,UAAU,EAAE4B,MAAM,CAAC;;IAEzD;IACAA,MAAM,GAAGJ,SAAS,CAACO,aAAa,CAAC,IAAI,CAAC9B,UAAU,EAAE2B,MAAM,CAAC;;IAEzD;IACAA,MAAM,GAAGJ,SAAS,CAACO,aAAa,CAAC,IAAI,CAAC7B,aAAa,EAAE0B,MAAM,CAAC;;IAE5D;IACAA,MAAM,GAAGJ,SAAS,CAACO,aAAa,CAAC,IAAI,CAAC5B,SAAS,EAAEyB,MAAM,CAAC;;IAExD;IACAA,MAAM,GAAGJ,SAAS,CAACO,aAAa,CAAC,IAAI,CAAC3B,YAAY,EAAEwB,MAAM,CAAC;;IAE3D;IACAA,MAAM,GAAGJ,SAAS,CAACQ,UAAU,CAAC,IAAI,CAACC,iBAAiB,CAAC,CAAC,EAAEL,MAAM,CAAC;;IAE/D;IACAA,MAAM,GAAGJ,SAAS,CAACQ,UAAU,CAAC,IAAI,CAACE,iBAAiB,CAAC,CAAC,EAAEN,MAAM,CAAC;;IAE/D;IACAA,MAAM,GAAGJ,SAAS,CAACQ,UAAU,CAAC,IAAI,CAACG,cAAc,CAAC,CAAC,EAAEP,MAAM,CAAC;;IAE5D;IACAA,MAAM,GAAGJ,SAAS,CAACQ,UAAU,CAAC,IAAI,CAACI,iBAAiB,CAAC,CAAC,EAAER,MAAM,CAAC;;IAE/D;IACAA,MAAM,GAAGJ,SAAS,CAACa,YAAY,CAAC,IAAI,CAAChC,cAAc,EAAEuB,MAAM,CAAC;;IAE5D;IACAA,MAAM,GAAGJ,SAAS,CAACO,aAAa,CAAC,IAAI,CAACzB,UAAU,EAAEsB,MAAM,CAAC;;IAEzD;IACAA,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACT,UAAU,EAAED,MAAM,CAAC;;IAEpD;IACA,IAAI,IAAI,CAACb,QAAQ,EAAE;MACjB,MAAMwB,MAAM,GAAGd,MAAM,CAACe,IAAI,CAAC,IAAI,CAACzB,QAAQ,EAAE,MAAM,CAAC;MAEjDa,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACC,MAAM,CAACT,MAAM,GAAG,CAAC,EAAEF,MAAM,CAAC;MAC3DC,UAAU,IAAIU,MAAM,CAACT,MAAM;MAE3BH,OAAO,CAACc,IAAI,CAACF,MAAM,CAAC;IACtB,CAAC,MAAM;MACLX,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACT,UAAU,EAAED,MAAM,CAAC;IACtD;;IAEA;IACAA,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACT,UAAU,EAAED,MAAM,CAAC;;IAEpD;IACA,IAAI,IAAI,CAACjB,QAAQ,EAAE;MACjB,MAAM4B,MAAM,GAAGd,MAAM,CAACe,IAAI,CAAC,IAAI,CAAC7B,QAAQ,EAAE,MAAM,CAAC;MAEjDiB,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACC,MAAM,CAACT,MAAM,GAAG,CAAC,EAAEF,MAAM,CAAC;MAC3DC,UAAU,IAAIU,MAAM,CAACT,MAAM;MAE3BH,OAAO,CAACc,IAAI,CAACF,MAAM,CAAC;IACtB,CAAC,MAAM;MACLX,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAAC,CAAC,EAAEV,MAAM,CAAC;IAC7C;;IAEA;IACAA,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACT,UAAU,EAAED,MAAM,CAAC;;IAEpD;IACA,IAAI,IAAI,CAAChB,QAAQ,EAAE;MACjB,MAAM2B,MAAM,GAAGd,MAAM,CAACe,IAAI,CAAC,IAAI,CAAC5B,QAAQ,EAAE,MAAM,CAAC;MAEjDgB,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACC,MAAM,CAACT,MAAM,GAAG,CAAC,EAAEF,MAAM,CAAC;MAC3DC,UAAU,IAAIU,MAAM,CAACT,MAAM;MAE3BH,OAAO,CAACc,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAACH,MAAM,CAAC,CAAC;IAC7C,CAAC,MAAM;MACLX,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAAC,CAAC,EAAEV,MAAM,CAAC;IAC7C;;IAEA;IACAA,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACT,UAAU,EAAED,MAAM,CAAC;;IAEpD;IACA,IAAI,IAAI,CAACd,OAAO,EAAE;MAChB,MAAMyB,MAAM,GAAGd,MAAM,CAACe,IAAI,CAAC,IAAI,CAAC1B,OAAO,EAAE,MAAM,CAAC;MAEhDc,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACC,MAAM,CAACT,MAAM,GAAG,CAAC,EAAEF,MAAM,CAAC;MAC3DC,UAAU,IAAIU,MAAM,CAACT,MAAM;MAE3BH,OAAO,CAACc,IAAI,CAACF,MAAM,CAAC;IACtB,CAAC,MAAM;MACLX,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAAC,CAAC,EAAEV,MAAM,CAAC;IAC7C;;IAEA;IACAA,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACT,UAAU,EAAED,MAAM,CAAC;;IAEpD;IACA,IAAI,IAAI,CAACf,UAAU,EAAE;MACnB,MAAM0B,MAAM,GAAGd,MAAM,CAACe,IAAI,CAAC,IAAI,CAAC3B,UAAU,EAAE,MAAM,CAAC;MAEnDe,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACC,MAAM,CAACT,MAAM,GAAG,CAAC,EAAEF,MAAM,CAAC;MAC3DC,UAAU,IAAIU,MAAM,CAACT,MAAM;MAE3BH,OAAO,CAACc,IAAI,CAACF,MAAM,CAAC;IACtB,CAAC,MAAM;MACLX,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAAC,CAAC,EAAEV,MAAM,CAAC;IAC7C;;IAEA;IACAA,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACT,UAAU,EAAED,MAAM,CAAC;;IAEpD;IACA,MAAMe,UAAU,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACzChB,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAAC,CAAC,EAAEV,MAAM,CAAC;IAC3C,MAAMiB,eAAe,GAAGpB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACvCmB,eAAe,CAACd,aAAa,CAACF,UAAU,IAAI,CAAC,EAAE,CAAC,CAAC;IACjDA,UAAU,IAAIc,UAAU,CAACb,MAAM;IAC/BH,OAAO,CAACc,IAAI,CAACI,eAAe,EAAEF,UAAU,CAAC;;IAEzC;IACAf,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACT,UAAU,EAAED,MAAM,CAAC;;IAEpD;IACA,IAAI,IAAI,CAACZ,WAAW,EAAE;MACpB,MAAMuB,MAAM,GAAGd,MAAM,CAACe,IAAI,CAAC,IAAI,CAACxB,WAAW,EAAE,MAAM,CAAC;MAEpDY,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACC,MAAM,CAACT,MAAM,GAAG,CAAC,EAAEF,MAAM,CAAC;MAC3DC,UAAU,IAAIU,MAAM,CAACT,MAAM;MAE3BH,OAAO,CAACc,IAAI,CAACF,MAAM,CAAC;IACtB,CAAC,MAAM;MACLX,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAAC,CAAC,EAAEV,MAAM,CAAC;IAC7C;;IAEA;IACAA,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACT,UAAU,EAAED,MAAM,CAAC;;IAEpD;IACA,IAAI,IAAI,CAACX,QAAQ,EAAE;MACjB,MAAMsB,MAAM,GAAGd,MAAM,CAACe,IAAI,CAAC,IAAI,CAACvB,QAAQ,EAAE,MAAM,CAAC;MAEjDW,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACC,MAAM,CAACT,MAAM,GAAG,CAAC,EAAEF,MAAM,CAAC;MAC3DC,UAAU,IAAIU,MAAM,CAACT,MAAM;MAE3BH,OAAO,CAACc,IAAI,CAACF,MAAM,CAAC;IACtB,CAAC,MAAM;MACLX,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAAC,CAAC,EAAEV,MAAM,CAAC;IAC7C;;IAEA;IACAA,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACT,UAAU,EAAED,MAAM,CAAC;;IAEpD;IACA,IAAI,IAAI,CAACV,QAAQ,EAAE;MACjB,MAAMqB,MAAM,GAAGd,MAAM,CAACe,IAAI,CAAC,IAAI,CAACtB,QAAQ,EAAE,MAAM,CAAC;MAEjDU,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACC,MAAM,CAACT,MAAM,GAAG,CAAC,EAAEF,MAAM,CAAC;MAC3DC,UAAU,IAAIU,MAAM,CAACT,MAAM;MAE3BH,OAAO,CAACc,IAAI,CAACF,MAAM,CAAC;IACtB,CAAC,MAAM;MACLX,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAAC,CAAC,EAAEV,MAAM,CAAC;IAC7C;;IAEA;IACA,IAAI,IAAI,CAACT,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAAC2B,IAAI,CAACtB,SAAS,EAAEI,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7C;IACAA,MAAM,IAAI,CAAC;;IAEX;IACAA,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACT,UAAU,EAAED,MAAM,CAAC;;IAEpD;IACA,IAAI,IAAI,CAACR,IAAI,EAAE;MACb,IAAI,IAAI,CAACA,IAAI,CAACU,MAAM,GAAG,KAAK,EAAE;QAC5BF,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAAC,KAAK,EAAEV,MAAM,CAAC;MACjD,CAAC,MAAM;QACLA,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAAC,IAAI,CAAClB,IAAI,CAACU,MAAM,EAAEF,MAAM,CAAC;MAC5D;MAEAD,OAAO,CAACc,IAAI,CAAC,IAAI,CAACrB,IAAI,CAAC;IACzB,CAAC,MAAM;MACLQ,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAAC,CAAC,EAAEV,MAAM,CAAC;IAC7C;;IAEA;IACAA,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACT,UAAU,EAAED,MAAM,CAAC;;IAEpD;IACA,IAAI,IAAI,CAACP,YAAY,EAAE;MACrB,MAAMkB,MAAM,GAAGd,MAAM,CAACe,IAAI,CAAC,IAAI,CAACnB,YAAY,EAAE,MAAM,CAAC;MAErDO,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACC,MAAM,CAACT,MAAM,GAAG,CAAC,EAAEF,MAAM,CAAC;MAC3DC,UAAU,IAAIU,MAAM,CAACT,MAAM;MAE3BH,OAAO,CAACc,IAAI,CAACF,MAAM,CAAC;IACtB,CAAC,MAAM;MACLX,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAAC,CAAC,EAAEV,MAAM,CAAC;IAC7C;;IAEA;IACAA,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACT,UAAU,EAAED,MAAM,CAAC;;IAEpD;IACA,IAAI,IAAI,CAACN,cAAc,EAAE;MACvB,MAAMiB,MAAM,GAAGd,MAAM,CAACe,IAAI,CAAC,IAAI,CAAClB,cAAc,EAAE,MAAM,CAAC;MAEvDM,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAACC,MAAM,CAACT,MAAM,GAAG,CAAC,EAAEF,MAAM,CAAC;MAC3DC,UAAU,IAAIU,MAAM,CAACT,MAAM;MAE3BH,OAAO,CAACc,IAAI,CAACF,MAAM,CAAC;IACtB,CAAC,MAAM;MACLX,MAAM,GAAGJ,SAAS,CAACc,aAAa,CAAC,CAAC,EAAEV,MAAM,CAAC;IAC7C;;IAEA;IACA,IAAI,IAAI,CAACR,IAAI,IAAI,IAAI,CAACA,IAAI,CAACU,MAAM,GAAG,KAAK,EAAE;MACzCN,SAAS,CAACO,aAAa,CAAC,IAAI,CAACX,IAAI,CAACU,MAAM,EAAEF,MAAM,CAAC;IACnD,CAAC,MAAM;MACLJ,SAAS,CAACO,aAAa,CAAC,CAAC,EAAEH,MAAM,CAAC;IACpC;IAEA,MAAMmB,IAAI,GAAGtB,MAAM,CAACuB,MAAM,CAACrB,OAAO,CAAC;IACnCoB,IAAI,CAAChB,aAAa,CAACgB,IAAI,CAACjB,MAAM,EAAE,CAAC,CAAC;IAClC,OAAOiB,IAAI;EACb;EAEAd,iBAAiBA,CAAA,EAAG;IAClB,IAAIgB,MAAM,GAAGvG,OAAO,CAACC,aAAa,GAAGD,OAAO,CAACG,aAAa,GAAGH,OAAO,CAACK,cAAc,GAAGL,OAAO,CAACS,gBAAgB,GAAGT,OAAO,CAACW,UAAU,GAAGX,OAAO,CAACe,gBAAgB;IAC9J,IAAI,IAAI,CAAC+C,WAAW,EAAE;MACpByC,MAAM,IAAIvG,OAAO,CAACa,aAAa;IACjC,CAAC,MAAM;MACL0F,MAAM,IAAIvG,OAAO,CAACY,YAAY;IAChC;IACA,OAAO2F,MAAM;EACf;EAEAL,eAAeA,CAAA,EAAG;IAChB,MAAMjB,OAAO,GAAG,EAAE;IAElB,MAAMlB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAIA,OAAO,EAAE;MACX,QAAQA,OAAO,CAACyC,IAAI;QAClB,KAAK,MAAM;UACT,MAAMX,MAAM,GAAGd,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;UAC9Ba,MAAM,CAACP,UAAU,CAAC3C,eAAe,CAACC,UAAU,EAAE,CAAC,CAAC;UAChDiD,MAAM,CAACR,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;UAC1BQ,MAAM,CAACP,UAAU,CAAE3C,eAAe,CAACG,YAAY,IAAI,CAAC,IAAKiB,OAAO,CAAC0C,IAAI,GAAG9D,eAAe,CAACI,gBAAgB,GAAGJ,eAAe,CAACK,eAAe,CAAC,EAAE,CAAC,CAAC;UAC/I6C,MAAM,CAACP,UAAU,CAACvB,OAAO,CAAC2C,QAAQ,KAAK,YAAY,GAAG,IAAI,GAAG/D,eAAe,CAACM,uBAAuB,EAAE,CAAC,CAAC;UACxGgC,OAAO,CAACc,IAAI,CAACF,MAAM,CAAC;UACpB;QAEF,KAAK,eAAe;UAClB,MAAMc,KAAK,GAAG5B,MAAM,CAACe,IAAI,CAAC/B,OAAO,CAAC6C,YAAY,EAAE,MAAM,CAAC;UACvD,MAAMC,GAAG,GAAG9B,MAAM,CAACC,KAAK,CAAC,EAAE,CAAC;UAE5B,IAAIE,MAAM,GAAG,CAAC;UACdA,MAAM,GAAG2B,GAAG,CAACvB,UAAU,CAAC3C,eAAe,CAACC,UAAU,EAAEsC,MAAM,CAAC;UAC3DA,MAAM,GAAG2B,GAAG,CAACxB,aAAa,CAACsB,KAAK,CAACvB,MAAM,GAAG,CAAC,GAAG,CAAC,EAAEF,MAAM,CAAC;UACxDA,MAAM,GAAG2B,GAAG,CAACvB,UAAU,CAAE3C,eAAe,CAACE,qBAAqB,IAAI,CAAC,IAAKkB,OAAO,CAAC0C,IAAI,GAAG9D,eAAe,CAACI,gBAAgB,GAAGJ,eAAe,CAACK,eAAe,CAAC,EAAEkC,MAAM,CAAC;UACnK2B,GAAG,CAAClB,YAAY,CAACgB,KAAK,CAACvB,MAAM,EAAEF,MAAM,CAAC;UAEtCD,OAAO,CAACc,IAAI,CAACc,GAAG,CAAC;UACjB5B,OAAO,CAACc,IAAI,CAACY,KAAK,CAAC;UAEnB;MACJ;IACF;IAEA,IAAI,IAAI,CAACrD,UAAU,IAAIwD,qBAAQ,CAAC,KAAK,CAAC,EAAE;MACtC;MACA,MAAMC,uBAAuB,GAAG,IAAI;MACpC,MAAMC,iCAAiC,GAAG,IAAI;MAC9C,MAAMH,GAAG,GAAG9B,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;MAC3B6B,GAAG,CAACvB,UAAU,CAACyB,uBAAuB,EAAE,CAAC,CAAC;MAC1CF,GAAG,CAACxB,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;MACvBwB,GAAG,CAACvB,UAAU,CAAC0B,iCAAiC,EAAE,CAAC,CAAC;MACpD/B,OAAO,CAACc,IAAI,CAACc,GAAG,CAAC;IACnB;IAEA5B,OAAO,CAACc,IAAI,CAAChB,MAAM,CAACe,IAAI,CAAC,CAAC3C,sBAAsB,CAAC,CAAC,CAAC;IAEnD,OAAO4B,MAAM,CAACuB,MAAM,CAACrB,OAAO,CAAC;EAC/B;EAEAO,iBAAiBA,CAAA,EAAG;IAClB,IAAIyB,MAAM,GAAGjG,OAAO,CAACC,cAAc,GAAGD,OAAO,CAACG,QAAQ,GAAGH,OAAO,CAACO,WAAW;IAC5E,IAAI,IAAI,CAACmD,IAAI,EAAE;MACbuC,MAAM,IAAIjG,OAAO,CAACY,sBAAsB;IAC1C,CAAC,MAAM;MACLqF,MAAM,IAAIjG,OAAO,CAACW,uBAAuB;IAC3C;IACA,OAAOsF,MAAM;EACf;EAEAxB,cAAcA,CAAA,EAAG;IACf,IAAIyB,SAAS,GAAGrF,UAAU,CAACC,QAAQ,GAAGD,UAAU,CAACG,SAAS;IAC1D,IAAI,IAAI,CAAC6B,cAAc,EAAE;MACvBqD,SAAS,IAAIrF,UAAU,CAACM,gBAAgB;IAC1C,CAAC,MAAM;MACL+E,SAAS,IAAIrF,UAAU,CAACK,iBAAiB;IAC3C;IACA,OAAOgF,SAAS;EAClB;EAEAxB,iBAAiBA,CAAA,EAAG;IAClB,OAAOtD,OAAO,CAACC,kBAAkB,GAAGD,OAAO,CAACK,0BAA0B,GAAGL,OAAO,CAACM,cAAc;EACjG;EAEAsD,gBAAgBA,CAAC9B,QAAgB,EAAE;IACjC,KAAK,IAAIiD,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGlD,QAAQ,CAACkB,MAAM,EAAE+B,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACnD,IAAIE,IAAI,GAAGnD,QAAQ,CAACiD,CAAC,CAAC;MACtB,MAAMG,SAAS,GAAGD,IAAI,GAAG,IAAI;MAC7B,MAAME,UAAU,GAAGF,IAAI,IAAI,CAAC;MAC5BA,IAAI,GAAIC,SAAS,IAAI,CAAC,GAAIC,UAAU;MACpCF,IAAI,GAAGA,IAAI,GAAG,IAAI;MAClBnD,QAAQ,CAACiD,CAAC,CAAC,GAAGE,IAAI;IACpB;IACA,OAAOnD,QAAQ;EACjB;EAEAsD,QAAQA,CAACC,MAAM,GAAG,EAAE,EAAE;IACpB,OAAOA,MAAM,GAAG,WAAW,GACzB,IAAAC,kBAAO,EAAC,4FAA4F,EAC5F,IAAI,CAACpE,UAAU,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,aAAa,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAACC,YACnF,CAAC,GAAG,IAAI,GAAG+D,MAAM,GAAG,WAAW,GAC/B,IAAAC,kBAAO,EAAC,qGAAqG,EACrG,IAAI,CAACnC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC/B,cAAc,EAAE,IAAI,CAACC,UACvI,CAAC,GAAG,IAAI,GAAG6D,MAAM,GAAG,WAAW,GAC/B,IAAAC,kBAAO,EAAC,8FAA8F,EAC9F,IAAI,CAACrD,QAAQ,EAAE,IAAI,CAACJ,QAAQ,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACE,OAAO,EAAE,IAAI,CAACD,UAAU,EAAE,IAAI,CAACG,WACzF,CAAC,GAAG,IAAI,GAAGmD,MAAM,GAAG,WAAW,GAC/B,IAAAC,kBAAO,EAAC,iFAAiF,EACjF,IAAI,CAACnD,QAAQ,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACE,IAAI,EAAE,IAAI,CAACC,YAAY,EAAE,IAAI,CAACC,cACzE,CAAC;EACL;AACF;AAAC,IAAA+C,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEczE,aAAa;AAC5B0E,MAAM,CAACF,OAAO,GAAGxE,aAAa"}
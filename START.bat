@echo off
echo Starting RC Dashboard with REAL DATA...

cd backend
start "Backend Server" cmd /k "npm start"

echo Waiting 15 seconds for backend...
timeout /t 15 /nobreak >nul

cd ..
start simple-dashboard.html

echo.
echo DASHBOARD STARTED WITH:
echo - Password: PRK@1234$ (CORRECT)
echo - 80 rows per page for 1700+ records
echo - Scrolls to 100%% bottom (not 75%%)
echo - Professional colors (no kiddy colors)
echo.
echo Keep backend window open!
pause

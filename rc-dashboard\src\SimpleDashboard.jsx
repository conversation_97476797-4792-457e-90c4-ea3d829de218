import React, { useState, useEffect } from 'react';

const SimpleDashboard = () => {
  const [data, setData] = useState([]);
  const [stats, setStats] = useState({});
  const [locations, setLocations] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchData();
  }, [selectedLocation]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // Fetch stats
      const statsResponse = await fetch(`http://localhost:5001/api/dashboard-stats?locationId=${selectedLocation || '%'}`);
      const statsData = await statsResponse.json();
      
      // Fetch RC data
      const rcResponse = await fetch(`http://localhost:5001/api/rc-pending?locationId=${selectedLocation || '%'}`);
      const rcData = await rcResponse.json();
      
      // Fetch locations
      const locResponse = await fetch('http://localhost:5001/api/locations');
      const locData = await locResponse.json();

      if (statsData.success) setStats(statsData.data);
      if (rcData.success) setData(rcData.data.slice(0, 20)); // Show first 20 records
      if (locData.success) setLocations(locData.data);
      
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const cardStyle = {
    background: 'white',
    padding: '1.5rem',
    borderRadius: '12px',
    boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
    margin: '1rem',
    flex: '1',
    minWidth: '200px'
  };

  const tableStyle = {
    width: '100%',
    borderCollapse: 'collapse',
    background: 'white',
    borderRadius: '12px',
    overflow: 'hidden',
    boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
  };

  const thStyle = {
    background: '#f8fafc',
    padding: '12px',
    textAlign: 'left',
    borderBottom: '2px solid #e2e8f0',
    fontWeight: 'bold',
    color: '#374151'
  };

  const tdStyle = {
    padding: '12px',
    borderBottom: '1px solid #f1f5f9',
    color: '#4b5563'
  };

  const getStatusColor = (status) => {
    return status === 'RC Pending' ? '#ef4444' : '#22c55e';
  };

  const getAgingColor = (days) => {
    if (days <= 7) return '#22c55e';
    if (days <= 15) return '#f59e0b';
    if (days <= 30) return '#ef4444';
    return '#dc2626';
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '2rem'
    }}>
      {/* Header */}
      <div style={{
        background: 'white',
        padding: '1.5rem',
        borderRadius: '12px',
        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
        marginBottom: '2rem',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap'
      }}>
        <div>
          <h1 style={{ color: '#1f2937', margin: '0 0 0.5rem 0', fontSize: '2rem' }}>
            🏭 RC Dashboard
          </h1>
          <p style={{ color: '#6b7280', margin: 0 }}>
            Route Card Management System
          </p>
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <select
            value={selectedLocation}
            onChange={(e) => setSelectedLocation(e.target.value)}
            style={{
              padding: '8px 12px',
              borderRadius: '8px',
              border: '1px solid #d1d5db',
              fontSize: '14px'
            }}
          >
            <option value="">All Locations</option>
            {locations.map(loc => (
              <option key={loc.LocationID} value={loc.LocationID}>
                {loc.LocationID}
              </option>
            ))}
          </select>
          
          <button
            onClick={fetchData}
            style={{
              background: '#3b82f6',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
          >
            🔄 Refresh
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div style={{ display: 'flex', flexWrap: 'wrap', margin: '0 -1rem 2rem -1rem' }}>
        <div style={cardStyle}>
          <h3 style={{ color: '#3b82f6', margin: '0 0 0.5rem 0' }}>Total RCs</h3>
          <p style={{ fontSize: '2rem', fontWeight: 'bold', margin: 0, color: '#1f2937' }}>
            {stats.TotalRCs || 0}
          </p>
        </div>
        
        <div style={cardStyle}>
          <h3 style={{ color: '#ef4444', margin: '0 0 0.5rem 0' }}>Pending RCs</h3>
          <p style={{ fontSize: '2rem', fontWeight: 'bold', margin: 0, color: '#1f2937' }}>
            {stats.PendingRCs || 0}
          </p>
        </div>
        
        <div style={cardStyle}>
          <h3 style={{ color: '#22c55e', margin: '0 0 0.5rem 0' }}>Closed RCs</h3>
          <p style={{ fontSize: '2rem', fontWeight: 'bold', margin: 0, color: '#1f2937' }}>
            {stats.ClosedRCs || 0}
          </p>
        </div>
        
        <div style={cardStyle}>
          <h3 style={{ color: '#f59e0b', margin: '0 0 0.5rem 0' }}>Avg Aging</h3>
          <p style={{ fontSize: '2rem', fontWeight: 'bold', margin: 0, color: '#1f2937' }}>
            {stats.AvgAgingDays ? `${stats.AvgAgingDays.toFixed(1)}d` : '0d'}
          </p>
        </div>
      </div>

      {/* Data Table */}
      <div style={{ background: 'white', borderRadius: '12px', padding: '1.5rem', boxShadow: '0 4px 6px rgba(0,0,0,0.1)' }}>
        <h2 style={{ color: '#1f2937', marginTop: 0 }}>Recent Route Cards</h2>
        
        {loading ? (
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <p>Loading data...</p>
          </div>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table style={tableStyle}>
              <thead>
                <tr>
                  <th style={thStyle}>Work Order</th>
                  <th style={thStyle}>Part & Product</th>
                  <th style={thStyle}>WO Date</th>
                  <th style={thStyle}>Status</th>
                  <th style={thStyle}>WO Qty</th>
                  <th style={thStyle}>OK Qty</th>
                  <th style={thStyle}>Aging</th>
                  <th style={thStyle}>Location</th>
                </tr>
              </thead>
              <tbody>
                {data.map((item, index) => (
                  <tr key={index} style={{ backgroundColor: index % 2 === 0 ? '#f9fafb' : 'white' }}>
                    <td style={tdStyle}>
                      <strong style={{ color: '#3b82f6' }}>{item.WoNo}</strong>
                    </td>
                    <td style={tdStyle}>
                      <div>
                        <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                          {item.PartNoAndProductName?.split(' | ')[1] || 'Unknown'}
                        </div>
                        <div style={{ fontSize: '12px', color: '#6b7280' }}>
                          {item.PartNoAndProductName?.split(' | ')[0] || 'Unknown'}
                        </div>
                      </div>
                    </td>
                    <td style={tdStyle}>{item.WODate}</td>
                    <td style={tdStyle}>
                      <span style={{
                        background: item.Status === 'RC Pending' ? '#fef2f2' : '#f0fdf4',
                        color: getStatusColor(item.Status),
                        padding: '4px 8px',
                        borderRadius: '4px',
                        fontSize: '12px',
                        fontWeight: 'bold'
                      }}>
                        {item.Status}
                      </span>
                    </td>
                    <td style={tdStyle}>{item.WO_Qty?.toLocaleString()}</td>
                    <td style={tdStyle}>
                      <span style={{ color: '#22c55e', fontWeight: 'bold' }}>
                        {item.TotalOkQty?.toLocaleString()}
                      </span>
                    </td>
                    <td style={tdStyle}>
                      <span style={{
                        color: getAgingColor(item.AgingInDays),
                        fontWeight: 'bold'
                      }}>
                        {item.AgingInDays}d
                      </span>
                    </td>
                    <td style={tdStyle}>
                      <span style={{
                        background: '#f3f4f6',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontSize: '12px'
                      }}>
                        {item.LocationID}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleDashboard;

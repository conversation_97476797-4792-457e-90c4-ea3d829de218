@echo off
title RC Dashboard - Starting Services
color 0A

echo.
echo ========================================
echo    RC DASHBOARD - STARTING SERVICES
echo ========================================
echo.

echo [1/3] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo ✓ Node.js is installed

echo.
echo [2/3] Starting backend server...
cd /d "%~dp0backend"
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
)

echo Starting server on port 5001...
start "RC Dashboard Backend" cmd /k "echo RC Dashboard Backend Server && echo Port: 5001 && echo. && node server.js"

echo ✓ Backend server started

echo.
echo [3/3] Opening dashboard in browser...
timeout /t 3 /nobreak >nul
start "" "http://localhost:5001/rc-dashboard.html"

echo.
echo ========================================
echo    RC DASHBOARD IS NOW RUNNING
echo ========================================
echo.
echo Backend Server: http://localhost:5001
echo Dashboard URL:  http://localhost:5001/rc-dashboard.html
echo.
echo Services started:
echo - Backend API Server (Port 5001)
echo - Web Dashboard (Auto-opened in browser)
echo.
echo To stop services, run: stop-rc-dashboard.bat
echo.
pause

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
export const proxyPolicyName = "proxyPolicy";
const errorMessage = "proxyPolicy is not supported in browser environment";
export function getDefaultProxySettings() {
    throw new Error(errorMessage);
}
/**
 * proxyPolicy is not supported in the browser and attempting
 * to use it will raise an error.
 */
export function proxyPolicy() {
    throw new Error(errorMessage);
}
/**
 * A function to reset the cached agents.
 * proxyPolicy is not supported in the browser and attempting
 * to use it will raise an error.
 * @internal
 */
export function resetCachedProxyAgents() {
    throw new Error(errorMessage);
}
//# sourceMappingURL=proxyPolicy.common.js.map
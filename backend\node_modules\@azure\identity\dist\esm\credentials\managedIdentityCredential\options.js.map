{"version": 3, "file": "options.js", "sourceRoot": "", "sources": ["../../../../src/credentials/managedIdentityCredential/options.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { TokenCredentialOptions } from \"../../tokenCredentialOptions.js\";\n\n/**\n * Options to send on the {@link ManagedIdentityCredential} constructor.\n * This variation supports `clientId` and not `resourceId`, since only one of both is supported.\n */\nexport interface ManagedIdentityCredentialClientIdOptions extends TokenCredentialOptions {\n  /**\n   * The client ID of the user - assigned identity, or app registration(when working with AKS pod - identity).\n   */\n  clientId?: string;\n}\n\n/**\n * Options to send on the {@link ManagedIdentityCredential} constructor.\n * This variation supports `resourceId` and not `clientId`, since only one of both is supported.\n */\nexport interface ManagedIdentityCredentialResourceIdOptions extends TokenCredentialOptions {\n  /**\n   * Allows specifying a custom resource Id.\n   * In scenarios such as when user assigned identities are created using an ARM template,\n   * where the resource Id of the identity is known but the client Id can't be known ahead of time,\n   * this parameter allows programs to use these user assigned identities\n   * without having to first determine the client Id of the created identity.\n   */\n  resourceId: string;\n}\n\n/**\n * Options to send on the {@link ManagedIdentityCredential} constructor.\n * This variation supports `objectId` as a constructor argument.\n */\nexport interface ManagedIdentityCredentialObjectIdOptions extends TokenCredentialOptions {\n  /**\n   * Allows specifying the object ID of the underlying service principal used to authenticate a user-assigned managed identity.\n   * This is an alternative to providing a client ID or resource ID and is not required for system-assigned managed identities.\n   */\n  objectId: string;\n}\n"]}
import type { AccessToken, TokenCredential } from "@azure/core-auth";
export declare const vsCodeCredentialControl: {
    vsCodeCredentialFinder: never;
};
/**
 * Connects to Azure using the credential provided by the VSCode extension 'Azure Account'.
 *
 * @deprecated This credential is deprecated because the VS Code Azure Account extension on which this credential
 * relies has been deprecated. Users should use other dev-time credentials, such as {@link AzureCliCredential},
 * {@link AzureDeveloperCliCredential}, or {@link AzurePowerShellCredential} for their
 * local development needs. See Azure account extension deprecation notice [here](https://github.com/microsoft/vscode-azure-account/issues/964).
 */
export declare class VisualStudioCodeCredential implements TokenCredential {
    /**
     * Only available in Node.js
     */
    constructor();
    getToken(): Promise<AccessToken | null>;
}
//# sourceMappingURL=visualStudioCodeCredential-browser.d.mts.map
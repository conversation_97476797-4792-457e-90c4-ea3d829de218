@echo off
echo ========================================
echo    RC Dashboard Final Test
echo ========================================
echo.

echo [1/3] Testing Backend API...
curl -s http://localhost:5001/api/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Backend is NOT running
    echo.
    echo Starting backend...
    start "RC Dashboard Backend" cmd /k "cd /d backend && npm start"
    echo.
    echo Waiting for backend to start...
    :wait_backend
    timeout /t 3 /nobreak >nul
    curl -s http://localhost:5001/api/health >nul 2>&1
    if %errorlevel% neq 0 (
        echo Backend starting, please wait...
        goto wait_backend
    )
)

echo ✅ Backend is running!

echo [2/3] Testing RC Data API...
curl -s "http://localhost:5001/api/rc-pending" | findstr "success" >nul
if %errorlevel% neq 0 (
    echo ❌ RC Data API failed
) else (
    echo ✅ RC Data API working!
)

echo [3/3] Opening dashboard with debug tools...
start simple-dashboard.html

echo.
echo ========================================
echo    Dashboard Opened - Check These:
echo ========================================
echo.
echo 1. ✅ Stats cards should show numbers (not "Error")
echo 2. ✅ Location dropdown should show "PRK Sunkudkatte" and "PRK Machohalli"
echo 3. ✅ Table should show work order data (not "Loading data...")
echo.
echo If table still shows "Loading data...":
echo 1. Click "🐛 Debug" button
echo 2. Click "Test All APIs" 
echo 3. Check browser console (F12) for errors
echo 4. Look for CORS or network errors
echo.
echo If you see errors:
echo 1. Open test-table.html to test table loading separately
echo 2. Check console logs for detailed error messages
echo.
echo Expected data:
echo - 1,739 Pending RCs
echo - 314 RCs ≤ 15 days
echo - 312 RCs 16-45 days
echo - 201 RCs 46-90 days
echo - 912 RCs > 90 days
echo.
echo Press any key to close...
pause >nul

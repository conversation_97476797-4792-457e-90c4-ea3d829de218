import type { CredentialLogger } from "./logging.js";
export { processMultiTenantRequest } from "./processMultiTenantRequest.js";
/**
 * @internal
 */
export declare function checkTenantId(logger: CredentialLogger, tenantId: string): void;
/**
 * @internal
 */
export declare function resolveTenantId(logger: CredentialLogger, tenantId?: string, clientId?: string): string;
/**
 * @internal
 */
export declare function resolveAdditionallyAllowedTenantIds(additionallyAllowedTenants?: string[]): string[];
//# sourceMappingURL=tenantIdUtils.d.ts.map
{"version": 3, "file": "operation.js", "sourceRoot": "", "sources": ["../../../../src/lro/recover/operation.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAMlC,iDAAiD;AACjD,iEAA+D;AAE/D,kEAAmE;AAQnE,MAAa,8BAA+B,SAAQ,+CAGnD;IACC,YACS,KAA0C,EACzC,MAAsB,EACtB,mBAAqC,EAAE;QAE/C,KAAK,CAAC,KAAK,EAAE,EAAE,aAAa,EAAE,2DAA2D,EAAE,CAAC,CAAC;QAJtF,UAAK,GAAL,KAAK,CAAqC;QACzC,WAAM,GAAN,MAAM,CAAgB;QACtB,qBAAgB,GAAhB,gBAAgB,CAAuB;IAGjD,CAAC;IAED;;;OAGG;IACK,MAAM,CAAC,IAAY,EAAE,UAAyB,EAAE;QACtD,OAAO,0BAAa,CAAC,QAAQ,CAC3B,+BAA+B,EAC/B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CACvC,IAAI,EACJ,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,OAAO,KAAI,EAAE,EAC7B,cAAc,CACf,CAAC;YACF,OAAO,IAAA,wCAAmB,EAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,iBAAiB,CAC7B,IAAY,EACZ,UAAoC,EAAE;QAEtC,OAAO,0BAAa,CAAC,QAAQ,CAC3B,0CAA0C,EAC1C,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YAC3E,OAAO,IAAA,wCAAmB,EAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,MAAM,CACjB,UAGI,EAAE;QAEN,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;QAEvB,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC/C,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,gBAAgB,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACrB,IAAI,CAAC;gBACH,KAAK,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;gBACzD,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;YAC3B,CAAC;YAAC,WAAM,CAAC;gBACP,sBAAsB;YACxB,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBACvB,KAAK,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;gBACpE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;YACzB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACvB,IAAI,CAAC;gBACH,KAAK,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;gBACzD,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBAC7B,6EAA6E;oBAC7E,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;gBAC3B,CAAC;qBAAM,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBACpC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;oBACpB,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;oBACzB,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAjGD,wEAiGC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { OperationOptions } from \"@azure-rest/core-client\";\nimport type { KeyVaultClient } from \"../../generated/keyVaultClient.js\";\nimport type { GetKeyOptions, KeyVaultKey, RecoverDeletedKeyOptions } from \"../../keysModels.js\";\nimport { tracingClient } from \"../../tracing.js\";\nimport { getKeyFromKeyBundle } from \"../../transformations.js\";\nimport type { KeyVaultKeyPollOperationState } from \"../keyVaultKeyPoller.js\";\nimport { KeyVaultKeyPollOperation } from \"../keyVaultKeyPoller.js\";\n\n/**\n * An interface representing the state of a delete key's poll operation\n */\nexport interface RecoverDeletedKeyPollOperationState\n  extends KeyVaultKeyPollOperationState<KeyVaultKey> {}\n\nexport class RecoverDeletedKeyPollOperation extends KeyVaultKeyPollOperation<\n  RecoverDeletedKeyPollOperationState,\n  KeyVaultKey\n> {\n  constructor(\n    public state: RecoverDeletedKeyPollOperationState,\n    private client: KeyVaultClient,\n    private operationOptions: OperationOptions = {},\n  ) {\n    super(state, { cancelMessage: \"Canceling the recovery of a deleted key is not supported.\" });\n  }\n\n  /**\n   * The getKey method gets a specified key and is applicable to any key stored in Azure Key Vault.\n   * This operation requires the keys/get permission.\n   */\n  private getKey(name: string, options: GetKeyOptions = {}): Promise<KeyVaultKey> {\n    return tracingClient.withSpan(\n      \"RecoverDeleteKeyPoller.getKey\",\n      options,\n      async (updatedOptions) => {\n        const response = await this.client.getKey(\n          name,\n          updatedOptions?.version || \"\",\n          updatedOptions,\n        );\n        return getKeyFromKeyBundle(response);\n      },\n    );\n  }\n\n  /**\n   * Sends a request to recover a deleted Key Vault Key based on the given name.\n   * Since the Key Vault Key won't be immediately recover the deleted key, we have {@link beginRecoverDeletedKey}.\n   */\n  private async recoverDeletedKey(\n    name: string,\n    options: RecoverDeletedKeyOptions = {},\n  ): Promise<KeyVaultKey> {\n    return tracingClient.withSpan(\n      \"RecoverDeletedKeyPoller.recoverDeleteKey\",\n      options,\n      async (updatedOptions) => {\n        const response = await this.client.recoverDeletedKey(name, updatedOptions);\n        return getKeyFromKeyBundle(response);\n      },\n    );\n  }\n\n  /**\n   * Reaches to the service and updates the delete key's poll operation.\n   */\n  public async update(\n    options: {\n      abortSignal?: AbortSignalLike;\n      fireProgress?: (state: RecoverDeletedKeyPollOperationState) => void;\n    } = {},\n  ): Promise<RecoverDeletedKeyPollOperation> {\n    const state = this.state;\n    const { name } = state;\n\n    const operationOptions = this.operationOptions;\n    if (options.abortSignal) {\n      operationOptions.abortSignal = options.abortSignal;\n    }\n\n    if (!state.isStarted) {\n      try {\n        state.result = await this.getKey(name, operationOptions);\n        state.isCompleted = true;\n      } catch {\n        // Nothing to do here.\n      }\n      if (!state.isCompleted) {\n        state.result = await this.recoverDeletedKey(name, operationOptions);\n        state.isStarted = true;\n      }\n    }\n\n    if (!state.isCompleted) {\n      try {\n        state.result = await this.getKey(name, operationOptions);\n        state.isCompleted = true;\n      } catch (error: any) {\n        if (error.statusCode === 403) {\n          // At this point, the resource exists but the user doesn't have access to it.\n          state.isCompleted = true;\n        } else if (error.statusCode !== 404) {\n          state.error = error;\n          state.isCompleted = true;\n          throw error;\n        }\n      }\n    }\n\n    return this;\n  }\n}\n"]}
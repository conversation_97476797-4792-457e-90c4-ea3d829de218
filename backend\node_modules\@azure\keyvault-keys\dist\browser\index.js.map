{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAClC,4CAA4C;;AAG5C,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAGlC,OAAO,EAAyB,0BAA0B,EAAE,MAAM,6BAA6B,CAAC;AAEhG,OAAO,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;AAC/D,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAAE,4BAA4B,EAAE,MAAM,wBAAwB,CAAC;AACtE,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AACzD,OAAO,EAAE,uBAAuB,EAAE,MAAM,yBAAyB,CAAC;AAClE,OAAO,EAgCL,kBAAkB,EAClB,kBAAkB,GAWnB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAC7D,OAAO,EAgBL,iCAAiC,EACjC,yBAAyB,EACzB,aAAa,EACb,kBAAkB,EAClB,wBAAwB,GAczB,MAAM,+BAA+B,CAAC;AACvC,OAAO,EAAyB,0BAA0B,EAAE,MAAM,iBAAiB,CAAC;AACpF,OAAO,EACL,+BAA+B,EAC/B,mBAAmB,EACnB,2BAA2B,EAC3B,0BAA0B,EAC1B,qBAAqB,GACtB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAC7C,OAAO,EAAE,mCAAmC,EAAE,MAAM,2BAA2B,CAAC;AAEhF,OAAO,EAQL,kBAAkB,EAalB,0BAA0B,EAe1B,kBAAkB,EAClB,iCAAiC,EAEjC,yBAAyB,EAEzB,kBAAkB,EAElB,aAAa,EAMb,wBAAwB,EASxB,0BAA0B,EA2B1B,MAAM,GACP,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,OAAO,SAAS;IAiBpB;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,YACE,QAAgB,EAChB,UAA2B,EAC3B,kBAAoC,EAAE;QAEtC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,MAAM,OAAO,GAAG,0BAA0B,WAAW,EAAE,CAAC;QAExD,MAAM,gBAAgB,GAAG,eAAe,CAAC,gBAAgB,CAAC;QAE1D,eAAe,CAAC,gBAAgB,GAAG;YACjC,eAAe,EACb,gBAAgB,IAAI,gBAAgB,CAAC,eAAe;gBAClD,CAAC,CAAC,GAAG,gBAAgB,CAAC,eAAe,IAAI,OAAO,EAAE;gBAClD,CAAC,CAAC,OAAO;SACd,CAAC;QAEF,MAAM,uBAAuB,mCACxB,eAAe,KAClB,UAAU,EAAE,eAAe,CAAC,cAAc,IAAI,kBAAkB,EAChE,cAAc,EAAE;gBACd,MAAM,EAAE,MAAM,CAAC,IAAI;gBACnB,4BAA4B,EAAE;oBAC5B,sBAAsB;oBACtB,4BAA4B;oBAC5B,+BAA+B;iBAChC;aACF,GACF,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,uBAAuB,CAAC,CAAC;QAEhF,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,mCAAmC,EAAE,CAAC,CAAC;QACjF,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,4BAA4B,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC,CAAC;QAC1F,yEAAyE;QACzE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC7B,IAAI,EAAE,mBAAmB;YACzB,WAAW,CAAC,OAAO,EAAE,IAAI;;gBACvB,MAAM,WAAW,GAAG,MAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,mCAAI,EAAE,CAAC;gBAC9D,IAAI,WAAW,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC;oBAC/C,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;gBAC1D,CAAC;gBACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACI,SAAS,CACd,IAAY,EACZ,OAAgB,EAChB,UAA4B,EAAE;QAE9B,OAAO,aAAa,CAAC,QAAQ,CAAC,qBAAqB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACrF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAC1C,IAAI,EACJ;gBACE,GAAG,EAAE,OAAO;gBACZ,KAAK,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK;gBACrB,aAAa,EAAE;oBACb,OAAO,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO;oBACzB,SAAS,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS;oBAC7B,OAAO,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS;oBAC3B,UAAU,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU;iBAChC;gBACD,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;gBACvB,OAAO,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO;gBACzB,aAAa,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa;gBACrC,IAAI,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI;aACpB,EACD,cAAc,CACf,CAAC;YACF,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACI,KAAK,CAAC,WAAW,CAAC,IAAY,EAAE,OAA4B;QACjE,MAAM,OAAO,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,EAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC;QACtE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACI,KAAK,CAAC,YAAY,CAAC,IAAY,EAAE,OAA6B;QACnE,MAAM,OAAO,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,EAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC;QACxE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACI,KAAK,CAAC,YAAY,CAAC,IAAY,EAAE,OAA6B;QACnE,MAAM,OAAO,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,EAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC;QACxE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACI,SAAS,CACd,IAAY,EACZ,GAAe,EACf,UAA4B,EAAE;QAE9B,OAAO,aAAa,CAAC,QAAQ,CAAC,qBAAqB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACrF,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;YAC5F,MAAM,aAAa,GAAG;gBACpB,OAAO;gBACP,SAAS;gBACT,OAAO;gBACP,UAAU;aACX,CAAC;YACF,MAAM,UAAU,GAAG;gBACjB,GAAG;gBACH,GAAG,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB;gBAC/B,aAAa;gBACb,aAAa;gBACb,IAAI;aACL,CAAC;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;YAC/E,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACI,qBAAqB,CAC1B,OAAe,EACf,OAAsC;QAEtC,MAAM,MAAM,GAAG,IAAI,GAAG,CACpB,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAChE,IAAI,CAAC,QAAQ,CACd,CAAC;QAEF,mGAAmG;QACnG,qGAAqG;QACrG,4GAA4G;QAC5G,8EAA8E;QAC9E,MAAM,kBAAkB,GAAoE;YAC1F,eAAe,EAAE,IAAI,CAAC,MAAM;SAC7B,CAAC;QACF,MAAM,YAAY,GAAG,IAAI,kBAAkB,CACzC,MAAM,CAAC,QAAQ,EAAE,EACjB,IAAI,CAAC,UAAU,EACf,kBAAkB,CACnB,CAAC;QACF,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACI,KAAK,CAAC,cAAc,CACzB,IAAY,EACZ,UAAiC,EAAE;QAEnC,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC;YACjC,IAAI;YACJ,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,gBAAgB,EAAE,OAAO;SAC1B,CAAC,CAAC;QAEH,yEAAyE;QACzE,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QAEpB,OAAO,MAAM,CAAC;IAChB,CAAC;IAqEM,mBAAmB,CACxB,GAAG,IAA2F;QAE9F,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,CAAC;QACnF,OAAO,aAAa,CAAC,QAAQ,CAC3B,+BAA+B,EAC/B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAC1C,IAAI,EACJ,UAAU,EACV;gBACE,aAAa,EAAE;oBACb,OAAO,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO;oBACzB,SAAS,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS;oBAC7B,OAAO,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS;iBAC5B;gBACD,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;gBACvB,aAAa,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa;gBACrC,IAAI,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI;aACpB,EACD,cAAc,CACf,CAAC;YACF,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACK,mCAAmC,CACzC,IAA2F;QAE3F,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;YAChC,oEAAoE;YACpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,iDAAiD;YACjD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACI,MAAM,CAAC,IAAY,EAAE,UAAyB,EAAE;QACrD,OAAO,aAAa,CAAC,QAAQ,CAAC,kBAAkB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAClF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE,EAAE,cAAc,CAAC,CAAC;YACvF,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACI,iBAAiB,CACtB,IAAY,EACZ,UAAoC,EAAE;QAEtC,OAAO,aAAa,CAAC,QAAQ,CAC3B,6BAA6B,EAC7B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAClD,IAAI,EACJ,MAAA,cAAc,CAAC,OAAO,mCAAI,EAAE,EAC5B,cAAc,CACf,CAAC;YACF,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACI,aAAa,CAAC,IAAY,EAAE,UAAgC,EAAE;QACnE,OAAO,aAAa,CAAC,QAAQ,CAAC,yBAAyB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACzF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YACvE,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACI,eAAe,CAAC,IAAY,EAAE,UAAkC,EAAE;QACvE,OAAO,aAAa,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC3F,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACI,KAAK,CAAC,sBAAsB,CACjC,IAAY,EACZ,UAAyC,EAAE;QAE3C,MAAM,MAAM,GAAG,IAAI,uBAAuB,CAAC;YACzC,IAAI;YACJ,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,gBAAgB,EAAE,OAAO;SAC1B,CAAC,CAAC;QACH,yEAAyE;QACzE,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QACpB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACI,SAAS,CAAC,IAAY,EAAE,UAA4B,EAAE;QAC3D,OAAO,aAAa,CAAC,QAAQ,CAAC,qBAAqB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACrF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YACnE,OAAO,QAAQ,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACI,KAAK,CAAC,gBAAgB,CAC3B,MAAkB,EAClB,UAAmC,EAAE;QAErC,OAAO,aAAa,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC5F,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,eAAe,EAAE,MAAM,EAAE,EAAE,cAAc,CAAC,CAAC;YAC3F,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACI,cAAc,CAAC,KAAa,EAAE,UAAiC,EAAE;QACtE,OAAO,aAAa,CAAC,QAAQ,CAAC,0BAA0B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC1F,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,EAAE,cAAc,CAAC,CAAC;YAC7E,OAAO,QAAQ,CAAC,KAAM,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACI,SAAS,CAAC,IAAY,EAAE,UAA4B,EAAE;QAC3D,OAAO,aAAa,CAAC,QAAQ,CAAC,qBAAqB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACrF,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YAC9D,OAAO,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACI,UAAU,CACf,IAAY,EACZ,sBAA8B,EAC9B,UAA6B,EAAE;QAE/B,OAAO,aAAa,CAAC,QAAQ,CAAC,sBAAsB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACtF,MAAM,EAAE,KAAK,EAAE,SAAS,KAAc,cAAc,EAAvB,IAAI,UAAK,cAAc,EAA9C,sBAA6B,CAAiB,CAAC;YACrD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CACtC,IAAI,EACJ,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,KAAI,EAAE,EACtB;gBACE,sBAAsB;gBACtB,GAAG,EAAE,SAAS;gBACd,KAAK;aACN,EACD,IAAI,CACL,CAAC;YAEF,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,KAAM,EAAE,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACI,oBAAoB,CACzB,OAAe,EACf,UAAuC,EAAE;QAEzC,OAAO,aAAa,CAAC,QAAQ,CAAC,gCAAgC,EAAE,OAAO,EAAE,KAAK,IAAI,EAAE;YAClF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAC/D,OAAO,0BAA0B,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACI,uBAAuB,CAC5B,OAAe,EACf,MAAmC,EACnC,UAA0C,EAAE;QAE5C,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,uBAAuB,CACtD,OAAO,EACP,0BAA0B,CAAC,qBAAqB,CAAC,MAAM,CAAC,EACxD,cAAc,CACf,CAAC;YACF,OAAO,0BAA0B,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC9D,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACI,2BAA2B,CAChC,IAAY,EACZ,UAA8C,EAAE;QAEhD,OAAO,qBAAqB,CAC1B,OAAO,EACP,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,EAClE,2BAA2B,CAC5B,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACI,oBAAoB,CACzB,UAAuC,EAAE;QAEzC,OAAO,qBAAqB,CAC1B,OAAO,EACP,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EACrC,2BAA2B,CAC5B,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACI,eAAe,CACpB,UAAkC,EAAE;QAEpC,OAAO,qBAAqB,CAC1B,OAAO,EACP,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAC5C,+BAA+B,CAChC,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n/// <reference lib=\"esnext.asynciterable\" />\n\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport { logger } from \"./log.js\";\nimport { PageSettings, PagedAsyncIterableIterator } from \"@azure/core-paging\";\nimport { PollOperationState, PollerLike } from \"@azure/core-lro\";\nimport { DeletionRecoveryLevel, KnownDeletionRecoveryLevel } from \"./generated/models/index.js\";\nimport type { KeyVaultClientOptionalParams } from \"./generated/keyVaultClient.js\";\nimport { KeyVaultClient } from \"./generated/keyVaultClient.js\";\nimport { SDK_VERSION } from \"./constants.js\";\nimport { keyVaultAuthenticationPolicy } from \"@azure/keyvault-common\";\nimport { DeleteKeyPoller } from \"./lro/delete/poller.js\";\nimport { RecoverDeletedKeyPoller } from \"./lro/recover/poller.js\";\nimport {\n  BackupKeyOptions,\n  BeginDeleteKeyOptions,\n  BeginRecoverDeletedKeyOptions,\n  CreateEcKeyOptions,\n  CreateKeyOptions,\n  CreateOctKeyOptions,\n  CreateRsaKeyOptions,\n  CryptographyClientOptions,\n  CryptographyOptions,\n  DeletedKey,\n  GetCryptographyClientOptions,\n  GetDeletedKeyOptions,\n  GetKeyAttestationOptions,\n  GetKeyOptions,\n  GetKeyRotationPolicyOptions,\n  GetRandomBytesOptions,\n  ImportKeyOptions,\n  JsonWebKey,\n  KeyAttestation,\n  KeyClientOptions,\n  KeyExportEncryptionAlgorithm,\n  KeyOperation,\n  KeyPollerOptions,\n  KeyProperties,\n  KeyReleasePolicy,\n  KeyRotationLifetimeAction,\n  KeyRotationPolicy,\n  KeyRotationPolicyAction,\n  KeyRotationPolicyProperties,\n  KeyType,\n  KeyVaultKey,\n  KnownKeyOperations,\n  LATEST_API_VERSION,\n  ListDeletedKeysOptions,\n  ListPropertiesOfKeyVersionsOptions,\n  ListPropertiesOfKeysOptions,\n  PurgeDeletedKeyOptions,\n  ReleaseKeyOptions,\n  ReleaseKeyResult,\n  RestoreKeyBackupOptions,\n  RotateKeyOptions,\n  UpdateKeyPropertiesOptions,\n  UpdateKeyRotationPolicyOptions,\n} from \"./keysModels.js\";\nimport { CryptographyClient } from \"./cryptographyClient.js\";\nimport {\n  AesCbcDecryptParameters,\n  AesCbcEncryptParameters,\n  AesCbcEncryptionAlgorithm,\n  AesGcmDecryptParameters,\n  AesGcmEncryptParameters,\n  AesGcmEncryptionAlgorithm,\n  DecryptOptions,\n  DecryptParameters,\n  DecryptResult,\n  EncryptOptions,\n  EncryptParameters,\n  EncryptResult,\n  EncryptionAlgorithm,\n  KeyCurveName,\n  KeyWrapAlgorithm,\n  KnownKeyExportEncryptionAlgorithm,\n  KnownEncryptionAlgorithms,\n  KnownKeyTypes,\n  KnownKeyCurveNames,\n  KnownSignatureAlgorithms,\n  RsaDecryptParameters,\n  RsaEncryptParameters,\n  RsaEncryptionAlgorithm,\n  SignOptions,\n  SignResult,\n  SignatureAlgorithm,\n  UnwrapKeyOptions,\n  UnwrapResult,\n  VerifyDataOptions,\n  VerifyOptions,\n  VerifyResult,\n  WrapKeyOptions,\n  WrapResult,\n} from \"./cryptographyClientModels.js\";\nimport { KeyVaultKeyIdentifier, parseKeyVaultKeyIdentifier } from \"./identifier.js\";\nimport {\n  getDeletedKeyFromDeletedKeyItem,\n  getKeyFromKeyBundle,\n  getKeyPropertiesFromKeyItem,\n  keyRotationTransformations,\n  mapPagedAsyncIterable,\n} from \"./transformations.js\";\nimport { tracingClient } from \"./tracing.js\";\nimport { bearerTokenAuthenticationPolicyName } from \"@azure/core-rest-pipeline\";\n\nexport {\n  CryptographyClientOptions,\n  KeyClientOptions,\n  BackupKeyOptions,\n  CreateEcKeyOptions,\n  CreateKeyOptions,\n  CreateRsaKeyOptions,\n  CreateOctKeyOptions,\n  CryptographyClient,\n  CryptographyOptions,\n  RsaEncryptionAlgorithm,\n  RsaDecryptParameters,\n  AesGcmEncryptionAlgorithm,\n  AesGcmDecryptParameters,\n  AesCbcEncryptionAlgorithm,\n  AesCbcDecryptParameters,\n  DecryptParameters,\n  DecryptOptions,\n  DecryptResult,\n  DeletedKey,\n  DeletionRecoveryLevel,\n  KnownDeletionRecoveryLevel,\n  RsaEncryptParameters,\n  AesGcmEncryptParameters,\n  AesCbcEncryptParameters,\n  EncryptParameters,\n  EncryptOptions,\n  EncryptResult,\n  GetDeletedKeyOptions,\n  GetKeyAttestationOptions,\n  GetKeyOptions,\n  GetRandomBytesOptions,\n  ImportKeyOptions,\n  JsonWebKey,\n  KeyAttestation,\n  KeyCurveName,\n  KnownKeyCurveNames,\n  KnownKeyExportEncryptionAlgorithm,\n  EncryptionAlgorithm,\n  KnownEncryptionAlgorithms,\n  KeyOperation,\n  KnownKeyOperations,\n  KeyType,\n  KnownKeyTypes,\n  KeyPollerOptions,\n  BeginDeleteKeyOptions,\n  BeginRecoverDeletedKeyOptions,\n  KeyProperties,\n  SignatureAlgorithm,\n  KnownSignatureAlgorithms,\n  KeyVaultKey,\n  KeyWrapAlgorithm,\n  ListPropertiesOfKeysOptions,\n  ListPropertiesOfKeyVersionsOptions,\n  ListDeletedKeysOptions,\n  PageSettings,\n  PagedAsyncIterableIterator,\n  KeyVaultKeyIdentifier,\n  parseKeyVaultKeyIdentifier,\n  PollOperationState,\n  PollerLike,\n  PurgeDeletedKeyOptions,\n  RestoreKeyBackupOptions,\n  RotateKeyOptions,\n  SignOptions,\n  SignResult,\n  UnwrapKeyOptions,\n  UnwrapResult,\n  UpdateKeyPropertiesOptions,\n  VerifyOptions,\n  VerifyDataOptions,\n  VerifyResult,\n  WrapKeyOptions,\n  WrapResult,\n  ReleaseKeyOptions,\n  ReleaseKeyResult,\n  KeyReleasePolicy,\n  KeyExportEncryptionAlgorithm,\n  GetCryptographyClientOptions,\n  KeyRotationPolicyAction,\n  KeyRotationPolicyProperties,\n  KeyRotationPolicy,\n  KeyRotationLifetimeAction,\n  UpdateKeyRotationPolicyOptions,\n  GetKeyRotationPolicyOptions,\n  logger,\n};\n\n/**\n * The KeyClient provides methods to manage {@link KeyVaultKey} in the\n * Azure Key Vault. The client supports creating, retrieving, updating,\n * deleting, purging, backing up, restoring and listing KeyVaultKeys. The\n * client also supports listing {@link DeletedKey} for a soft-delete enabled Azure Key\n * Vault.\n */\nexport class KeyClient {\n  /**\n   * The base URL to the vault\n   */\n  public readonly vaultUrl: string;\n\n  /**\n   * A reference to the auto-generated Key Vault HTTP client.\n   */\n  private readonly client: KeyVaultClient;\n\n  /**\n   * A reference to the credential that was used to construct this client.\n   * Later used to instantiate a {@link CryptographyClient} with the same credential.\n   */\n  private readonly credential: TokenCredential;\n\n  /**\n   * Creates an instance of KeyClient.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleCreateClient\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * // Build the URL to reach your key vault\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`; // or `https://${vaultName}.managedhsm.azure.net` for managed HSM.\n   *\n   * // Lastly, create our keys client and connect to the service\n   * const client = new KeyClient(url, credential);\n   * ```\n   * @param vaultUrl - the URL of the Key Vault. It should have this shape: `https://${your-key-vault-name}.vault.azure.net`. You should validate that this URL references a valid Key Vault or Managed HSM resource. See https://aka.ms/azsdk/blog/vault-uri for details.\n   * @param credential - An object that implements the `TokenCredential` interface used to authenticate requests to the service. Use the \\@azure/identity package to create a credential that suits your needs.\n   * @param pipelineOptions - Pipeline options used to configure Key Vault API requests. Omit this parameter to use the default pipeline configuration.\n   */\n  constructor(\n    vaultUrl: string,\n    credential: TokenCredential,\n    pipelineOptions: KeyClientOptions = {},\n  ) {\n    this.vaultUrl = vaultUrl;\n\n    const libInfo = `azsdk-js-keyvault-keys/${SDK_VERSION}`;\n\n    const userAgentOptions = pipelineOptions.userAgentOptions;\n\n    pipelineOptions.userAgentOptions = {\n      userAgentPrefix:\n        userAgentOptions && userAgentOptions.userAgentPrefix\n          ? `${userAgentOptions.userAgentPrefix} ${libInfo}`\n          : libInfo,\n    };\n\n    const internalPipelineOptions: KeyVaultClientOptionalParams = {\n      ...pipelineOptions,\n      apiVersion: pipelineOptions.serviceVersion || LATEST_API_VERSION,\n      loggingOptions: {\n        logger: logger.info,\n        additionalAllowedHeaderNames: [\n          \"x-ms-keyvault-region\",\n          \"x-ms-keyvault-network-info\",\n          \"x-ms-keyvault-service-version\",\n        ],\n      },\n    };\n\n    this.credential = credential;\n    this.client = new KeyVaultClient(vaultUrl, credential, internalPipelineOptions);\n\n    this.client.pipeline.removePolicy({ name: bearerTokenAuthenticationPolicyName });\n    this.client.pipeline.addPolicy(keyVaultAuthenticationPolicy(credential, pipelineOptions));\n    // Workaround for: https://github.com/Azure/azure-sdk-for-js/issues/31843\n    this.client.pipeline.addPolicy({\n      name: \"ContentTypePolicy\",\n      sendRequest(request, next) {\n        const contentType = request.headers.get(\"Content-Type\") ?? \"\";\n        if (contentType.startsWith(\"application/json\")) {\n          request.headers.set(\"Content-Type\", \"application/json\");\n        }\n        return next(request);\n      },\n    });\n  }\n\n  /**\n   * The create key operation can be used to create any key type in Azure Key Vault. If the named key\n   * already exists, Azure Key Vault creates a new version of the key. It requires the keys/create\n   * permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleCreateKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   * const result = await client.createKey(keyName, \"RSA\");\n   * console.log(\"result: \", result);\n   * ```\n   * Creates a new key, stores it, then returns key parameters and properties to the client.\n   * @param name - The name of the key.\n   * @param keyType - The type of the key. One of the following: 'EC', 'EC-HSM', 'RSA', 'RSA-HSM', 'oct'.\n   * @param options - The optional parameters.\n   */\n  public createKey(\n    name: string,\n    keyType: KeyType,\n    options: CreateKeyOptions = {},\n  ): Promise<KeyVaultKey> {\n    return tracingClient.withSpan(\"KeyClient.createKey\", options, async (updatedOptions) => {\n      const response = await this.client.createKey(\n        name,\n        {\n          kty: keyType,\n          curve: options?.curve,\n          keyAttributes: {\n            enabled: options?.enabled,\n            notBefore: options?.notBefore,\n            expires: options?.expiresOn,\n            exportable: options?.exportable,\n          },\n          keyOps: options?.keyOps,\n          keySize: options?.keySize,\n          releasePolicy: options?.releasePolicy,\n          tags: options?.tags,\n        },\n        updatedOptions,\n      );\n      return getKeyFromKeyBundle(response);\n    });\n  }\n\n  /**\n   * The createEcKey method creates a new elliptic curve key in Azure Key Vault. If the named key\n   * already exists, Azure Key Vault creates a new version of the key. It requires the keys/create\n   * permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleCreateEcKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   * const result = await client.createEcKey(keyName, { curve: \"P-256\" });\n   * console.log(\"result: \", result);\n   * ```\n   * Creates a new key, stores it, then returns key parameters and properties to the client.\n   * @param name - The name of the key.\n   * @param options - The optional parameters.\n   */\n  public async createEcKey(name: string, options?: CreateEcKeyOptions): Promise<KeyVaultKey> {\n    const keyType = options?.hsm ? KnownKeyTypes.ECHSM : KnownKeyTypes.EC;\n    return this.createKey(name, keyType, options);\n  }\n\n  /**\n   * The createRSAKey method creates a new RSA key in Azure Key Vault. If the named key\n   * already exists, Azure Key Vault creates a new version of the key. It requires the keys/create\n   * permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleCreateRsaKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   * const result = await client.createRsaKey(\"MyKey\", { keySize: 2048 });\n   * console.log(\"result: \", result);\n   * ```\n   * Creates a new key, stores it, then returns key parameters and properties to the client.\n   * @param name - The name of the key.\n   * @param options - The optional parameters.\n   */\n  public async createRsaKey(name: string, options?: CreateRsaKeyOptions): Promise<KeyVaultKey> {\n    const keyType = options?.hsm ? KnownKeyTypes.RSAHSM : KnownKeyTypes.RSA;\n    return this.createKey(name, keyType, options);\n  }\n\n  /**\n   * The createOctKey method creates a new OCT key in Azure Key Vault. If the named key\n   * already exists, Azure Key Vault creates a new version of the key. It requires the keys/create\n   * permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleCreateOctKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   * const result = await client.createOctKey(\"MyKey\", { hsm: true });\n   * console.log(\"result: \", result);\n   * ```\n   * Creates a new key, stores it, then returns key parameters and properties to the client.\n   * @param name - The name of the key.\n   * @param options - The optional parameters.\n   */\n  public async createOctKey(name: string, options?: CreateOctKeyOptions): Promise<KeyVaultKey> {\n    const keyType = options?.hsm ? KnownKeyTypes.OctHSM : KnownKeyTypes.Oct;\n    return this.createKey(name, keyType, options);\n  }\n\n  /**\n   * The import key operation may be used to import any key type into an Azure Key Vault. If the\n   * named key already exists, Azure Key Vault creates a new version of the key. This operation\n   * requires the keys/import permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleImportKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const jsonWebKey = {\n   *   kty: \"RSA\",\n   *   kid: \"test-key-123\",\n   *   use: \"sig\",\n   *   alg: \"RS256\",\n   *   n: new Uint8Array([112, 34, 56, 98, 123, 244, 200, 99]),\n   *   e: new Uint8Array([1, 0, 1]),\n   *   d: new Uint8Array([45, 67, 89, 23, 144, 200, 76, 233]),\n   *   p: new Uint8Array([34, 89, 100, 77, 204, 56, 29, 77]),\n   *   q: new Uint8Array([78, 99, 201, 45, 188, 34, 67, 90]),\n   *   dp: new Uint8Array([23, 45, 78, 56, 200, 144, 32, 67]),\n   *   dq: new Uint8Array([12, 67, 89, 144, 99, 56, 23, 45]),\n   *   qi: new Uint8Array([78, 90, 45, 201, 34, 67, 120, 55]),\n   * };\n   *\n   * const result = await client.importKey(\"MyKey\", jsonWebKey);\n   * ```\n   * Imports an externally created key, stores it, and returns key parameters and properties\n   * to the client.\n   * @param name - Name for the imported key.\n   * @param key - The JSON web key.\n   * @param options - The optional parameters.\n   */\n  public importKey(\n    name: string,\n    key: JsonWebKey,\n    options: ImportKeyOptions = {},\n  ): Promise<KeyVaultKey> {\n    return tracingClient.withSpan(`KeyClient.importKey`, options, async (updatedOptions) => {\n      const { enabled, notBefore, expiresOn: expires, exportable, releasePolicy, tags } = options;\n      const keyAttributes = {\n        enabled,\n        notBefore,\n        expires,\n        exportable,\n      };\n      const parameters = {\n        key,\n        hsm: options?.hardwareProtected,\n        keyAttributes,\n        releasePolicy,\n        tags,\n      };\n      const response = await this.client.importKey(name, parameters, updatedOptions);\n      return getKeyFromKeyBundle(response);\n    });\n  }\n\n  /**\n   * Gets a {@link CryptographyClient} for the given key.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleGetCryptographyClient\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * // Get a cryptography client for a given key\n   * const cryptographyClient = client.getCryptographyClient(\"MyKey\");\n   * ```\n   * @param name - The name of the key used to perform cryptographic operations.\n   * @param version - Optional version of the key used to perform cryptographic operations.\n   * @returns - A {@link CryptographyClient} using the same options, credentials, and http client as this {@link KeyClient}\n   */\n  public getCryptographyClient(\n    keyName: string,\n    options?: GetCryptographyClientOptions,\n  ): CryptographyClient {\n    const keyUrl = new URL(\n      [\"keys\", keyName, options?.keyVersion].filter(Boolean).join(\"/\"),\n      this.vaultUrl,\n    );\n\n    // The goals of this method are discoverability and performance (by sharing a client and pipeline).\n    // The existing cryptography client does not accept a pipeline as an argument, nor does it expose it.\n    // In order to avoid publicly exposing the pipeline we will pass in the underlying client as an undocumented\n    // property to the constructor so that crypto providers downstream can use it.\n    const constructorOptions: CryptographyClientOptions & { generatedClient: KeyVaultClient } = {\n      generatedClient: this.client,\n    };\n    const cryptoClient = new CryptographyClient(\n      keyUrl.toString(),\n      this.credential,\n      constructorOptions,\n    );\n    return cryptoClient;\n  }\n\n  /**\n   * The delete operation applies to any key stored in Azure Key Vault. Individual versions\n   * of a key can not be deleted, only all versions of a given key at once.\n   *\n   * This function returns a Long Running Operation poller that allows you to wait indefinitely until the key is deleted.\n   *\n   * This operation requires the keys/delete permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleDeleteKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * const poller = await client.beginDeleteKey(keyName);\n   * await poller.pollUntilDone();\n   * ```\n   * Deletes a key from a specified key vault.\n   * @param name - The name of the key.\n   * @param options - The optional parameters.\n   */\n  public async beginDeleteKey(\n    name: string,\n    options: BeginDeleteKeyOptions = {},\n  ): Promise<PollerLike<PollOperationState<DeletedKey>, DeletedKey>> {\n    const poller = new DeleteKeyPoller({\n      name,\n      client: this.client,\n      intervalInMs: options.intervalInMs,\n      resumeFrom: options.resumeFrom,\n      operationOptions: options,\n    });\n\n    // This will initialize the poller's operation (the deletion of the key).\n    await poller.poll();\n\n    return poller;\n  }\n\n  /**\n   * The updateKeyProperties method changes specified properties of an existing stored key. Properties that\n   * are not specified in the request are left unchanged. The value of a key itself cannot be\n   * changed. This operation requires the keys/set permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleUpdateKeyProperties\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * const result = await client.createKey(keyName, \"RSA\");\n   * await client.updateKeyProperties(keyName, result.properties.version, {\n   *   enabled: false,\n   * });\n   * ```\n   * Updates the properties associated with a specified key in a given key vault.\n   * @param name - The name of the key.\n   * @param keyVersion - The version of the key.\n   * @param options - The optional parameters.\n   */\n  public updateKeyProperties(\n    name: string,\n    keyVersion: string,\n    options?: UpdateKeyPropertiesOptions,\n  ): Promise<KeyVaultKey>;\n  /**\n   * The updateKeyProperties method changes specified properties of the latest version of an existing stored key. Properties that\n   * are not specified in the request are left unchanged. The value of a key itself cannot be\n   * changed. This operation requires the keys/set permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleUpdateKeyProperties\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * const result = await client.createKey(keyName, \"RSA\");\n   * await client.updateKeyProperties(keyName, result.properties.version, {\n   *   enabled: false,\n   * });\n   * ```\n   * Updates the properties associated with a specified key in a given key vault.\n   * @param name - The name of the key.\n   * @param keyVersion - The version of the key.\n   * @param options - The optional parameters.\n   */\n  public updateKeyProperties(\n    name: string,\n    options?: UpdateKeyPropertiesOptions,\n  ): Promise<KeyVaultKey>;\n  public updateKeyProperties(\n    ...args: [string, string, UpdateKeyPropertiesOptions?] | [string, UpdateKeyPropertiesOptions?]\n  ): Promise<KeyVaultKey> {\n    const [name, keyVersion, options] = this.disambiguateUpdateKeyPropertiesArgs(args);\n    return tracingClient.withSpan(\n      `KeyClient.updateKeyProperties`,\n      options,\n      async (updatedOptions) => {\n        const response = await this.client.updateKey(\n          name,\n          keyVersion,\n          {\n            keyAttributes: {\n              enabled: options?.enabled,\n              notBefore: options?.notBefore,\n              expires: options?.expiresOn,\n            },\n            keyOps: options?.keyOps,\n            releasePolicy: options?.releasePolicy,\n            tags: options?.tags,\n          },\n          updatedOptions,\n        );\n        return getKeyFromKeyBundle(response);\n      },\n    );\n  }\n\n  /**\n   * Standardizes an overloaded arguments collection for the updateKeyProperties method.\n   *\n   * @param args - The arguments collection.\n   * @returns - The standardized arguments collection.\n   */\n  private disambiguateUpdateKeyPropertiesArgs(\n    args: [string, string, UpdateKeyPropertiesOptions?] | [string, UpdateKeyPropertiesOptions?],\n  ): [string, string, UpdateKeyPropertiesOptions] {\n    if (typeof args[1] === \"string\") {\n      // [name, keyVersion, options?] => [name, keyVersion, options || {}]\n      return [args[0], args[1], args[2] || {}];\n    } else {\n      // [name, options?] => [name , \"\", options || {}]\n      return [args[0], \"\", args[1] || {}];\n    }\n  }\n\n  /**\n   * The getKey method gets a specified key and is applicable to any key stored in Azure Key Vault.\n   * This operation requires the keys/get permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleGetKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * const latestKey = await client.getKey(keyName);\n   * console.log(`Latest version of the key ${keyName}: `, latestKey);\n   *\n   * const specificKey = await client.getKey(keyName, { version: latestKey.properties.version! });\n   * console.log(`The key ${keyName} at the version ${latestKey.properties.version!}: `, specificKey);\n   * ```\n   * Get a specified key from a given key vault.\n   * @param name - The name of the key.\n   * @param options - The optional parameters.\n   */\n  public getKey(name: string, options: GetKeyOptions = {}): Promise<KeyVaultKey> {\n    return tracingClient.withSpan(`KeyClient.getKey`, options, async (updatedOptions) => {\n      const response = await this.client.getKey(name, options.version || \"\", updatedOptions);\n      return getKeyFromKeyBundle(response);\n    });\n  }\n\n  /**\n   * The getKeyAttestation method gets a specified key and its attestation blob and is applicable to any key stored in Azure Key Vault Managed HSM.\n   * This operation requires the keys/get permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleGetKeyAttestation\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT MANAGED HSM NAME>\";\n   * const url = `https://${vaultName}.managedhsm.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * const latestKey = await client.getKeyAttestation(keyName);\n   * console.log(`Latest version of the key ${keyName}: `, latestKey);\n   *\n   * const specificKey = await client.getKeyAttestation(keyName, {\n   *   version: latestKey.properties.version!,\n   * });\n   * console.log(`The key ${keyName} at the version ${latestKey.properties.version!}: `, specificKey);\n   * ```\n   * Get a specified key from a given key vault.\n   * @param name - The name of the key.\n   * @param options - The optional parameters.\n   */\n  public getKeyAttestation(\n    name: string,\n    options: GetKeyAttestationOptions = {},\n  ): Promise<KeyVaultKey> {\n    return tracingClient.withSpan(\n      `KeyClient.getKeyAttestation`,\n      options,\n      async (updatedOptions) => {\n        const response = await this.client.getKeyAttestation(\n          name,\n          updatedOptions.version ?? \"\",\n          updatedOptions,\n        );\n        return getKeyFromKeyBundle(response);\n      },\n    );\n  }\n\n  /**\n   * The getDeletedKey method returns the specified deleted key along with its properties.\n   * This operation requires the keys/get permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleGetDeletedKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * await client.getDeletedKey(keyName);\n   * ```\n   * Gets the specified deleted key.\n   * @param name - The name of the key.\n   * @param options - The optional parameters.\n   */\n  public getDeletedKey(name: string, options: GetDeletedKeyOptions = {}): Promise<DeletedKey> {\n    return tracingClient.withSpan(`KeyClient.getDeletedKey`, options, async (updatedOptions) => {\n      const response = await this.client.getDeletedKey(name, updatedOptions);\n      return getKeyFromKeyBundle(response);\n    });\n  }\n\n  /**\n   * The purge deleted key operation removes the key permanently, without the possibility of\n   * recovery. This operation can only be enabled on a soft-delete enabled vault. This operation\n   * requires the keys/purge permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSamplePurgeDeletedKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * const deletePoller = await client.beginDeleteKey(keyName);\n   * await deletePoller.pollUntilDone();\n   *\n   * await client.purgeDeletedKey(keyName);\n   * ```\n   * Permanently deletes the specified key.\n   * @param name - The name of the key.\n   * @param options - The optional parameters.\n   */\n  public purgeDeletedKey(name: string, options: PurgeDeletedKeyOptions = {}): Promise<void> {\n    return tracingClient.withSpan(`KeyClient.purgeDeletedKey`, options, async (updatedOptions) => {\n      await this.client.purgeDeletedKey(name, updatedOptions);\n    });\n  }\n\n  /**\n   * Recovers the deleted key in the specified vault. This operation can only be performed on a\n   * soft-delete enabled vault.\n   *\n   * This function returns a Long Running Operation poller that allows you to wait indefinitely until the deleted key is recovered.\n   *\n   * This operation requires the keys/recover permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleRecoverDeletedKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * const deletePoller = await client.beginDeleteKey(keyName);\n   * await deletePoller.pollUntilDone();\n   *\n   * const recoverPoller = await client.beginRecoverDeletedKey(keyName);\n   * const recoveredKey = await recoverPoller.pollUntilDone();\n   * ```\n   * Recovers the deleted key to the latest version.\n   * @param name - The name of the deleted key.\n   * @param options - The optional parameters.\n   */\n  public async beginRecoverDeletedKey(\n    name: string,\n    options: BeginRecoverDeletedKeyOptions = {},\n  ): Promise<PollerLike<PollOperationState<DeletedKey>, DeletedKey>> {\n    const poller = new RecoverDeletedKeyPoller({\n      name,\n      client: this.client,\n      intervalInMs: options.intervalInMs,\n      resumeFrom: options.resumeFrom,\n      operationOptions: options,\n    });\n    // This will initialize the poller's operation (the deletion of the key).\n    await poller.poll();\n    return poller;\n  }\n\n  /**\n   * Requests that a backup of the specified key be downloaded to the client. All versions of the\n   * key will be downloaded. This operation requires the keys/backup permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleBackupKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * const backupContents = await client.backupKey(keyName);\n   * ```\n   * Backs up the specified key.\n   * @param name - The name of the key.\n   * @param options - The optional parameters.\n   */\n  public backupKey(name: string, options: BackupKeyOptions = {}): Promise<Uint8Array | undefined> {\n    return tracingClient.withSpan(`KeyClient.backupKey`, options, async (updatedOptions) => {\n      const response = await this.client.backupKey(name, updatedOptions);\n      return response.value;\n    });\n  }\n\n  /**\n   * Restores a backed up key, and all its versions, to a vault. This operation requires the\n   * keys/restore permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleRestoreKeyBackup\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * const backupContents = await client.backupKey(keyName);\n   *\n   * const key = await client.restoreKeyBackup(backupContents);\n   * ```\n   * Restores a backed up key to a vault.\n   * @param backup - The backup blob associated with a key bundle.\n   * @param options - The optional parameters.\n   */\n  public async restoreKeyBackup(\n    backup: Uint8Array,\n    options: RestoreKeyBackupOptions = {},\n  ): Promise<KeyVaultKey> {\n    return tracingClient.withSpan(`KeyClient.restoreKeyBackup`, options, async (updatedOptions) => {\n      const response = await this.client.restoreKey({ keyBundleBackup: backup }, updatedOptions);\n      return getKeyFromKeyBundle(response);\n    });\n  }\n\n  /**\n   * Gets the requested number of bytes containing random values from a managed HSM.\n   * This operation requires the managedHsm/rng permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleGetRandomBytes\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const bytes = await client.getRandomBytes(10);\n   * ```\n   * @param count - The number of bytes to generate between 1 and 128 inclusive.\n   * @param options - The optional parameters.\n   */\n  public getRandomBytes(count: number, options: GetRandomBytesOptions = {}): Promise<Uint8Array> {\n    return tracingClient.withSpan(\"KeyClient.getRandomBytes\", options, async (updatedOptions) => {\n      const response = await this.client.getRandomBytes({ count }, updatedOptions);\n      return response.value!;\n    });\n  }\n\n  /**\n   * Rotates the key based on the key policy by generating a new version of the key. This operation requires the keys/rotate permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleKeyRotation\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * // Set the key's automated rotation policy to rotate the key 30 days before expiry.\n   * const policy = await client.updateKeyRotationPolicy(keyName, {\n   *   lifetimeActions: [\n   *     {\n   *       action: \"Rotate\",\n   *       timeBeforeExpiry: \"P30D\",\n   *     },\n   *   ],\n   *   // You may also specify the duration after which any newly rotated key will expire.\n   *   // In this case, any new key versions will expire after 90 days.\n   *   expiresIn: \"P90D\",\n   * });\n   *\n   * // You can get the current key rotation policy of a given key by calling the getKeyRotationPolicy method.\n   * const currentPolicy = await client.getKeyRotationPolicy(keyName);\n   *\n   * // Finally, you can rotate a key on-demand by creating a new version of the given key.\n   * const rotatedKey = await client.rotateKey(keyName);\n   * ```\n   *\n   * @param name - The name of the key to rotate.\n   * @param options - The optional parameters.\n   */\n  public rotateKey(name: string, options: RotateKeyOptions = {}): Promise<KeyVaultKey> {\n    return tracingClient.withSpan(\"KeyClient.rotateKey\", options, async (updatedOptions) => {\n      const key = await this.client.rotateKey(name, updatedOptions);\n      return getKeyFromKeyBundle(key);\n    });\n  }\n\n  /**\n   * Releases a key from a managed HSM.\n   *\n   * The release key operation is applicable to all key types. The operation requires the key to be marked exportable and the keys/release permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleReleaseKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * const result = await client.releaseKey(\"myKey\", \"<attestation-target>\");\n   * ```\n   *\n   * @param name - The name of the key.\n   * @param targetAttestationToken - The attestation assertion for the target of the key release.\n   * @param options - The optional parameters.\n   */\n  public releaseKey(\n    name: string,\n    targetAttestationToken: string,\n    options: ReleaseKeyOptions = {},\n  ): Promise<ReleaseKeyResult> {\n    return tracingClient.withSpan(\"KeyClient.releaseKey\", options, async (updatedOptions) => {\n      const { nonce, algorithm, ...rest } = updatedOptions;\n      const result = await this.client.release(\n        name,\n        options?.version || \"\",\n        {\n          targetAttestationToken,\n          enc: algorithm,\n          nonce,\n        },\n        rest,\n      );\n\n      return { value: result.value! };\n    });\n  }\n\n  /**\n   * Gets the rotation policy of a Key Vault Key.\n   * By default, all keys have a policy that will notify 30 days before expiry.\n   *\n   * This operation requires the keys/get permission.\n   * Example usage:\n   * ```ts snippet:ReadmeSampleGetKeyRotationPolicy\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * const result = await client.getKeyRotationPolicy(keyName);\n   * ```\n   *\n   * @param keyName - The name of the key.\n   * @param options - The optional parameters.\n   */\n  public getKeyRotationPolicy(\n    keyName: string,\n    options: GetKeyRotationPolicyOptions = {},\n  ): Promise<KeyRotationPolicy> {\n    return tracingClient.withSpan(\"KeyClient.getKeyRotationPolicy\", options, async () => {\n      const policy = await this.client.getKeyRotationPolicy(keyName);\n      return keyRotationTransformations.generatedToPublic(policy);\n    });\n  }\n\n  /**\n   * Updates the rotation policy of a Key Vault Key.\n   * This operation requires the keys/update permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleUpdateKeyRotationPolicy\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * const myPolicy = await client.getKeyRotationPolicy(keyName);\n   *\n   * const setPolicy = await client.updateKeyRotationPolicy(keyName, myPolicy);\n   * ```\n   *\n   * @param keyName - The name of the key.\n   * @param policyProperties - The {@link KeyRotationPolicyProperties} for the policy.\n   * @param options - The optional parameters.\n   */\n  public updateKeyRotationPolicy(\n    keyName: string,\n    policy: KeyRotationPolicyProperties,\n    options: UpdateKeyRotationPolicyOptions = {},\n  ): Promise<KeyRotationPolicy> {\n    return tracingClient.withSpan(\n      \"KeyClient.updateKeyRotationPolicy\",\n      options,\n      async (updatedOptions) => {\n        const result = await this.client.updateKeyRotationPolicy(\n          keyName,\n          keyRotationTransformations.propertiesToGenerated(policy),\n          updatedOptions,\n        );\n        return keyRotationTransformations.generatedToPublic(result);\n      },\n    );\n  }\n\n  /**\n   * Iterates all versions of the given key in the vault. The full key identifier, properties, and tags are provided\n   * in the response. This operation requires the keys/list permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleListKeys\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * for await (const keyProperties of client.listPropertiesOfKeys()) {\n   *   console.log(\"Key properties: \", keyProperties);\n   * }\n   *\n   * for await (const deletedKey of client.listDeletedKeys()) {\n   *   console.log(\"Deleted: \", deletedKey);\n   * }\n   *\n   * for await (const versionProperties of client.listPropertiesOfKeyVersions(keyName)) {\n   *   console.log(\"Version properties: \", versionProperties);\n   * }\n   * ```\n   * @param name - Name of the key to fetch versions for\n   * @param options - The optional parameters.\n   */\n  public listPropertiesOfKeyVersions(\n    name: string,\n    options: ListPropertiesOfKeyVersionsOptions = {},\n  ): PagedAsyncIterableIterator<KeyProperties> {\n    return mapPagedAsyncIterable(\n      options,\n      (mappedOptions) => this.client.getKeyVersions(name, mappedOptions),\n      getKeyPropertiesFromKeyItem,\n    );\n  }\n\n  /**\n   * Iterates the latest version of all keys in the vault.  The full key identifier and properties are provided\n   * in the response. No values are returned for the keys. This operations requires the keys/list permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleListKeys\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * for await (const keyProperties of client.listPropertiesOfKeys()) {\n   *   console.log(\"Key properties: \", keyProperties);\n   * }\n   *\n   * for await (const deletedKey of client.listDeletedKeys()) {\n   *   console.log(\"Deleted: \", deletedKey);\n   * }\n   *\n   * for await (const versionProperties of client.listPropertiesOfKeyVersions(keyName)) {\n   *   console.log(\"Version properties: \", versionProperties);\n   * }\n   * ```\n   * List all keys in the vault\n   * @param options - The optional parameters.\n   */\n  public listPropertiesOfKeys(\n    options: ListPropertiesOfKeysOptions = {},\n  ): PagedAsyncIterableIterator<KeyProperties> {\n    return mapPagedAsyncIterable(\n      options,\n      this.client.getKeys.bind(this.client),\n      getKeyPropertiesFromKeyItem,\n    );\n  }\n\n  /**\n   * Iterates the deleted keys in the vault.  The full key identifier and properties are provided\n   * in the response. No values are returned for the keys. This operations requires the keys/list permission.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleListKeys\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const keyName = \"MyKeyName\";\n   *\n   * for await (const keyProperties of client.listPropertiesOfKeys()) {\n   *   console.log(\"Key properties: \", keyProperties);\n   * }\n   *\n   * for await (const deletedKey of client.listDeletedKeys()) {\n   *   console.log(\"Deleted: \", deletedKey);\n   * }\n   *\n   * for await (const versionProperties of client.listPropertiesOfKeyVersions(keyName)) {\n   *   console.log(\"Version properties: \", versionProperties);\n   * }\n   * ```\n   * List all keys in the vault\n   * @param options - The optional parameters.\n   */\n  public listDeletedKeys(\n    options: ListDeletedKeysOptions = {},\n  ): PagedAsyncIterableIterator<DeletedKey> {\n    return mapPagedAsyncIterable(\n      options,\n      this.client.getDeletedKeys.bind(this.client),\n      getDeletedKeyFromDeletedKeyItem,\n    );\n  }\n}\n"]}
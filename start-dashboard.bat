@echo off
echo ========================================
echo    RC Dashboard Startup Script
echo ========================================
echo.

REM Kill any existing processes on ports 5001 and 5174
echo [1/7] Killing existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":5001"') do (
    echo Killing process on port 5001: %%a
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":5174"') do (
    echo Killing process on port 5174: %%a
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":5173"') do (
    echo Killing process on port 5173: %%a
    taskkill /f /pid %%a >nul 2>&1
)

echo [2/7] Waiting for ports to be released...
timeout /t 3 /nobreak >nul

echo [3/7] Starting Backend Server...
start "RC Dashboard Backend" cmd /k "cd /d backend && echo Starting Backend Server on Port 5001... && npm start"

echo [4/7] Waiting for backend to start...
:wait_backend
timeout /t 2 /nobreak >nul
curl -s http://localhost:5001/api/health >nul 2>&1
if %errorlevel% neq 0 (
    echo Backend not ready yet, waiting...
    goto wait_backend
)
echo Backend is ready!

echo [5/7] Starting Frontend Server...
start "RC Dashboard Frontend" cmd /k "cd /d rc-dashboard && echo Starting Frontend Server on Port 5174... && npm run dev"

echo [6/7] Waiting for frontend to start...
:wait_frontend
timeout /t 2 /nobreak >nul
curl -s -I http://localhost:5174 >nul 2>&1
if %errorlevel% neq 0 (
    echo Frontend not ready yet, waiting...
    goto wait_frontend
)
echo Frontend is ready!

echo [7/7] Opening HTML Dashboard (more reliable)...
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo    Dashboard Started Successfully!
echo ========================================
echo.
echo Backend API:  http://localhost:5001
echo Frontend:     http://localhost:5174
echo HTML Dashboard: simple-dashboard.html
echo.
echo Opening HTML dashboard (recommended)...
start simple-dashboard.html

echo.
echo If HTML dashboard doesn't work, try:
echo 1. Open simple-dashboard.html manually
echo 2. Or visit http://localhost:5174
echo.
echo Press any key to close this window...
pause >nul

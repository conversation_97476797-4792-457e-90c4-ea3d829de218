@echo off
echo ========================================
echo    RC Dashboard Startup Script
echo ========================================
echo.

REM Kill any existing processes on ports 5001 and 5174
echo [1/6] Killing existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":5001"') do (
    echo Killing process on port 5001: %%a
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":5174"') do (
    echo Killing process on port 5174: %%a
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":5173"') do (
    echo Killing process on port 5173: %%a
    taskkill /f /pid %%a >nul 2>&1
)

echo [2/6] Waiting for ports to be released...
timeout /t 3 /nobreak >nul

echo [3/6] Starting Backend Server...
start "RC Dashboard Backend" cmd /k "cd /d backend && echo Starting Backend Server on Port 5001... && npm start"

echo [4/6] Waiting for backend to initialize...
timeout /t 5 /nobreak >nul

echo [5/6] Starting Frontend Server...
start "RC Dashboard Frontend" cmd /k "cd /d rc-dashboard && echo Starting Frontend Server on Port 5174... && npm run dev"

echo [6/6] Waiting for frontend to initialize...
timeout /t 8 /nobreak >nul

echo.
echo ========================================
echo    Dashboard Started Successfully!
echo ========================================
echo.
echo Backend API:  http://localhost:5001
echo Frontend:     http://localhost:5174
echo.
echo Opening dashboard in browser...
start http://localhost:5174

echo.
echo Press any key to close this window...
pause >nul

{"version": 3, "file": "keyVaultKeyPoller.d.ts", "sourceRoot": "", "sources": ["../../../src/lro/keyVaultKeyPoller.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAEhE,OAAO,KAAK,EAAE,aAAa,EAAE,kBAAkB,EAAE,MAAM,iBAAiB,CAAC;AACzE,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAErE;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC,MAAM,EAAE,cAAc,CAAC;IACvB,IAAI,EAAE,MAAM,CAAC;IACb,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IACpC,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,6BAA6B,CAAC,OAAO,CAAE,SAAQ,kBAAkB,CAAC,OAAO,CAAC;IACzF;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;CACd;AAED;;GAEG;AACH,8BAAsB,iBAAiB,CACrC,MAAM,SAAS,6BAA6B,CAAC,OAAO,CAAC,EACrD,OAAO,CACP,SAAQ,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;IAC/B;;OAEG;IACI,YAAY,EAAE,MAAM,CAAQ;IAEnC;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;CAG7B;AAED;;GAEG;AACH,MAAM,WAAW,+BAA+B;IAC9C,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED;;GAEG;AACH,qBAAa,wBAAwB,CAAC,MAAM,EAAE,OAAO,CAAE,YAAW,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC;IAIrF,KAAK,EAAE,MAAM;IAHtB,OAAO,CAAC,aAAa,CAAc;gBAG1B,KAAK,EAAE,MAAM,EACpB,OAAO,GAAE,+BAAoC;IAO/C;;OAEG;IACU,MAAM,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAI9D;;OAEG;IACU,MAAM,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAI9D;;OAEG;IACI,QAAQ,IAAI,MAAM;CAK1B"}
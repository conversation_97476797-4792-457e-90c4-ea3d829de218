import React from 'react';
import { motion } from 'framer-motion';
import { 
  LayoutDashboard, 
  Table, 
  BarChart3, 
  Settings, 
  ChevronLeft,
  Factory,
  TrendingUp,
  Clock
} from 'lucide-react';

const Sidebar = ({ open, onToggle, currentView, onViewChange }) => {
  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: LayoutDashboard,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      id: 'data-table',
      label: 'RC Data Table',
      icon: Table,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: BarChart3,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  const sidebarVariants = {
    open: {
      width: '16rem',
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 40
      }
    },
    closed: {
      width: '4rem',
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 40
      }
    }
  };

  const itemVariants = {
    open: {
      opacity: 1,
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 24
      }
    },
    closed: {
      opacity: 0,
      x: -20,
      transition: {
        duration: 0.2
      }
    }
  };

  return (
    <motion.div
      variants={sidebarVariants}
      animate={open ? 'open' : 'closed'}
      className="fixed left-0 top-0 h-full bg-white shadow-xl z-30 border-r border-gray-200"
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <motion.div
            initial={false}
            animate={{ opacity: open ? 1 : 0 }}
            transition={{ duration: 0.2 }}
            className="flex items-center space-x-3"
          >
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Factory className="w-5 h-5 text-white" />
            </div>
            {open && (
              <motion.div
                variants={itemVariants}
                className="flex flex-col"
              >
                <h1 className="text-lg font-bold text-gray-900">RC Dashboard</h1>
                <p className="text-xs text-gray-500">Route Card Management</p>
              </motion.div>
            )}
          </motion.div>
          
          <motion.button
            onClick={onToggle}
            className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <motion.div
              animate={{ rotate: open ? 0 : 180 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronLeft className="w-5 h-5 text-gray-600" />
            </motion.div>
          </motion.button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentView === item.id;
              
              return (
                <li key={item.id}>
                  <motion.button
                    onClick={() => onViewChange(item.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-xl transition-all duration-200 ${
                      isActive
                        ? `${item.bgColor} ${item.color} shadow-sm`
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Icon className={`w-5 h-5 ${isActive ? item.color : 'text-gray-500'}`} />
                    {open && (
                      <motion.span
                        variants={itemVariants}
                        className="font-medium text-sm"
                      >
                        {item.label}
                      </motion.span>
                    )}
                  </motion.button>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <motion.div
            className={`flex items-center space-x-3 px-3 py-2 rounded-xl ${
              open ? 'bg-gray-50' : 'justify-center'
            }`}
          >
            <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center">
              <Clock className="w-4 h-4 text-white" />
            </div>
            {open && (
              <motion.div
                variants={itemVariants}
                className="flex flex-col"
              >
                <p className="text-xs font-medium text-gray-900">Live Data</p>
                <p className="text-xs text-gray-500">Auto-refresh: ON</p>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default Sidebar;

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const sql = require('mssql');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Database configuration
const dbConfig = {
    server: 'WIN-PRK-SRV-01',
    database: 'ICsoft',
    options: {
        trustedConnection: true,
        trustServerCertificate: true,
        enableArithAbort: true,
        encrypt: false
    },
    pool: {
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000
    }
};

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors({
    origin: ['http://localhost:5173', 'http://localhost:3000'],
    credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Database connection pool
let pool;

let databaseConnected = false;

async function initializeDatabase() {
    try {
        pool = await sql.connect(dbConfig);
        console.log('✅ Database connected successfully');
        databaseConnected = true;
    } catch (err) {
        console.error('❌ Database connection failed:', err.message);
        console.log('🔄 Running in demo mode with mock data');
        databaseConnected = false;
    }
}

// Main RC Pending query
const RC_PENDING_QUERY = `
WITH ProcessDetails AS (
    SELECT pfs.SlNo, wo.WO_Qty, TRY_CAST(pfs.OkQty AS FLOAT) AS OkQty, 
           TRY_CAST(pfs.RejQty AS FLOAT) AS RejQty, TRY_CAST(pfs.StockQty AS FLOAT) AS StockQty, 
           pfs.PostProcessID, pfs.ProcessID, wo.WoNo, wo.WODate, wo.CloseDate, 
           p.InternalPartNo, p.prodname, wo.Status, wo.LocationID, p.ProdId
    FROM ProdnForgingStages pfs
    INNER JOIN WorkOrder wo ON wo.WoNo = pfs.WoNo
    INNER JOIN product p ON wo.prodid = p.prodid
    WHERE wo.LocationID like @locationFilter
      AND wo.WODate between @startDate and @endDate
),
StatusDetails AS (
    SELECT pd.WoNo, pd.WO_Qty, pd.PostProcessID, pd.ProcessID, 
           SUM(pd.OkQty) AS TotalOkQty, SUM(pd.RejQty) AS TotalRejQty, 
           SUM(pd.StockQty) AS TotalStockQty, pd.SlNo, pd.WODate, pd.CloseDate, 
           pd.InternalPartNo, pd.prodname, fp.ProcessName,
           CASE WHEN pd.PostProcessID = 0 
                THEN CASE WHEN ABS(SUM(pd.OkQty) - pd.WO_Qty) <= pd.WO_Qty * 0.10 
                          THEN 'RC Closed' 
                          ELSE 'Route Card Pending - Items Despatched' 
                     END 
                ELSE 'RC Closed' 
           END AS INFO,
           pd.Status AS WOSTATUS, pd.LocationID, pd.ProdId
    FROM ProcessDetails pd
    LEFT JOIN ForgingProcess fp ON pd.PostProcessID = fp.ProcessID
    GROUP BY pd.WoNo, pd.WO_Qty, pd.PostProcessID, pd.ProcessID, pd.SlNo, 
             pd.WODate, pd.CloseDate, pd.InternalPartNo, pd.prodname, 
             fp.ProcessName, pd.Status, pd.LocationID, pd.ProdId
),
RejectionSummary AS (
    SELECT pd.WoNo, SUM(pd.RejQty) AS TotalRejQty
    FROM ProcessDetails pd
    GROUP BY pd.WoNo
),
DespatchSummary AS (
    SELECT s.WoNo, SUM(s.despqty) AS DespQty,
           STUFF((
               SELECT ' Despatch Qty: ' + CAST(SUM(s1.despqty) AS VARCHAR(MAX)) + ' (' + i1.InvoiceNo1 + ') '
               FROM Sales_Despatch_Details s1
               INNER JOIN invoice i1 ON i1.InvoiceNo = s1.InvoiceNo
               WHERE s1.WoNo = s.WoNo
               GROUP BY i1.InvoiceNo1
               FOR XML PATH(''), TYPE
           ).value('.', 'NVARCHAR(MAX)'), 1, 1, '') AS DespatchQtyDetails
    FROM Sales_Despatch_Details s
    INNER JOIN invoice i ON i.InvoiceNo = s.InvoiceNo
    GROUP BY s.WoNo
),
StockSummary AS (
    SELECT WoNo, SUM(TRY_CAST(StockQty AS FLOAT)) AS TotalStockQtyAllStages
    FROM ProdnForgingStages
    GROUP BY WoNo
)
SELECT 
    CONCAT(sd.InternalPartNo, ' | ', sd.prodname) AS PartNoAndProductName, 
    sd.WoNo, 
    CONVERT(VARCHAR, sd.WODate, 105) AS WODate, 
    sd.WO_Qty,
    CASE WHEN sd.WOSTATUS = 'Closed' THEN 'RC Closed' 
         WHEN sd.WOSTATUS = 'Pending' THEN 'RC Pending' 
         ELSE 'Unknown Status' 
    END AS Status,
    sd.TotalOkQty, 
    COALESCE(ss.TotalStockQtyAllStages, 0) AS TotalStockQty, 
    rs.TotalRejQty, 
    COALESCE(ds.DespQty, 0) AS DespQty,
    COALESCE(REPLACE(ds.DespatchQtyDetails, ' || ', ' | '), 'Despatch Not Yet Done') AS DespatchQtyDetails,
    CASE WHEN sd.WOSTATUS = 'Closed' AND sd.CloseDate IS NOT NULL 
         THEN CONVERT(VARCHAR, sd.CloseDate, 105) 
         WHEN sd.WOSTATUS <> 'Closed' AND sd.CloseDate IS NULL 
         THEN 'In Progress' 
         ELSE 'N/A' 
    END AS CloseDate,
    CASE WHEN sd.WOSTATUS = 'Closed' AND sd.CloseDate IS NOT NULL 
         THEN DATEDIFF(DAY, sd.WODate, sd.CloseDate)
         WHEN sd.WOSTATUS = 'Pending' AND sd.WODate IS NOT NULL 
         THEN DATEDIFF(DAY, sd.WODate, GETDATE()) 
         ELSE NULL 
    END AS AgingInDays,
    CASE WHEN sd.WOSTATUS = 'Pending' THEN 1 
         WHEN sd.WOSTATUS = 'Closed' THEN 0 
         ELSE NULL 
    END AS RCStatus,
    sd.LocationID
FROM StatusDetails sd
LEFT JOIN RejectionSummary rs ON sd.WoNo = rs.WoNo
LEFT JOIN DespatchSummary ds ON sd.WoNo = ds.WoNo
LEFT JOIN StockSummary ss ON sd.WoNo = ss.WoNo
JOIN (
    SELECT WoNo, MAX(SlNo) AS MaxSlNo 
    FROM StatusDetails 
    GROUP BY WoNo
) AS LastRow ON sd.WoNo = LastRow.WoNo AND sd.SlNo = LastRow.MaxSlNo
WHERE sd.LocationID like @locationFilter  
  AND sd.ProdId like @prodFilter  
  AND sd.WODate between @startDate and @endDate 
  AND sd.WOStatus = @statusFilter  
ORDER BY sd.WODate, sd.wono
`;

// API Routes
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Generate mock data
function generateMockRCData(locationFilter = '%') {
    const locations = ['LOC001', 'LOC002', 'MAIN', 'WAREHOUSE', 'PLANT-A'];
    const products = [
        'GEAR001 | Transmission Gear',
        'SHAFT002 | Drive Shaft',
        'BEARING003 | Ball Bearing',
        'VALVE004 | Control Valve',
        'PUMP005 | Hydraulic Pump'
    ];

    const mockData = Array.from({ length: 75 }, (_, index) => {
        const location = locations[Math.floor(Math.random() * locations.length)];
        const product = products[Math.floor(Math.random() * products.length)];
        const woDate = new Date(2024, Math.floor(Math.random() * 8) + 4, Math.floor(Math.random() * 28) + 1);
        const agingDays = Math.floor(Math.random() * 60) + 1;
        const woQty = Math.floor(Math.random() * 1000) + 100;
        const okQty = Math.floor(woQty * (0.7 + Math.random() * 0.25));

        return {
            PartNoAndProductName: product,
            WoNo: `WO${String(index + 1).padStart(4, '0')}`,
            WODate: woDate.toLocaleDateString('en-GB'),
            WO_Qty: woQty,
            Status: Math.random() > 0.3 ? 'RC Pending' : 'RC Closed',
            TotalOkQty: okQty,
            TotalStockQty: Math.floor(Math.random() * 200) + 10,
            TotalRejQty: Math.floor(Math.random() * 50),
            DespQty: Math.floor(Math.random() * 500) + 20,
            DespatchQtyDetails: Math.random() > 0.4 ? `Despatch Qty: ${Math.floor(Math.random() * 300) + 50} (INV${String(Math.floor(Math.random() * 999) + 1).padStart(3, '0')})` : 'Despatch Not Yet Done',
            CloseDate: Math.random() > 0.6 ? new Date().toLocaleDateString('en-GB') : 'In Progress',
            AgingInDays: agingDays,
            LocationID: location,
            RCStatus: Math.random() > 0.3 ? 1 : 0
        };
    });

    // Filter by location if specified
    if (locationFilter !== '%' && locationFilter !== '') {
        return mockData.filter(item => item.LocationID === locationFilter);
    }

    return mockData;
}

// Get RC Pending data
app.get('/api/rc-pending', async (req, res) => {
    try {
        const {
            locationId = '%',
            prodId = '%',
            startDate = '2024-04-01',
            endDate = '2025-06-26',
            status = 'Pending'
        } = req.query;

        if (databaseConnected) {
            const request = pool.request();
            request.input('locationFilter', sql.VarChar, locationId);
            request.input('prodFilter', sql.VarChar, prodId);
            request.input('startDate', sql.Date, startDate);
            request.input('endDate', sql.Date, endDate);
            request.input('statusFilter', sql.VarChar, status);

            const result = await request.query(RC_PENDING_QUERY);

            res.json({
                success: true,
                data: result.recordset,
                count: result.recordset.length,
                timestamp: new Date().toISOString()
            });
        } else {
            // Return mock data
            const mockData = generateMockRCData(locationId);
            res.json({
                success: true,
                data: mockData,
                count: mockData.length,
                timestamp: new Date().toISOString(),
                demo: true
            });
        }
    } catch (error) {
        console.error('Error fetching RC pending data:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            message: error.message
        });
    }
});

// Get locations
app.get('/api/locations', async (req, res) => {
    try {
        if (databaseConnected) {
            const request = pool.request();
            const result = await request.query(`
                SELECT DISTINCT LocationID
                FROM WorkOrder
                WHERE LocationID IS NOT NULL AND LocationID != ''
                ORDER BY LocationID
            `);

            res.json({
                success: true,
                data: result.recordset,
                timestamp: new Date().toISOString()
            });
        } else {
            // Return mock locations
            const mockLocations = [
                { LocationID: 'LOC001' },
                { LocationID: 'LOC002' },
                { LocationID: 'MAIN' },
                { LocationID: 'WAREHOUSE' },
                { LocationID: 'PLANT-A' }
            ];

            res.json({
                success: true,
                data: mockLocations,
                timestamp: new Date().toISOString(),
                demo: true
            });
        }
    } catch (error) {
        console.error('Error fetching locations:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            message: error.message
        });
    }
});

// Get dashboard statistics
app.get('/api/dashboard-stats', async (req, res) => {
    try {
        const { locationId = '%' } = req.query;

        if (databaseConnected) {
            const request = pool.request();
            request.input('locationFilter', sql.VarChar, locationId);

            const result = await request.query(`
                SELECT
                    COUNT(*) as TotalRCs,
                    SUM(CASE WHEN Status = 'Pending' THEN 1 ELSE 0 END) as PendingRCs,
                    SUM(CASE WHEN Status = 'Closed' THEN 1 ELSE 0 END) as ClosedRCs,
                    AVG(CASE WHEN Status = 'Pending' THEN DATEDIFF(DAY, WODate, GETDATE()) END) as AvgAgingDays
                FROM WorkOrder
                WHERE LocationID like @locationFilter
                  AND WODate >= '2024-04-01'
            `);

            res.json({
                success: true,
                data: result.recordset[0],
                timestamp: new Date().toISOString()
            });
        } else {
            // Return mock stats
            const mockData = generateMockRCData(locationId);
            const pendingRCs = mockData.filter(item => item.Status === 'RC Pending').length;
            const closedRCs = mockData.filter(item => item.Status === 'RC Closed').length;
            const avgAging = mockData.reduce((sum, item) => sum + item.AgingInDays, 0) / mockData.length;

            res.json({
                success: true,
                data: {
                    TotalRCs: mockData.length,
                    PendingRCs: pendingRCs,
                    ClosedRCs: closedRCs,
                    AvgAgingDays: Math.round(avgAging * 10) / 10
                },
                timestamp: new Date().toISOString(),
                demo: true
            });
        }
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            message: error.message
        });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        success: false,
        error: 'Something went wrong!',
        message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: 'Route not found'
    });
});

// Start server
async function startServer() {
    await initializeDatabase();
    
    app.listen(PORT, () => {
        console.log(`🚀 Server running on port ${PORT}`);
        console.log(`📊 Dashboard API available at http://localhost:${PORT}/api`);
    });
}

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down server...');
    if (pool) {
        await pool.close();
    }
    process.exit(0);
});

startServer().catch(console.error);

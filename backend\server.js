const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const sql = require('mssql');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5001;

// Database configuration
const dbConfig = {
    server: 'WIN-PRK-SRV-01',
    database: 'ICsoft',
    user: 'sa',
    password: 'PRK@1234$',
    options: {
        trustServerCertificate: true,
        enableArithAbort: true,
        encrypt: false
    },
    pool: {
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000
    }
};

// Middleware
app.use(helmet());
app.use(compression());
// Very permissive CORS for local development
app.use(cors({
    origin: '*', // Allow all origins for development
    credentials: false, // Set to false when using wildcard origin
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
    optionsSuccessStatus: 200 // For legacy browser support
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Serve static files from parent directory (where HTML files are located)
app.use(express.static(path.join(__dirname, '..')));

// Database connection pool
let pool;

let databaseConnected = false;

async function initializeDatabase() {
    try {
        console.log('🔄 Initializing database connection...');
        console.log('Server:', dbConfig.server);
        console.log('Database:', dbConfig.database);

        pool = await sql.connect(dbConfig);
        console.log('✅ Database connected successfully');
        databaseConnected = true;

        // Test query
        const testResult = await pool.request().query('SELECT COUNT(*) as count FROM WorkOrder');
        console.log('✅ Test query successful, WorkOrder count:', testResult.recordset[0].count);

    } catch (err) {
        console.error('❌ Database connection failed:', err.message);
        console.log('🔄 Running in demo mode with mock data');
        databaseConnected = false;
    }
}

// Main RC Pending query
const RC_PENDING_QUERY = `
WITH ProcessDetails AS (
    SELECT pfs.SlNo, wo.WO_Qty, TRY_CAST(pfs.OkQty AS FLOAT) AS OkQty, 
           TRY_CAST(pfs.RejQty AS FLOAT) AS RejQty, TRY_CAST(pfs.StockQty AS FLOAT) AS StockQty, 
           pfs.PostProcessID, pfs.ProcessID, wo.WoNo, wo.WODate, wo.CloseDate, 
           p.InternalPartNo, p.prodname, wo.Status, wo.LocationID, p.ProdId
    FROM ProdnForgingStages pfs
    INNER JOIN WorkOrder wo ON wo.WoNo = pfs.WoNo
    INNER JOIN product p ON wo.prodid = p.prodid
    WHERE wo.LocationID like @locationFilter
      AND wo.WODate between @startDate and @endDate
),
StatusDetails AS (
    SELECT pd.WoNo, pd.WO_Qty, pd.PostProcessID, pd.ProcessID, 
           SUM(pd.OkQty) AS TotalOkQty, SUM(pd.RejQty) AS TotalRejQty, 
           SUM(pd.StockQty) AS TotalStockQty, pd.SlNo, pd.WODate, pd.CloseDate, 
           pd.InternalPartNo, pd.prodname, fp.ProcessName,
           CASE WHEN pd.PostProcessID = 0 
                THEN CASE WHEN ABS(SUM(pd.OkQty) - pd.WO_Qty) <= pd.WO_Qty * 0.10 
                          THEN 'RC Closed' 
                          ELSE 'Route Card Pending - Items Despatched' 
                     END 
                ELSE 'RC Closed' 
           END AS INFO,
           pd.Status AS WOSTATUS, pd.LocationID, pd.ProdId
    FROM ProcessDetails pd
    LEFT JOIN ForgingProcess fp ON pd.PostProcessID = fp.ProcessID
    GROUP BY pd.WoNo, pd.WO_Qty, pd.PostProcessID, pd.ProcessID, pd.SlNo, 
             pd.WODate, pd.CloseDate, pd.InternalPartNo, pd.prodname, 
             fp.ProcessName, pd.Status, pd.LocationID, pd.ProdId
),
RejectionSummary AS (
    SELECT pd.WoNo, SUM(pd.RejQty) AS TotalRejQty
    FROM ProcessDetails pd
    GROUP BY pd.WoNo
),
DespatchSummary AS (
    SELECT s.WoNo, SUM(s.despqty) AS DespQty,
           STUFF((
               SELECT ' Despatch Qty: ' + CAST(SUM(s1.despqty) AS VARCHAR(MAX)) + ' (' + i1.InvoiceNo1 + ') '
               FROM Sales_Despatch_Details s1
               INNER JOIN invoice i1 ON i1.InvoiceNo = s1.InvoiceNo
               WHERE s1.WoNo = s.WoNo
               GROUP BY i1.InvoiceNo1
               FOR XML PATH(''), TYPE
           ).value('.', 'NVARCHAR(MAX)'), 1, 1, '') AS DespatchQtyDetails
    FROM Sales_Despatch_Details s
    INNER JOIN invoice i ON i.InvoiceNo = s.InvoiceNo
    GROUP BY s.WoNo
),
StockSummary AS (
    SELECT WoNo, SUM(TRY_CAST(StockQty AS FLOAT)) AS TotalStockQtyAllStages
    FROM ProdnForgingStages
    GROUP BY WoNo
)
SELECT 
    CONCAT(sd.InternalPartNo, ' | ', sd.prodname) AS PartNoAndProductName, 
    sd.WoNo, 
    CONVERT(VARCHAR, sd.WODate, 105) AS WODate, 
    sd.WO_Qty,
    CASE WHEN sd.WOSTATUS = 'Closed' THEN 'RC Closed' 
         WHEN sd.WOSTATUS = 'Pending' THEN 'RC Pending' 
         ELSE 'Unknown Status' 
    END AS Status,
    sd.TotalOkQty, 
    COALESCE(ss.TotalStockQtyAllStages, 0) AS TotalStockQty, 
    rs.TotalRejQty, 
    COALESCE(ds.DespQty, 0) AS DespQty,
    COALESCE(REPLACE(ds.DespatchQtyDetails, ' || ', ' | '), 'Despatch Not Yet Done') AS DespatchQtyDetails,
    CASE WHEN sd.WOSTATUS = 'Closed' AND sd.CloseDate IS NOT NULL 
         THEN CONVERT(VARCHAR, sd.CloseDate, 105) 
         WHEN sd.WOSTATUS <> 'Closed' AND sd.CloseDate IS NULL 
         THEN 'In Progress' 
         ELSE 'N/A' 
    END AS CloseDate,
    CASE WHEN sd.WOSTATUS = 'Closed' AND sd.CloseDate IS NOT NULL 
         THEN DATEDIFF(DAY, sd.WODate, sd.CloseDate)
         WHEN sd.WOSTATUS = 'Pending' AND sd.WODate IS NOT NULL 
         THEN DATEDIFF(DAY, sd.WODate, GETDATE()) 
         ELSE NULL 
    END AS AgingInDays,
    CASE WHEN sd.WOSTATUS = 'Pending' THEN 1 
         WHEN sd.WOSTATUS = 'Closed' THEN 0 
         ELSE NULL 
    END AS RCStatus,
    sd.LocationID
FROM StatusDetails sd
LEFT JOIN RejectionSummary rs ON sd.WoNo = rs.WoNo
LEFT JOIN DespatchSummary ds ON sd.WoNo = ds.WoNo
LEFT JOIN StockSummary ss ON sd.WoNo = ss.WoNo
JOIN (
    SELECT WoNo, MAX(SlNo) AS MaxSlNo 
    FROM StatusDetails 
    GROUP BY WoNo
) AS LastRow ON sd.WoNo = LastRow.WoNo AND sd.SlNo = LastRow.MaxSlNo
WHERE sd.LocationID like @locationFilter  
  AND sd.ProdId like @prodFilter  
  AND sd.WODate between @startDate and @endDate 
  AND sd.WOStatus = @statusFilter  
ORDER BY sd.WODate, sd.wono
`;

// API Routes
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Generate mock data
function generateMockRCData(locationFilter = '%') {
    const locations = [
        { id: '2', name: 'PRK Sunkudkatte' },
        { id: '3', name: 'PRK Machohalli' },
        { id: 'LOC001', name: 'Location LOC001' },
        { id: 'MAIN', name: 'Location MAIN' }
    ];
    const products = [
        'GEAR001 | Transmission Gear',
        'SHAFT002 | Drive Shaft',
        'BEARING003 | Ball Bearing',
        'VALVE004 | Control Valve',
        'PUMP005 | Hydraulic Pump'
    ];

    const mockData = Array.from({ length: 75 }, (_, index) => {
        const location = locations[Math.floor(Math.random() * locations.length)];
        const product = products[Math.floor(Math.random() * products.length)];
        const woDate = new Date(2024, Math.floor(Math.random() * 8) + 4, Math.floor(Math.random() * 28) + 1);
        const agingDays = Math.floor(Math.random() * 60) + 1;
        const woQty = Math.floor(Math.random() * 1000) + 100;
        const okQty = Math.floor(woQty * (0.7 + Math.random() * 0.25));

        return {
            PartNoAndProductName: product,
            WoNo: `WO${String(index + 1).padStart(4, '0')}`,
            WODate: woDate.toLocaleDateString('en-GB'),
            WO_Qty: woQty,
            Status: 'RC Pending', // Only pending RCs
            TotalOkQty: okQty,
            TotalStockQty: Math.floor(Math.random() * 200) + 10,
            TotalRejQty: Math.floor(Math.random() * 50),
            DespQty: Math.floor(Math.random() * 500) + 20,
            DespatchQtyDetails: 'Despatch Not Yet Done', // Always pending
            CloseDate: 'In Progress', // Always in progress
            AgingInDays: agingDays,
            LocationID: location.id,
            LocationName: location.name,
            RCStatus: 1 // Always pending
        };
    });

    // Filter by location if specified
    if (locationFilter !== '%' && locationFilter !== '') {
        return mockData.filter(item => item.LocationID === locationFilter);
    }

    return mockData;
}

// Get RC Pending data - Only pending RCs, no date filters
app.get('/api/rc-pending', async (req, res) => {
    try {
        const {
            locationId = '%',
            prodId = '%'
        } = req.query;

        if (databaseConnected) {
            const request = pool.request();
            request.input('locationFilter', sql.VarChar, locationId);
            request.input('prodFilter', sql.VarChar, prodId);

            // Use the exact SQL query provided by user
            const result = await request.query(`
                WITH ProcessDetails AS (
                    SELECT pfs.SlNo, wo.WO_Qty, TRY_CAST(pfs.OkQty AS FLOAT) AS OkQty, TRY_CAST(pfs.RejQty AS FLOAT) AS RejQty, TRY_CAST(pfs.StockQty AS FLOAT) AS StockQty, pfs.PostProcessID, pfs.ProcessID, wo.WoNo, wo.WODate, wo.CloseDate, p.InternalPartNo, p.prodname, wo.Status, wo.LocationID, p.ProdId
                    FROM ProdnForgingStages pfs
                    INNER JOIN WorkOrder wo ON wo.WoNo = pfs.WoNo
                    INNER JOIN product p ON wo.prodid = p.prodid
                    WHERE wo.LocationID like @locationFilter
                ),
                StatusDetails AS (
                    SELECT pd.WoNo, pd.WO_Qty, pd.PostProcessID, pd.ProcessID, SUM(pd.OkQty) AS TotalOkQty, SUM(pd.RejQty) AS TotalRejQty, SUM(pd.StockQty) AS TotalStockQty, pd.SlNo, pd.WODate, pd.CloseDate, pd.InternalPartNo, pd.prodname, fp.ProcessName,
                           CASE WHEN pd.PostProcessID = 0 THEN CASE WHEN ABS(SUM(pd.OkQty) - pd.WO_Qty) <= pd.WO_Qty * 0.10 THEN 'RC Closed' ELSE 'Route Card Pending - Items Despatched' END ELSE 'RC Closed' END AS INFO,
                           pd.Status AS WOSTATUS, pd.LocationID, pd.ProdId
                    FROM ProcessDetails pd
                    LEFT JOIN ForgingProcess fp ON pd.PostProcessID = fp.ProcessID
                    GROUP BY pd.WoNo, pd.WO_Qty, pd.PostProcessID, pd.ProcessID, pd.SlNo, pd.WODate, pd.CloseDate, pd.InternalPartNo, pd.prodname, fp.ProcessName, pd.Status, pd.LocationID, pd.ProdId
                ),
                RejectionSummary AS (
                    SELECT pd.WoNo, SUM(pd.RejQty) AS TotalRejQty
                    FROM ProcessDetails pd
                    GROUP BY pd.WoNo
                ),
                DespatchSummary AS (
                    SELECT
                        s.WoNo,
                        SUM(s.despqty) AS DespQty,
                        STUFF((
                            SELECT ' Despatch Qty: ' + CAST(SUM(s1.despqty) AS VARCHAR(MAX)) + ' (' + i1.InvoiceNo1 + ') '
                            FROM Sales_Despatch_Details s1
                            INNER JOIN invoice i1 ON i1.InvoiceNo = s1.InvoiceNo
                            WHERE s1.WoNo = s.WoNo
                            GROUP BY i1.InvoiceNo1
                            FOR XML PATH(''), TYPE
                        ).value('.', 'NVARCHAR(MAX)'), 1, 1, '') AS DespatchQtyDetails
                    FROM Sales_Despatch_Details s
                    INNER JOIN invoice i ON i.InvoiceNo = s.InvoiceNo
                    GROUP BY s.WoNo
                ),
                StockSummary AS (
                    SELECT WoNo, SUM(TRY_CAST(StockQty AS FLOAT)) AS TotalStockQtyAllStages
                    FROM ProdnForgingStages
                    GROUP BY WoNo
                )
                SELECT
                    c.Location,
                    CONCAT(sd.InternalPartNo, ' | ', sd.prodname) AS PartNoAndProductName,
                    sd.WoNo,
                    CONVERT(VARCHAR, sd.WODate, 105) AS WODate,
                    sd.WO_Qty,
                    CASE WHEN sd.WOSTATUS = 'Closed' THEN 'RC Closed'
                         WHEN sd.WOSTATUS = 'Pending' THEN 'RC Pending'
                         ELSE 'Unknown Status' END AS Status,
                    sd.TotalOkQty,
                    COALESCE(ss.TotalStockQtyAllStages, 0) AS TotalStockQty,
                    rs.TotalRejQty,
                    COALESCE(ds.DespQty, 0) AS DespQty,
                    COALESCE(REPLACE(ds.DespatchQtyDetails, ' || ', ' | '), 'Despatch Not Yet Done') AS DespatchQtyDetails,
                    CASE WHEN sd.WOSTATUS = 'Closed' AND sd.CloseDate IS NOT NULL THEN CONVERT(VARCHAR, sd.CloseDate, 105)
                         WHEN sd.WOSTATUS <> 'Closed' AND sd.CloseDate IS NULL THEN 'In Progress' ELSE 'N/A' END AS CloseDate,
                    CASE WHEN sd.WOSTATUS = 'Closed' AND sd.CloseDate IS NOT NULL THEN DATEDIFF(DAY, sd.WODate, sd.CloseDate)
                         WHEN sd.WOSTATUS = 'Pending' AND sd.WODate IS NOT NULL THEN DATEDIFF(DAY, sd.WODate, GETDATE()) ELSE NULL END AS AgingInDays,
                    sd.LocationID
                FROM StatusDetails sd
                LEFT JOIN RejectionSummary rs ON sd.WoNo = rs.WoNo
                LEFT JOIN DespatchSummary ds ON sd.WoNo = ds.WoNo
                LEFT JOIN StockSummary ss ON sd.WoNo = ss.WoNo
                INNER JOIN Company c ON c.CompanyID = sd.LocationID
                JOIN (
                    SELECT WoNo, MAX(SlNo) AS MaxSlNo
                    FROM StatusDetails
                    GROUP BY WoNo
                ) AS LastRow ON sd.WoNo = LastRow.WoNo AND sd.SlNo = LastRow.MaxSlNo
                WHERE sd.LocationID like @locationFilter
                  AND sd.ProdId like @prodFilter
                  AND sd.WOStatus = 'Pending'
                ORDER BY sd.WODate, sd.WoNo
            `);

            res.json({
                success: true,
                data: result.recordset,
                count: result.recordset.length,
                timestamp: new Date().toISOString()
            });
        } else {
            // Return mock data - only pending RCs
            const mockData = generateMockRCData(locationId).filter(item => item.Status === 'RC Pending');
            res.json({
                success: true,
                data: mockData,
                count: mockData.length,
                timestamp: new Date().toISOString(),
                demo: true
            });
        }
    } catch (error) {
        console.error('Error fetching RC pending data:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            message: error.message
        });
    }
});

// Location mapping function
function getLocationName(locationId) {
    const locationMap = {
        '2': 'PRK Sunkudkatte',
        '3': 'PRK Machohalli'
    };
    return locationMap[locationId] || `Location ${locationId}`;
}

// Get locations with names
app.get('/api/locations', async (req, res) => {
    try {
        if (databaseConnected) {
            try {
                const request = pool.request();
                const result = await request.query(`
                    SELECT DISTINCT CAST(LocationID AS VARCHAR) as LocationID
                    FROM WorkOrder
                    WHERE LocationID IS NOT NULL AND LocationID != ''
                    ORDER BY CAST(LocationID AS VARCHAR)
                `);

                // Add location names to the results
                const locationsWithNames = result.recordset.map(loc => ({
                    LocationID: loc.LocationID,
                    LocationName: getLocationName(loc.LocationID)
                }));

                res.json({
                    success: true,
                    data: locationsWithNames,
                    timestamp: new Date().toISOString()
                });
            } catch (dbError) {
                console.error('Database error in locations:', dbError);
                // Fall back to mock data if database query fails
                const mockLocations = [
                    { LocationID: '2', LocationName: 'PRK Sunkudkatte' },
                    { LocationID: '3', LocationName: 'PRK Machohalli' }
                ];

                res.json({
                    success: true,
                    data: mockLocations,
                    timestamp: new Date().toISOString(),
                    demo: true
                });
            }
        } else {
            // Return mock locations with names
            const mockLocations = [
                { LocationID: '2', LocationName: 'PRK Sunkudkatte' },
                { LocationID: '3', LocationName: 'PRK Machohalli' },
                { LocationID: 'LOC001', LocationName: 'Location LOC001' },
                { LocationID: 'MAIN', LocationName: 'Location MAIN' }
            ];

            res.json({
                success: true,
                data: mockLocations,
                timestamp: new Date().toISOString(),
                demo: true
            });
        }
    } catch (error) {
        console.error('Error fetching locations:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            message: error.message
        });
    }
});

// Get dashboard statistics - Only Pending RCs
app.get('/api/dashboard-stats', async (req, res) => {
    try {
        const { locationId = '%' } = req.query;

        if (databaseConnected) {
            const request = pool.request();
            request.input('locationFilter', sql.VarChar, locationId);

            const result = await request.query(`
                SELECT
                    COUNT(*) as PendingRCs,
                    AVG(DATEDIFF(DAY, WODate, GETDATE())) as AvgAgingDays,
                    SUM(CASE WHEN DATEDIFF(DAY, WODate, GETDATE()) <= 15 THEN 1 ELSE 0 END) as RCsWithin15Days,
                    SUM(CASE WHEN DATEDIFF(DAY, WODate, GETDATE()) BETWEEN 16 AND 45 THEN 1 ELSE 0 END) as RCsBetween16And45Days,
                    SUM(CASE WHEN DATEDIFF(DAY, WODate, GETDATE()) BETWEEN 46 AND 90 THEN 1 ELSE 0 END) as RCsBetween46And90Days,
                    SUM(CASE WHEN DATEDIFF(DAY, WODate, GETDATE()) > 90 THEN 1 ELSE 0 END) as RCsOver90Days
                FROM WorkOrder
                WHERE LocationID like @locationFilter
                  AND Status = 'Pending'
            `);

            res.json({
                success: true,
                data: result.recordset[0],
                timestamp: new Date().toISOString()
            });
        } else {
            // Return mock stats - Only pending
            const mockData = generateMockRCData(locationId);
            const pendingRCs = mockData.filter(item => item.Status === 'RC Pending');
            const avgAging = pendingRCs.length > 0 ?
                pendingRCs.reduce((sum, item) => sum + item.AgingInDays, 0) / pendingRCs.length : 0;

            const within15Days = pendingRCs.filter(item => item.AgingInDays <= 15).length;
            const between16And45Days = pendingRCs.filter(item => item.AgingInDays >= 16 && item.AgingInDays <= 45).length;
            const between46And90Days = pendingRCs.filter(item => item.AgingInDays >= 46 && item.AgingInDays <= 90).length;
            const over90Days = pendingRCs.filter(item => item.AgingInDays > 90).length;

            res.json({
                success: true,
                data: {
                    PendingRCs: pendingRCs.length,
                    AvgAgingDays: Math.round(avgAging * 10) / 10,
                    RCsWithin15Days: within15Days,
                    RCsBetween16And45Days: between16And45Days,
                    RCsBetween46And90Days: between46And90Days,
                    RCsOver90Days: over90Days
                },
                timestamp: new Date().toISOString(),
                demo: true
            });
        }
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            message: error.message
        });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        success: false,
        error: 'Something went wrong!',
        message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: 'Route not found'
    });
});

// Start server
async function startServer() {
    console.log('🚀 Starting RC Dashboard Server...');

    await initializeDatabase();
    console.log('🔄 Database initialization complete');

    app.listen(PORT, () => {
        console.log(`✅ Server running on port ${PORT}`);
        console.log(`📊 Dashboard API available at http://localhost:${PORT}/api`);
        console.log(`🌐 Dashboard URL: http://localhost:${PORT}/rc-dashboard.html`);
    });
}

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down server...');
    if (pool) {
        await pool.close();
    }
    process.exit(0);
});

startServer().catch(console.error);

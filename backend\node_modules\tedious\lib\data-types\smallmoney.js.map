{"version": 3, "file": "smallmoney.js", "names": ["_moneyn", "_interopRequireDefault", "require", "obj", "__esModule", "default", "DATA_LENGTH", "<PERSON><PERSON><PERSON>", "from", "NULL_LENGTH", "SmallMoney", "id", "type", "name", "declaration", "generateTypeInfo", "MoneyN", "generateParameterLength", "parameter", "options", "value", "generateParameterData", "buffer", "alloc", "writeInt32LE", "validate", "parseFloat", "isNaN", "TypeError", "_default", "exports", "module"], "sources": ["../../src/data-types/smallmoney.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport MoneyN from './moneyn';\n\nconst DATA_LENGTH = Buffer.from([0x04]);\nconst NULL_LENGTH = Buffer.from([0x00]);\n\nconst SmallMoney: DataType = {\n  id: 0x7A,\n  type: 'MONEY4',\n  name: '<PERSON><PERSON><PERSON>',\n\n  declaration: function() {\n    return 'smallmoney';\n  },\n\n  generateTypeInfo: function() {\n    return Buffer.from([MoneyN.id, 0x04]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    return DATA_LENGTH;\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const buffer = Buffer.alloc(4);\n    buffer.writeInt32LE(parameter.value * 10000, 0);\n    yield buffer;\n  },\n\n  validate: function(value): null | number {\n    if (value == null) {\n      return null;\n    }\n    value = parseFloat(value);\n    if (isNaN(value)) {\n      throw new TypeError('Invalid number.');\n    }\n    if (value < -214748.3648 || value > 214748.3647) {\n      throw new TypeError('Value must be between -214748.3648 and 214748.3647.');\n    }\n    return value;\n  }\n};\n\nexport default SmallMoney;\nmodule.exports = SmallMoney;\n"], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA8B,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE9B,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,MAAMC,WAAW,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAME,UAAoB,GAAG;EAC3BC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,YAAY;EAElBC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,YAAY;EACrB,CAAC;EAEDC,gBAAgB,EAAE,SAAAA,CAAA,EAAW;IAC3B,OAAOR,MAAM,CAACC,IAAI,CAAC,CAACQ,eAAM,CAACL,EAAE,EAAE,IAAI,CAAC,CAAC;EACvC,CAAC;EAEDM,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOX,WAAW;IACpB;IAEA,OAAOH,WAAW;EACpB,CAAC;EAED,CAAEe,qBAAqBA,CAACH,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAME,MAAM,GAAGf,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,YAAY,CAACN,SAAS,CAACE,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC;IAC/C,MAAME,MAAM;EACd,CAAC;EAEDG,QAAQ,EAAE,SAAAA,CAASL,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACAA,KAAK,GAAGM,UAAU,CAACN,KAAK,CAAC;IACzB,IAAIO,KAAK,CAACP,KAAK,CAAC,EAAE;MAChB,MAAM,IAAIQ,SAAS,CAAC,iBAAiB,CAAC;IACxC;IACA,IAAIR,KAAK,GAAG,CAAC,WAAW,IAAIA,KAAK,GAAG,WAAW,EAAE;MAC/C,MAAM,IAAIQ,SAAS,CAAC,qDAAqD,CAAC;IAC5E;IACA,OAAOR,KAAK;EACd;AACF,CAAC;AAAC,IAAAS,QAAA,GAAAC,OAAA,CAAAzB,OAAA,GAEaK,UAAU;AACzBqB,MAAM,CAACD,OAAO,GAAGpB,UAAU"}
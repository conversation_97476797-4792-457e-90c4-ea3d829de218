<!DOCTYPE html>
<html>
<head>
    <title>Test API Response</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>API Response Test</h1>
    <button onclick="testAPI()">Test RC Pending API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result">Loading...</div>';
            
            try {
                console.log('Fetching data...');
                const response = await fetch('http://localhost:5001/api/rc-pending');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Data received:', data);
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h3>API Response Success!</h3>
                        <p><strong>Success:</strong> ${data.success}</p>
                        <p><strong>Record Count:</strong> ${data.data ? data.data.length : 0}</p>
                        <p><strong>Timestamp:</strong> ${data.timestamp}</p>
                        ${data.data && data.data.length > 0 ? `
                            <h4>First Record:</h4>
                            <pre>${JSON.stringify(data.data[0], null, 2)}</pre>
                        ` : '<p>No data records found</p>'}
                    </div>
                `;
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>API Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        // Auto-test on load
        window.addEventListener('load', () => {
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>

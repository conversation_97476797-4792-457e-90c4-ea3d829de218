{"version": 3, "file": "sendRequest.js", "sourceRoot": "", "sources": ["../../../src/client/sendRequest.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AA6BlC,kCAsCC;AAzDD,kDAAyD;AAEzD,sDAAsD;AACtD,8DAA8D;AAC9D,yDAAiE;AACjE,yDAAyD;AAGzD,iDAAoD;AAEpD;;;;;;;;GAQG;AACI,KAAK,UAAU,WAAW,CAC/B,MAAmB,EACnB,GAAW,EACX,QAAkB,EAClB,UAAqC,EAAE,EACvC,gBAA6B;;IAE7B,MAAM,UAAU,GAAG,gBAAgB,aAAhB,gBAAgB,cAAhB,gBAAgB,GAAI,IAAA,8CAA2B,GAAE,CAAC;IACrE,MAAM,OAAO,GAAG,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IAE3D,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACjE,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QAC1C,MAAM,MAAM,GAAG,MAAA,QAAQ,CAAC,kBAAkB,mCAAI,QAAQ,CAAC,iBAAiB,CAAC;QACzE,MAAM,UAAU,GACd,OAAO,CAAC,gBAAgB,IAAI,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAC3F,MAAM,IAAI,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,UAAU,CAAC;QAElC,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,EAAE,CAAC;YACxB,OAAO,CAAC,UAAU,iCAAM,QAAQ,KAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,IAAG,CAAC;QAChF,CAAC;QAED,OAAO;YACL,OAAO;YACP,OAAO;YACP,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE;YAC5B,IAAI;SACL,CAAC;IACJ,CAAC;IAAC,OAAO,CAAU,EAAE,CAAC;QACpB,IAAI,IAAA,0BAAW,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;YACvB,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC7C,0FAA0F;YAC1F,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,iCAAM,QAAQ,KAAE,OAAO,EAAE,UAAU,KAAI,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,CAAC,CAAC;IACV,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,qBAAqB,CAAC,UAAqC,EAAE;;IACpE,OAAO,CACL,MAAA,MAAA,OAAO,CAAC,WAAW,mCAClB,MAAA,OAAO,CAAC,OAAO,0CAAG,cAAc,CAAY,mCAC7C,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAC7B,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAS,cAAc,CAAC,IAAS;IAC/B,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7B,OAAO,0BAA0B,CAAC;IACpC,CAAC;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACjB,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,+CAA+C;YAC/C,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IACD,yBAAyB;IACzB,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAMD,SAAS,oBAAoB,CAC3B,MAAmB,EACnB,GAAW,EACX,UAAqC,EAAE;;IAEvC,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAC1D,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;IACjF,MAAM,UAAU,GAAG,IAAI,KAAK,SAAS,IAAI,aAAa,KAAK,SAAS,CAAC;IAErE,MAAM,OAAO,GAAG,IAAA,kCAAiB,gDAC5B,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAC3C,MAAM,EAAE,MAAA,MAAA,OAAO,CAAC,MAAM,mCAAI,MAAA,OAAO,CAAC,OAAO,0CAAE,MAAM,mCAAI,kBAAkB,KACpE,CAAC,UAAU;QACZ,kBAAkB,IAAI;QACpB,cAAc,EAAE,kBAAkB;KACnC,CAAC,EACJ,CAAC;IAEH,OAAO,IAAA,0CAAqB,EAAC;QAC3B,GAAG;QACH,MAAM;QACN,IAAI;QACJ,aAAa;QACb,OAAO;QACP,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;QACxD,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;QAC1C,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;QAC9C,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,oBAAoB,EAAE,IAAI;QAC1B,yBAAyB,EAAE,OAAO,CAAC,gBAAgB;YACjD,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACrC,CAAC,CAAC,SAAS;KACd,CAAC,CAAC;AACL,CAAC;AAOD;;GAEG;AACH,SAAS,cAAc,CAAC,IAAc,EAAE,cAAsB,EAAE;IAC9D,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;IAC7B,CAAC;IAED,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,IAAI,YAAY,QAAQ,EAAE,CAAC;QAChE,OAAO,EAAE,IAAI,EAAE,CAAC;IAClB,CAAC;IAED,IAAI,IAAA,gCAAgB,EAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO,EAAE,IAAI,EAAE,CAAC;IAClB,CAAC;IAED,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7B,OAAO,EAAE,IAAI,EAAE,IAAI,YAAY,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;IAC5E,CAAC;IAED,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAE5C,QAAQ,SAAS,EAAE,CAAC;QAClB,KAAK,kBAAkB;YACrB,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;QACxC,KAAK,qBAAqB;YACxB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACxB,OAAO,EAAE,aAAa,EAAE,IAAA,iCAAkB,EAAC,IAAwB,CAAC,EAAE,CAAC;YACzE,CAAC;YACD,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;QACxC,KAAK,YAAY;YACf,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAChC;YACE,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC7B,OAAO,EAAE,IAAI,EAAE,CAAC;YAClB,CAAC;YACD,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;IAC1C,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,QAA0B;;IACjD,gCAAgC;IAChC,MAAM,WAAW,GAAG,MAAA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,mCAAI,EAAE,CAAC;IAC/D,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,MAAM,WAAW,GAAG,MAAA,QAAQ,CAAC,UAAU,mCAAI,EAAE,CAAC;IAE9C,IAAI,SAAS,KAAK,YAAY,EAAE,CAAC;QAC/B,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC;IAC7B,CAAC;IACD,wDAAwD;IACxD,IAAI,CAAC;QACH,OAAO,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC3D,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,yDAAyD;QACzD,6BAA6B;QAC7B,IAAI,SAAS,KAAK,kBAAkB,EAAE,CAAC;YACrC,MAAM,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC1C,CAAC;QAED,gEAAgE;QAChE,cAAc;QACd,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC;IAC7B,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,QAA0B,EAAE,GAAQ;;IAC5D,MAAM,GAAG,GAAG,UAAU,GAAG,gDAAgD,QAAQ,CAAC,UAAU,GAAG,CAAC;IAChG,MAAM,OAAO,GAAG,MAAA,GAAG,CAAC,IAAI,mCAAI,wBAAS,CAAC,WAAW,CAAC;IAClD,OAAO,IAAI,wBAAS,CAAC,GAAG,EAAE;QACxB,IAAI,EAAE,OAAO;QACb,UAAU,EAAE,QAAQ,CAAC,MAAM;QAC3B,OAAO,EAAE,QAAQ,CAAC,OAAO;QACzB,QAAQ,EAAE,QAAQ;KACnB,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  HttpClient,\n  HttpMethods,\n  MultipartRequestBody,\n  PipelineRequest,\n  PipelineResponse,\n  RequestBodyType,\n} from \"../interfaces.js\";\nimport { isRestError, RestError } from \"../restError.js\";\nimport type { Pipeline } from \"../pipeline.js\";\nimport { createHttpHeaders } from \"../httpHeaders.js\";\nimport { createPipelineRequest } from \"../pipelineRequest.js\";\nimport { getCachedDefaultHttpsClient } from \"./clientHelpers.js\";\nimport { isReadableStream } from \"../util/typeGuards.js\";\nimport type { HttpResponse, RequestParameters } from \"./common.js\";\nimport type { PartDescriptor } from \"./multipart.js\";\nimport { buildMultipartBody } from \"./multipart.js\";\n\n/**\n * Helper function to send request used by the client\n * @param method - method to use to send the request\n * @param url - url to send the request to\n * @param pipeline - pipeline with the policies to run when sending the request\n * @param options - request options\n * @param customHttpClient - a custom HttpClient to use when making the request\n * @returns returns and HttpResponse\n */\nexport async function sendRequest(\n  method: HttpMethods,\n  url: string,\n  pipeline: Pipeline,\n  options: InternalRequestParameters = {},\n  customHttpClient?: HttpClient,\n): Promise<HttpResponse> {\n  const httpClient = customHttpClient ?? getCachedDefaultHttpsClient();\n  const request = buildPipelineRequest(method, url, options);\n\n  try {\n    const response = await pipeline.sendRequest(httpClient, request);\n    const headers = response.headers.toJSON();\n    const stream = response.readableStreamBody ?? response.browserStreamBody;\n    const parsedBody =\n      options.responseAsStream || stream !== undefined ? undefined : getResponseBody(response);\n    const body = stream ?? parsedBody;\n\n    if (options?.onResponse) {\n      options.onResponse({ ...response, request, rawHeaders: headers, parsedBody });\n    }\n\n    return {\n      request,\n      headers,\n      status: `${response.status}`,\n      body,\n    };\n  } catch (e: unknown) {\n    if (isRestError(e) && e.response && options.onResponse) {\n      const { response } = e;\n      const rawHeaders = response.headers.toJSON();\n      // UNBRANDED DIFFERENCE: onResponse callback does not have a second __legacyError property\n      options?.onResponse({ ...response, request, rawHeaders }, e);\n    }\n\n    throw e;\n  }\n}\n\n/**\n * Function to determine the request content type\n * @param options - request options InternalRequestParameters\n * @returns returns the content-type\n */\nfunction getRequestContentType(options: InternalRequestParameters = {}): string {\n  return (\n    options.contentType ??\n    (options.headers?.[\"content-type\"] as string) ??\n    getContentType(options.body)\n  );\n}\n\n/**\n * Function to determine the content-type of a body\n * this is used if an explicit content-type is not provided\n * @param body - body in the request\n * @returns returns the content-type\n */\nfunction getContentType(body: any): string | undefined {\n  if (ArrayBuffer.isView(body)) {\n    return \"application/octet-stream\";\n  }\n\n  if (typeof body === \"string\") {\n    try {\n      JSON.parse(body);\n      return \"application/json\";\n    } catch (error: any) {\n      // If we fail to parse the body, it is not json\n      return undefined;\n    }\n  }\n  // By default return json\n  return \"application/json\";\n}\n\nexport interface InternalRequestParameters extends RequestParameters {\n  responseAsStream?: boolean;\n}\n\nfunction buildPipelineRequest(\n  method: HttpMethods,\n  url: string,\n  options: InternalRequestParameters = {},\n): PipelineRequest {\n  const requestContentType = getRequestContentType(options);\n  const { body, multipartBody } = getRequestBody(options.body, requestContentType);\n  const hasContent = body !== undefined || multipartBody !== undefined;\n\n  const headers = createHttpHeaders({\n    ...(options.headers ? options.headers : {}),\n    accept: options.accept ?? options.headers?.accept ?? \"application/json\",\n    ...(hasContent &&\n      requestContentType && {\n        \"content-type\": requestContentType,\n      }),\n  });\n\n  return createPipelineRequest({\n    url,\n    method,\n    body,\n    multipartBody,\n    headers,\n    allowInsecureConnection: options.allowInsecureConnection,\n    abortSignal: options.abortSignal,\n    onUploadProgress: options.onUploadProgress,\n    onDownloadProgress: options.onDownloadProgress,\n    timeout: options.timeout,\n    enableBrowserStreams: true,\n    streamResponseStatusCodes: options.responseAsStream\n      ? new Set([Number.POSITIVE_INFINITY])\n      : undefined,\n  });\n}\n\ninterface RequestBody {\n  body?: RequestBodyType;\n  multipartBody?: MultipartRequestBody;\n}\n\n/**\n * Prepares the body before sending the request\n */\nfunction getRequestBody(body?: unknown, contentType: string = \"\"): RequestBody {\n  if (body === undefined) {\n    return { body: undefined };\n  }\n\n  if (typeof FormData !== \"undefined\" && body instanceof FormData) {\n    return { body };\n  }\n\n  if (isReadableStream(body)) {\n    return { body };\n  }\n\n  if (ArrayBuffer.isView(body)) {\n    return { body: body instanceof Uint8Array ? body : JSON.stringify(body) };\n  }\n\n  const firstType = contentType.split(\";\")[0];\n\n  switch (firstType) {\n    case \"application/json\":\n      return { body: JSON.stringify(body) };\n    case \"multipart/form-data\":\n      if (Array.isArray(body)) {\n        return { multipartBody: buildMultipartBody(body as PartDescriptor[]) };\n      }\n      return { body: JSON.stringify(body) };\n    case \"text/plain\":\n      return { body: String(body) };\n    default:\n      if (typeof body === \"string\") {\n        return { body };\n      }\n      return { body: JSON.stringify(body) };\n  }\n}\n\n/**\n * Prepares the response body\n */\nfunction getResponseBody(response: PipelineResponse): RequestBodyType | undefined {\n  // Set the default response type\n  const contentType = response.headers.get(\"content-type\") ?? \"\";\n  const firstType = contentType.split(\";\")[0];\n  const bodyToParse = response.bodyAsText ?? \"\";\n\n  if (firstType === \"text/plain\") {\n    return String(bodyToParse);\n  }\n  // Default to \"application/json\" and fallback to string;\n  try {\n    return bodyToParse ? JSON.parse(bodyToParse) : undefined;\n  } catch (error: any) {\n    // If we were supposed to get a JSON object and failed to\n    // parse, throw a parse error\n    if (firstType === \"application/json\") {\n      throw createParseError(response, error);\n    }\n\n    // We are not sure how to handle the response so we return it as\n    // plain text.\n    return String(bodyToParse);\n  }\n}\n\nfunction createParseError(response: PipelineResponse, err: any): RestError {\n  const msg = `Error \"${err}\" occurred while parsing the response body - ${response.bodyAsText}.`;\n  const errCode = err.code ?? RestError.PARSE_ERROR;\n  return new RestError(msg, {\n    code: errCode,\n    statusCode: response.status,\n    request: response.request,\n    response: response,\n  });\n}\n"]}
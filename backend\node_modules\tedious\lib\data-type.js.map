{"version": 3, "file": "data-type.js", "names": ["_null", "_interopRequireDefault", "require", "_tinyint", "_bit", "_smallint", "_int", "_smalldatetime", "_real", "_money", "_datetime", "_float", "_decimal", "_numeric", "_smallmoney", "_bigint", "_image", "_text", "_uniqueidentifier", "_intn", "_ntext", "_bitn", "_decimaln", "_numericn", "_floatn", "_moneyn", "_datetimen", "_varbinary", "_varchar", "_binary", "_char", "_nvarchar", "_nchar", "_xml", "_time", "_date", "_datetime2", "_datetimeoffset", "_udt", "_tvp", "_sqlVariant", "obj", "__esModule", "default", "TYPE", "exports", "<PERSON><PERSON>", "id", "TinyInt", "Bit", "SmallInt", "Int", "SmallDateTime", "Real", "Money", "DateTime", "Float", "Decimal", "Numeric", "SmallMoney", "BigInt", "Image", "Text", "UniqueIdentifier", "IntN", "NText", "BitN", "DecimalN", "NumericN", "FloatN", "MoneyN", "DateTimeN", "VarBinary", "VarChar", "Binary", "Char", "NVarChar", "NChar", "Xml", "Time", "Date", "DateTime2", "DateTimeOffset", "UDT", "TVP", "<PERSON><PERSON><PERSON>", "TYPES", "typeByName"], "sources": ["../src/data-type.ts"], "sourcesContent": ["import Null from './data-types/null';\nimport TinyInt from './data-types/tinyint';\nimport Bit from './data-types/bit';\nimport SmallInt from './data-types/smallint';\nimport Int from './data-types/int';\nimport SmallDateTime from './data-types/smalldatetime';\nimport Real from './data-types/real';\nimport Money from './data-types/money';\nimport DateTime from './data-types/datetime';\nimport Float from './data-types/float';\nimport Decimal from './data-types/decimal';\nimport Numeric from './data-types/numeric';\nimport SmallMoney from './data-types/smallmoney';\nimport BigInt from './data-types/bigint';\nimport Image from './data-types/image';\nimport Text from './data-types/text';\nimport UniqueIdentifier from './data-types/uniqueidentifier';\nimport IntN from './data-types/intn';\nimport NText from './data-types/ntext';\nimport BitN from './data-types/bitn';\nimport DecimalN from './data-types/decimaln';\nimport NumericN from './data-types/numericn';\nimport FloatN from './data-types/floatn';\nimport MoneyN from './data-types/moneyn';\nimport DateTimeN from './data-types/datetimen';\nimport VarBinary from './data-types/varbinary';\nimport VarChar from './data-types/varchar';\nimport Binary from './data-types/binary';\nimport Char from './data-types/char';\nimport NVarChar from './data-types/nvarchar';\nimport NChar from './data-types/nchar';\nimport Xml from './data-types/xml';\nimport Time from './data-types/time';\nimport Date from './data-types/date';\nimport DateTime2 from './data-types/datetime2';\nimport DateTimeOffset from './data-types/datetimeoffset';\nimport UDT from './data-types/udt';\nimport TVP from './data-types/tvp';\nimport Variant from './data-types/sql-variant';\nimport { type CryptoMetadata } from './always-encrypted/types';\n\nimport { type InternalConnectionOptions } from './connection';\nimport { Collation } from './collation';\n\nexport interface Parameter {\n  type: DataType;\n  name: string;\n\n  value: unknown;\n\n  output: boolean;\n  length?: number | undefined;\n  precision?: number | undefined;\n  scale?: number | undefined;\n\n  nullable?: boolean | undefined;\n\n  forceEncrypt?: boolean | undefined;\n  cryptoMetadata?: CryptoMetadata | undefined;\n  encryptedVal?: Buffer | undefined;\n}\n\n\nexport interface ParameterData<T = any> {\n  length?: number | undefined;\n  scale?: number | undefined;\n  precision?: number | undefined;\n\n  collation?: Collation | undefined;\n\n  value: T;\n}\n\nexport interface DataType {\n  id: number;\n  type: string;\n  name: string;\n\n  declaration(parameter: Parameter): string;\n  generateTypeInfo(parameter: ParameterData, options: InternalConnectionOptions): Buffer;\n  generateParameterLength(parameter: ParameterData, options: InternalConnectionOptions): Buffer;\n  generateParameterData(parameter: ParameterData, options: InternalConnectionOptions): Generator<Buffer, void>;\n  validate(value: any, collation: Collation | undefined, options?: InternalConnectionOptions): any; // TODO: Refactor 'any' and replace with more specific type.\n\n  hasTableName?: boolean;\n\n  resolveLength?: (parameter: Parameter) => number;\n  resolvePrecision?: (parameter: Parameter) => number;\n  resolveScale?: (parameter: Parameter) => number;\n}\n\nexport const TYPE = {\n  [Null.id]: Null,\n  [TinyInt.id]: TinyInt,\n  [Bit.id]: Bit,\n  [SmallInt.id]: SmallInt,\n  [Int.id]: Int,\n  [SmallDateTime.id]: SmallDateTime,\n  [Real.id]: Real,\n  [Money.id]: Money,\n  [DateTime.id]: DateTime,\n  [Float.id]: Float,\n  [Decimal.id]: Decimal,\n  [Numeric.id]: Numeric,\n  [SmallMoney.id]: SmallMoney,\n  [BigInt.id]: BigInt,\n  [Image.id]: Image,\n  [Text.id]: Text,\n  [UniqueIdentifier.id]: UniqueIdentifier,\n  [IntN.id]: IntN,\n  [NText.id]: NText,\n  [BitN.id]: BitN,\n  [DecimalN.id]: DecimalN,\n  [NumericN.id]: NumericN,\n  [FloatN.id]: FloatN,\n  [MoneyN.id]: MoneyN,\n  [DateTimeN.id]: DateTimeN,\n  [VarBinary.id]: VarBinary,\n  [VarChar.id]: VarChar,\n  [Binary.id]: Binary,\n  [Char.id]: Char,\n  [NVarChar.id]: NVarChar,\n  [NChar.id]: NChar,\n  [Xml.id]: Xml,\n  [Time.id]: Time,\n  [Date.id]: Date,\n  [DateTime2.id]: DateTime2,\n  [DateTimeOffset.id]: DateTimeOffset,\n  [UDT.id]: UDT,\n  [TVP.id]: TVP,\n  [Variant.id]: Variant,\n};\n\n/**\n * <table>\n * <thead>\n *   <tr>\n *     <th>Type</th>\n *     <th>Constant</th>\n *     <th>JavaScript</th>\n *     <th>Result set</th>\n *     <th>Parameter</th>\n *   </tr>\n * </thead>\n *\n * <tbody>\n *   <tr class=\"group-heading\">\n *     <th colspan=\"5\">Exact numerics</th>\n *   </tr>\n *   <tr>\n *     <td><code>bit</code></td>\n *     <td><code>[[TYPES.Bit]]</code></td>\n *     <td><code>boolean</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>tinyint</code></td>\n *     <td><code>[[TYPES.TinyInt]]</code></td>\n *     <td><code>number</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>smallint</code></td>\n *     <td><code>[[TYPES.SmallInt]]</code></td>\n *     <td><code>number</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>int</code></td>\n *     <td><code>[[TYPES.Int]]</code></td>\n *     <td><code>number</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>bigint</code><sup>1</sup></td>\n *     <td><code>[[TYPES.BigInt]]</code></td>\n *     <td><code>string</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>numeric</code><sup>2</sup></td>\n *     <td><code>[[TYPES.Numeric]]</code></td>\n *     <td><code>number</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>decimal</code><sup>2</sup></td>\n *     <td><code>[[TYPES.Decimal]]</code></td>\n *     <td><code>number</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>smallmoney</code></td>\n *     <td><code>[[TYPES.SmallMoney]]</code></td>\n *     <td><code>number</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>money</code></td>\n *     <td><code>[[TYPES.Money]]</code></td>\n *     <td><code>number</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n * </tbody>\n *\n * <tbody>\n *   <tr class=\"group-heading\">\n *     <th colspan=\"5\">Approximate numerics</th>\n *   </tr>\n *   <tr>\n *     <td><code>float</code></td>\n *     <td><code>[[TYPES.Float]]</code></td>\n *     <td><code>number</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>real</code></td>\n *     <td><code>[[TYPES.Real]]</code></td>\n *     <td><code>number</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n * </tbody>\n *\n * <tbody>\n *   <tr class=\"group-heading\">\n *     <th colspan=\"4\">Date and Time</th>\n *   </tr>\n *   <tr>\n *     <td><code>smalldatetime</code></td>\n *     <td><code>[[TYPES.SmallDateTime]]</code></td>\n *     <td><code>Date</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>datetime</code></td>\n *     <td><code>[[TYPES.DateTime]]</code></td>\n *     <td><code>Date</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>datetime2</code></td>\n *     <td><code>[[TYPES.DateTime2]]</code></td>\n *     <td><code>Date</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>datetimeoffset</code></td>\n *     <td><code>[[TYPES.DateTimeOffset]]</code></td>\n *     <td><code>Date</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>time</code></td>\n *     <td><code>[[TYPES.Time]]</code></td>\n *     <td><code>Date</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>date</code></td>\n *     <td><code>[[TYPES.Date]]</code></td>\n *     <td><code>Date</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n * </tbody>\n *\n * <tbody>\n *   <tr class=\"group-heading\">\n *     <th colspan=\"4\">Character Strings</th>\n *   </tr>\n *   <tr>\n *     <td><code>char</code></td>\n *     <td><code>[[TYPES.Char]]</code></td>\n *     <td><code>string</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>varchar</code><sup>3</sup></td>\n *     <td><code>[[TYPES.VarChar]]</code></td>\n *     <td><code>string</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>text</code></td>\n *     <td><code>[[TYPES.Text]]</code></td>\n *     <td><code>string</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n * </tbody>\n *\n * <tbody>\n *   <tr class=\"group-heading\">\n *     <th colspan=\"4\">Unicode Strings</th>\n *   </tr>\n *   <tr>\n *     <td><code>nchar</code></td>\n *     <td><code>[[TYPES.NChar]]</code></td>\n *     <td><code>string</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>nvarchar</code><sup>3</sup></td>\n *     <td><code>[[TYPES.NVarChar]]</code></td>\n *     <td><code>string</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>ntext</code></td>\n *     <td><code>[[TYPES.NText]]</code></td>\n *     <td><code>string</code></td>\n *     <td>✓</td>\n *     <td>-</td>\n *   </tr>\n * </tbody>\n *\n * <tbody>\n *   <tr class=\"group-heading\">\n *     <th colspan=\"5\">Binary Strings<sup>4</sup></th>\n *   </tr>\n *   <tr>\n *     <td><code>binary</code></td>\n *     <td><code>[[TYPES.Binary]]</code></td>\n *     <td><code>Buffer</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>varbinary</code></td>\n *     <td><code>[[TYPES.VarBinary]]</code></td>\n *     <td><code>Buffer</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>image</code></td>\n *     <td><code>[[TYPES.Image]]</code></td>\n *     <td><code>Buffer</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n * </tbody>\n *\n * <tbody>\n *   <tr class=\"group-heading\">\n *     <th colspan=\"5\">Other Data Types</th>\n *   </tr>\n *   <tr>\n *     <td><code>TVP</code></td>\n *     <td><code>[[TYPES.TVP]]</code></td>\n *     <td><code>Object</code></td>\n *     <td>-</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>UDT</code></td>\n *     <td><code>[[TYPES.UDT]]</code></td>\n *     <td><code>Buffer</code></td>\n *     <td>✓</td>\n *     <td>-</td>\n *   </tr>\n *   <tr>\n *     <td><code>uniqueidentifier</code><sup>4</sup></td>\n *     <td><code>[[TYPES.UniqueIdentifier]]</code></td>\n *     <td><code>string</code></td>\n *     <td>✓</td>\n *     <td>✓</td>\n *   </tr>\n *   <tr>\n *     <td><code>variant</code></td>\n *     <td><code>[[TYPES.Variant]]</code></td>\n *     <td><code>any</code></td>\n *     <td>✓</td>\n *     <td>-</td>\n *   </tr>\n *   <tr>\n *     <td><code>xml</code></td>\n *     <td><code>[[TYPES.Xml]]</code></td>\n *     <td><code>string</code></td>\n *     <td>✓</td>\n *     <td>-</td>\n *   </tr>\n * </tbody>\n * </table>\n *\n * <ol>\n *   <li>\n *     <h4>BigInt</h4>\n *     <p>\n *       Values are returned as a string. This is because values can exceed 53 bits of significant data, which is greater than a\n *       Javascript <code>number</code> type can represent as an integer.\n *     </p>\n *   </li>\n *   <li>\n *     <h4>Numerical, Decimal</h4>\n *     <p>\n *       For input parameters, default precision is 18 and default scale is 0. Maximum supported precision is 19.\n *     </p>\n *   </li>\n *   <li>\n *     <h4>VarChar, NVarChar</h4>\n *     <p>\n *       <code>varchar(max)</code> and <code>nvarchar(max)</code> are also supported.\n *     </p>\n *   </li>\n *   <li>\n *     <h4>UniqueIdentifier</h4>\n *     <p>\n *       Values are returned as a 16 byte hexadecimal string.\n *     </p>\n *     <p>\n *       Note that the order of bytes is not the same as the character representation. See\n *       <a href=\"http://msdn.microsoft.com/en-us/library/ms190215.aspx\">Using uniqueidentifier Data</a>\n *       for an example of the different ordering of bytes.\n *     </p>\n *   </li>\n * </ol>\n */\nexport const TYPES = {\n  TinyInt,\n  Bit,\n  SmallInt,\n  Int,\n  SmallDateTime,\n  Real,\n  Money,\n  DateTime,\n  Float,\n  Decimal,\n  Numeric,\n  SmallMoney,\n  BigInt,\n  Image,\n  Text,\n  UniqueIdentifier,\n  NText,\n  VarBinary,\n  VarChar,\n  Binary,\n  Char,\n  NVarChar,\n  NChar,\n  Xml,\n  Time,\n  Date,\n  DateTime2,\n  DateTimeOffset,\n  UDT,\n  TVP,\n  Variant\n};\n\nexport const typeByName = TYPES;\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,IAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,IAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,cAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,KAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,MAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,SAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,MAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,QAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,QAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACA,IAAAY,WAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,OAAA,GAAAd,sBAAA,CAAAC,OAAA;AACA,IAAAc,MAAA,GAAAf,sBAAA,CAAAC,OAAA;AACA,IAAAe,KAAA,GAAAhB,sBAAA,CAAAC,OAAA;AACA,IAAAgB,iBAAA,GAAAjB,sBAAA,CAAAC,OAAA;AACA,IAAAiB,KAAA,GAAAlB,sBAAA,CAAAC,OAAA;AACA,IAAAkB,MAAA,GAAAnB,sBAAA,CAAAC,OAAA;AACA,IAAAmB,KAAA,GAAApB,sBAAA,CAAAC,OAAA;AACA,IAAAoB,SAAA,GAAArB,sBAAA,CAAAC,OAAA;AACA,IAAAqB,SAAA,GAAAtB,sBAAA,CAAAC,OAAA;AACA,IAAAsB,OAAA,GAAAvB,sBAAA,CAAAC,OAAA;AACA,IAAAuB,OAAA,GAAAxB,sBAAA,CAAAC,OAAA;AACA,IAAAwB,UAAA,GAAAzB,sBAAA,CAAAC,OAAA;AACA,IAAAyB,UAAA,GAAA1B,sBAAA,CAAAC,OAAA;AACA,IAAA0B,QAAA,GAAA3B,sBAAA,CAAAC,OAAA;AACA,IAAA2B,OAAA,GAAA5B,sBAAA,CAAAC,OAAA;AACA,IAAA4B,KAAA,GAAA7B,sBAAA,CAAAC,OAAA;AACA,IAAA6B,SAAA,GAAA9B,sBAAA,CAAAC,OAAA;AACA,IAAA8B,MAAA,GAAA/B,sBAAA,CAAAC,OAAA;AACA,IAAA+B,IAAA,GAAAhC,sBAAA,CAAAC,OAAA;AACA,IAAAgC,KAAA,GAAAjC,sBAAA,CAAAC,OAAA;AACA,IAAAiC,KAAA,GAAAlC,sBAAA,CAAAC,OAAA;AACA,IAAAkC,UAAA,GAAAnC,sBAAA,CAAAC,OAAA;AACA,IAAAmC,eAAA,GAAApC,sBAAA,CAAAC,OAAA;AACA,IAAAoC,IAAA,GAAArC,sBAAA,CAAAC,OAAA;AACA,IAAAqC,IAAA,GAAAtC,sBAAA,CAAAC,OAAA;AACA,IAAAsC,WAAA,GAAAvC,sBAAA,CAAAC,OAAA;AAA+C,SAAAD,uBAAAwC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAqDxC,MAAMG,IAAI,GAAAC,OAAA,CAAAD,IAAA,GAAG;EAClB,CAACE,aAAI,CAACC,EAAE,GAAGD,aAAI;EACf,CAACE,gBAAO,CAACD,EAAE,GAAGC,gBAAO;EACrB,CAACC,YAAG,CAACF,EAAE,GAAGE,YAAG;EACb,CAACC,iBAAQ,CAACH,EAAE,GAAGG,iBAAQ;EACvB,CAACC,YAAG,CAACJ,EAAE,GAAGI,YAAG;EACb,CAACC,sBAAa,CAACL,EAAE,GAAGK,sBAAa;EACjC,CAACC,aAAI,CAACN,EAAE,GAAGM,aAAI;EACf,CAACC,cAAK,CAACP,EAAE,GAAGO,cAAK;EACjB,CAACC,iBAAQ,CAACR,EAAE,GAAGQ,iBAAQ;EACvB,CAACC,cAAK,CAACT,EAAE,GAAGS,cAAK;EACjB,CAACC,gBAAO,CAACV,EAAE,GAAGU,gBAAO;EACrB,CAACC,gBAAO,CAACX,EAAE,GAAGW,gBAAO;EACrB,CAACC,mBAAU,CAACZ,EAAE,GAAGY,mBAAU;EAC3B,CAACC,eAAM,CAACb,EAAE,GAAGa,eAAM;EACnB,CAACC,cAAK,CAACd,EAAE,GAAGc,cAAK;EACjB,CAACC,aAAI,CAACf,EAAE,GAAGe,aAAI;EACf,CAACC,yBAAgB,CAAChB,EAAE,GAAGgB,yBAAgB;EACvC,CAACC,aAAI,CAACjB,EAAE,GAAGiB,aAAI;EACf,CAACC,cAAK,CAAClB,EAAE,GAAGkB,cAAK;EACjB,CAACC,aAAI,CAACnB,EAAE,GAAGmB,aAAI;EACf,CAACC,iBAAQ,CAACpB,EAAE,GAAGoB,iBAAQ;EACvB,CAACC,iBAAQ,CAACrB,EAAE,GAAGqB,iBAAQ;EACvB,CAACC,eAAM,CAACtB,EAAE,GAAGsB,eAAM;EACnB,CAACC,eAAM,CAACvB,EAAE,GAAGuB,eAAM;EACnB,CAACC,kBAAS,CAACxB,EAAE,GAAGwB,kBAAS;EACzB,CAACC,kBAAS,CAACzB,EAAE,GAAGyB,kBAAS;EACzB,CAACC,gBAAO,CAAC1B,EAAE,GAAG0B,gBAAO;EACrB,CAACC,eAAM,CAAC3B,EAAE,GAAG2B,eAAM;EACnB,CAACC,aAAI,CAAC5B,EAAE,GAAG4B,aAAI;EACf,CAACC,iBAAQ,CAAC7B,EAAE,GAAG6B,iBAAQ;EACvB,CAACC,cAAK,CAAC9B,EAAE,GAAG8B,cAAK;EACjB,CAACC,YAAG,CAAC/B,EAAE,GAAG+B,YAAG;EACb,CAACC,aAAI,CAAChC,EAAE,GAAGgC,aAAI;EACf,CAACC,aAAI,CAACjC,EAAE,GAAGiC,aAAI;EACf,CAACC,kBAAS,CAAClC,EAAE,GAAGkC,kBAAS;EACzB,CAACC,uBAAc,CAACnC,EAAE,GAAGmC,uBAAc;EACnC,CAACC,YAAG,CAACpC,EAAE,GAAGoC,YAAG;EACb,CAACC,YAAG,CAACrC,EAAE,GAAGqC,YAAG;EACb,CAACC,mBAAO,CAACtC,EAAE,GAAGsC;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,KAAK,GAAAzC,OAAA,CAAAyC,KAAA,GAAG;EACnBtC,OAAO,EAAPA,gBAAO;EACPC,GAAG,EAAHA,YAAG;EACHC,QAAQ,EAARA,iBAAQ;EACRC,GAAG,EAAHA,YAAG;EACHC,aAAa,EAAbA,sBAAa;EACbC,IAAI,EAAJA,aAAI;EACJC,KAAK,EAALA,cAAK;EACLC,QAAQ,EAARA,iBAAQ;EACRC,KAAK,EAALA,cAAK;EACLC,OAAO,EAAPA,gBAAO;EACPC,OAAO,EAAPA,gBAAO;EACPC,UAAU,EAAVA,mBAAU;EACVC,MAAM,EAANA,eAAM;EACNC,KAAK,EAALA,cAAK;EACLC,IAAI,EAAJA,aAAI;EACJC,gBAAgB,EAAhBA,yBAAgB;EAChBE,KAAK,EAALA,cAAK;EACLO,SAAS,EAATA,kBAAS;EACTC,OAAO,EAAPA,gBAAO;EACPC,MAAM,EAANA,eAAM;EACNC,IAAI,EAAJA,aAAI;EACJC,QAAQ,EAARA,iBAAQ;EACRC,KAAK,EAALA,cAAK;EACLC,GAAG,EAAHA,YAAG;EACHC,IAAI,EAAJA,aAAI;EACJC,IAAI,EAAJA,aAAI;EACJC,SAAS,EAATA,kBAAS;EACTC,cAAc,EAAdA,uBAAc;EACdC,GAAG,EAAHA,YAAG;EACHC,GAAG,EAAHA,YAAG;EACHC,OAAO,EAAPA;AACF,CAAC;AAEM,MAAME,UAAU,GAAA1C,OAAA,CAAA0C,UAAA,GAAGD,KAAK"}
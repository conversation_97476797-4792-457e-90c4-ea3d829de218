<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Table - RC Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background: #f9f9f9;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        #log {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug Table - RC Dashboard</h1>
        
        <div class="status info" id="status">Ready to test...</div>
        
        <button class="btn" onclick="testAPI()">Test API Connection</button>
        <button class="btn" onclick="loadTableData()">Load Table Data</button>
        <button class="btn" onclick="clearLog()">Clear Log</button>
        
        <div id="log"></div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }
        
        async function testAPI() {
            try {
                log('Testing API connection...');
                setStatus('Testing API...', 'info');
                
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (data.success) {
                    log('✅ API connection successful');
                    setStatus('API connection successful', 'success');
                } else {
                    log('❌ API returned error: ' + data.error);
                    setStatus('API error: ' + data.error, 'error');
                }
            } catch (error) {
                log('❌ API connection failed: ' + error.message);
                setStatus('API connection failed: ' + error.message, 'error');
            }
        }
        
        async function loadTableData() {
            try {
                log('Starting table data load...');
                setStatus('Loading table data...', 'info');
                
                const url = `${API_BASE}/rc-pending`;
                log('Fetching from: ' + url);
                
                const response = await fetch(url);
                log('Response status: ' + response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log('Response received, parsing...');
                
                if (!data.success) {
                    throw new Error('API returned success=false: ' + (data.error || 'Unknown error'));
                }
                
                if (!data.data) {
                    throw new Error('No data array in response');
                }
                
                log(`✅ Data loaded successfully: ${data.data.length} records`);
                setStatus(`Data loaded: ${data.data.length} records`, 'success');
                
                // Display first 10 records in a simple table
                displayTable(data.data.slice(0, 10));
                
            } catch (error) {
                log('❌ Error loading table data: ' + error.message);
                setStatus('Error: ' + error.message, 'error');
                console.error('Full error:', error);
            }
        }
        
        function displayTable(records) {
            log('Rendering table with ' + records.length + ' records...');
            
            const resultsDiv = document.getElementById('results');
            
            if (records.length === 0) {
                resultsDiv.innerHTML = '<div class="status error">No records to display</div>';
                return;
            }
            
            let tableHTML = `
                <h3>First 10 Records (out of total)</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Work Order</th>
                            <th>Product Name</th>
                            <th>WO Date</th>
                            <th>WO Qty</th>
                            <th>OK Qty</th>
                            <th>Aging</th>
                            <th>Location</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            records.forEach((record, index) => {
                try {
                    const productName = (record.PartNoAndProductName || 'Unknown').split(' | ')[1] || 'Unknown';
                    
                    tableHTML += `
                        <tr>
                            <td>${record.WoNo || 'N/A'}</td>
                            <td>${productName}</td>
                            <td>${record.WODate || 'N/A'}</td>
                            <td>${(record.WO_Qty || 0).toLocaleString()}</td>
                            <td>${(record.TotalOkQty || 0).toLocaleString()}</td>
                            <td>${record.AgingInDays || 0} days</td>
                            <td>${record.Location || 'Unknown'}</td>
                        </tr>
                    `;
                } catch (err) {
                    log(`Error processing record ${index}: ${err.message}`);
                }
            });
            
            tableHTML += `
                    </tbody>
                </table>
            `;
            
            resultsDiv.innerHTML = tableHTML;
            log('✅ Table rendered successfully');
        }
        
        // Auto-test on load
        window.addEventListener('load', () => {
            log('Page loaded, ready for testing');
            setTimeout(() => {
                testAPI();
            }, 1000);
        });
    </script>
</body>
</html>

{"version": 3, "file": "userAgent.js", "sourceRoot": "", "sources": ["../../../src/util/userAgent.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,aAAa,EAAE,uBAAuB,EAAE,MAAM,wBAAwB,CAAC;AAChF,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAE9C,SAAS,kBAAkB,CAAC,aAAkC;IAC5D,MAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,aAAa,EAAE,CAAC;QACzC,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QAC9C,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpB,CAAC;IACD,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,sBAAsB;IACpC,OAAO,aAAa,EAAE,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAAC,MAAe;IACrD,MAAM,WAAW,GAAG,IAAI,GAAG,EAAkB,CAAC;IAC9C,WAAW,CAAC,GAAG,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;IACnD,MAAM,uBAAuB,CAAC,WAAW,CAAC,CAAC;IAC3C,MAAM,YAAY,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;IACrD,MAAM,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,YAAY,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC;IAC3E,OAAO,cAAc,CAAC;AACxB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { getHeaderName, setPlatformSpecificData } from \"./userAgentPlatform.js\";\nimport { SDK_VERSION } from \"../constants.js\";\n\nfunction getUserAgentString(telemetryInfo: Map<string, string>): string {\n  const parts: string[] = [];\n  for (const [key, value] of telemetryInfo) {\n    const token = value ? `${key}/${value}` : key;\n    parts.push(token);\n  }\n  return parts.join(\" \");\n}\n\n/**\n * @internal\n */\nexport function getUserAgentHeaderName(): string {\n  return getHeaderName();\n}\n\n/**\n * @internal\n */\nexport async function getUserAgentValue(prefix?: string): Promise<string> {\n  const runtimeInfo = new Map<string, string>();\n  runtimeInfo.set(\"core-rest-pipeline\", SDK_VERSION);\n  await setPlatformSpecificData(runtimeInfo);\n  const defaultAgent = getUserAgentString(runtimeInfo);\n  const userAgentValue = prefix ? `${prefix} ${defaultAgent}` : defaultAgent;\n  return userAgentValue;\n}\n"]}
# RC Dashboard - Route Card Management System

A comprehensive dashboard for monitoring and managing Route Cards (RC) in manufacturing operations.

## 🚀 Features

- **Real-time RC Monitoring**: Track pending route cards with live data
- **Aging Analysis**: Categorize RCs by aging (≤15, 16-45, 46-90, >90 days)
- **Location Filtering**: Filter data by manufacturing location
- **Auto-refresh**: Automatic data refresh every 5 minutes
- **Auto-slide**: Automatic pagination through large datasets
- **Professional UI**: Modern, responsive design with smooth animations
- **SQL Server Integration**: Direct connection to your manufacturing database

## 📊 Dashboard Components

### Statistics Cards
- **Total Pending RCs**: Overall count of pending route cards
- **Aging Categories**: Visual breakdown by aging periods
- **Color-coded Indicators**: <PERSON> (≤15), <PERSON> (16-45), <PERSON> (46-90), <PERSON> (>90)

### Data Table
- **Comprehensive View**: Work Order, Product, Quantities, Aging, Location
- **Auto-scroll**: Smooth scrolling through data
- **Pagination**: Automatic slide between pages
- **Responsive Design**: Works on all screen sizes

## 🛠️ Installation

### Prerequisites
- Node.js (LTS version recommended)
- SQL Server access to ICsoft database
- Windows environment

### Quick Setup

1. **Install Dependencies**
   ```bash
   .\install-rc-dashboard.bat
   ```

2. **Test System**
   ```bash
   .\test-rc-dashboard.bat
   ```

3. **Start Dashboard**
   ```bash
   .\start-rc-dashboard.bat
   ```

4. **Stop Dashboard**
   ```bash
   .\stop-rc-dashboard.bat
   ```

## 🔧 Configuration

### Database Connection
Edit `backend/server.js` to configure your database connection:

```javascript
const dbConfig = {
    server: 'WIN-PRK-SRV-01',
    database: 'ICsoft',
    user: 'sa',
    password: 'PRK@1234',
    options: {
        encrypt: false,
        trustServerCertificate: true
    }
};
```

### Port Configuration
Default port is 5001. To change:
```javascript
const PORT = process.env.PORT || 5001;
```

## 📁 File Structure

```
RC pending Dashboard/
├── backend/
│   ├── server.js           # Main server file
│   ├── package.json        # Dependencies
│   └── node_modules/       # Installed packages
├── rc-dashboard.html       # Main dashboard
├── debug-rc-dashboard.html # Debug version
├── start-rc-dashboard.bat  # Start services
├── stop-rc-dashboard.bat   # Stop services
├── test-rc-dashboard.bat   # Test system
├── install-rc-dashboard.bat # Install dependencies
└── README.md              # This file
```

## 🔍 SQL Query

The dashboard uses your provided SQL query with the following CTEs:
- **ProcessDetails**: Basic work order and production data
- **StatusDetails**: Calculated totals and RC status
- **RejectionSummary**: Rejection quantities by work order
- **DespatchSummary**: Dispatch information with invoice details
- **StockSummary**: Total stock quantities

### Key Fields Returned
- Location, Work Order Number, Product Name
- WO Date, WO Quantity, OK Quantity, Stock Quantity
- Rejection Quantity, Dispatch Quantity, Aging in Days
- Status (RC Pending/Closed)

## 🌐 API Endpoints

- `GET /api/health` - Health check
- `GET /api/locations` - Available locations
- `GET /api/dashboard-stats` - Statistics for cards
- `GET /api/rc-pending` - Main RC data (supports locationId filter)

## 🎨 Dashboard Features

### Auto-refresh
- Data refreshes every 5 minutes automatically
- Visual indicators show auto-refresh status

### Auto-slide
- Automatically cycles through data pages
- 5-second intervals between slides
- Smooth transitions

### Location Filtering
- Dropdown to filter by specific locations
- "All Locations" option for complete view

### Responsive Design
- Works on desktop, tablet, and mobile
- Adaptive layout and font sizes
- Touch-friendly interface

## 🚨 Troubleshooting

### Common Issues

1. **"Route not found" Error**
   - Ensure server is running: `.\start-rc-dashboard.bat`
   - Check if port 5001 is available

2. **Database Connection Failed**
   - Verify SQL Server is running
   - Check database credentials in `backend/server.js`
   - Ensure network connectivity to database server

3. **Node.js Not Found**
   - Install Node.js from https://nodejs.org/
   - Restart command prompt after installation

4. **Dependencies Missing**
   - Run `.\install-rc-dashboard.bat`
   - Or manually: `cd backend && npm install`

### Debug Mode
Use `debug-rc-dashboard.html` for troubleshooting:
- Shows detailed API test results
- Displays error messages
- Logs all operations

## 📈 Performance

- **Optimized Queries**: Efficient SQL with proper indexing
- **Pagination**: Large datasets split into manageable pages
- **Caching**: Smart data caching for better performance
- **Compression**: Gzip compression for faster loading

## 🔒 Security

- **Rate Limiting**: API rate limiting to prevent abuse
- **CORS**: Configured for local development
- **Helmet**: Security headers for protection
- **Input Validation**: SQL injection prevention

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Run `.\test-rc-dashboard.bat` for diagnostics
3. Check browser console for JavaScript errors
4. Review server logs in the backend terminal

## 🔄 Updates

To update the system:
1. Stop the dashboard: `.\stop-rc-dashboard.bat`
2. Update files as needed
3. Restart: `.\start-rc-dashboard.bat`

---

**RC Dashboard v1.0** - Professional Route Card Management System

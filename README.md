# 🏭 RC Pending Dashboard

A beautiful, modern dashboard for managing Route Card (RC) pending status with location-wise filtering, real-time data visualization, and smooth animations.

## ✨ Features

- **🎨 Modern UI/UX**: Beautiful gradient backgrounds and clean design
- **📊 Real-time Data**: Live data from SQL Server database
- **🏢 Location-wise Filtering**: Dynamic filtering by location with instant updates
- **📋 Data Table**: Clean, sortable table with RC information
- **📱 Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **⚡ Real-time Updates**: Refresh functionality with live data
- **📈 Analytics Dashboard**: KPI cards and status distribution
- **🔗 API Integration**: RESTful APIs for all data operations

## 🛠️ Tech Stack

### Frontend
- **React 18** with Vite for fast development
- **Material-UI (MUI)** for component library
- **Modern CSS** with gradients and animations

### Backend
- **Node.js** with Express.js
- **SQL Server** database connection (ICsoft)
- **CORS** enabled for cross-origin requests
- **Security middleware** and rate limiting

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ installed
- SQL Server access to ICsoft database
- Windows environment

### Easy Installation & Startup

**Option 1: Use Batch Files (Recommended)**

1. **Install Dependencies:**
   ```cmd
   install-dependencies.bat
   ```

2. **Start Dashboard:**
   ```cmd
   start-dashboard.bat
   ```

3. **Stop Dashboard:**
   ```cmd
   stop-dashboard.bat
   ```

4. **Test Connections:**
   ```cmd
   test-connection.bat
   ```

**Option 2: Manual Setup**

1. **Install backend dependencies:**
   ```bash
   cd backend
   npm install
   ```

2. **Install frontend dependencies:**
   ```bash
   cd rc-dashboard
   npm install
   ```

3. **Start backend server:**
   ```bash
   cd backend
   npm start
   ```

4. **Start frontend server:**
   ```bash
   cd rc-dashboard
   npm run dev
   ```

**Option 3: Simple HTML Dashboard**

If React is not working, open `simple-dashboard.html` directly in your browser.

## 📊 API Endpoints

- `GET /api/health` - Health check
- `GET /api/rc-pending` - Get RC pending data with filters
- `GET /api/locations` - Get all available locations
- `GET /api/dashboard-stats` - Get dashboard statistics

### Query Parameters for `/api/rc-pending`:
- `locationId` - Filter by location (default: '%' for all)
- `prodId` - Filter by product ID (default: '%' for all)
- `startDate` - Start date filter (default: '2024-04-01')
- `endDate` - End date filter (default: current date)
- `status` - Status filter (default: 'Pending')

## 🎨 Dashboard Features

### 📈 Analytics Dashboard
- **KPI Cards**: Total RCs, Pending RCs, Completed RCs, Average Aging
- **Location Distribution**: Bar chart showing RC distribution by location
- **Status Overview**: Pie chart showing pending vs closed RCs
- **Aging Analysis**: Breakdown of RCs by aging buckets (0-7, 8-15, 16-30, 31+ days)

### 📋 Data Table
- **Advanced Filtering**: Search across all fields
- **Sortable Columns**: Click column headers to sort
- **Export Functionality**: Download filtered data as CSV
- **Detailed View**: Click eye icon to view detailed RC information
- **Status Indicators**: Color-coded status chips
- **Aging Indicators**: Color-coded aging values

### 🎛️ Interactive Features
- **Real-time Updates**: Auto-refresh every 30 seconds
- **Location Filtering**: Dynamic dropdown with all available locations
- **Responsive Sidebar**: Collapsible navigation with smooth animations
- **Loading States**: Beautiful loading animations during data fetch
- **Error Handling**: Graceful error handling with fallback data

## 🎨 Design System

### Colors
- **Primary**: Blue gradient (#3b82f6 to #1d4ed8)
- **Secondary**: Purple gradient (#8b5cf6 to #7c3aed)
- **Success**: Green (#22c55e)
- **Warning**: Orange (#f59e0b)
- **Danger**: Red (#ef4444)

### Animations
- **Page Transitions**: Smooth slide animations between views
- **Card Hover Effects**: Subtle lift and shadow effects
- **Loading States**: Shimmer and pulse animations
- **Button Interactions**: Scale and color transitions

## 🔧 Configuration

### Environment Variables (Backend)
Create a `.env` file in the backend directory:
```env
NODE_ENV=development
PORT=5000
DB_SERVER=WIN-PRK-SRV-01
DB_DATABASE=ICsoft
DB_TRUSTED_CONNECTION=true
DB_TRUST_SERVER_CERTIFICATE=true
```

### Customization
- **Colors**: Modify `tailwind.config.js` for custom color schemes
- **Animations**: Adjust Framer Motion variants in components
- **Charts**: Customize Recharts configuration in Dashboard component
- **Database**: Update SQL queries in `backend/server.js`

## 📱 Responsive Design

The dashboard is fully responsive and optimized for:
- **Desktop**: Full sidebar navigation and multi-column layouts
- **Tablet**: Collapsible sidebar with optimized spacing
- **Mobile**: Hidden sidebar with mobile-friendly navigation

## 🚀 Production Deployment

### Frontend Build
```bash
cd rc-dashboard
npm run build
```

### Backend Production
```bash
cd backend
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is proprietary software for internal use.

## 🆘 Support

For technical support or questions:
- Check the console for error messages
- Verify database connectivity
- Ensure all dependencies are installed
- Check network connectivity between frontend and backend

---

**Built with ❤️ for efficient Route Card management**

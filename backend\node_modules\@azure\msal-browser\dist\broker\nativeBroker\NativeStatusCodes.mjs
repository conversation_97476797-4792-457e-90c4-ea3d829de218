/*! @azure/msal-browser v4.13.2 2025-06-18 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
// Status Codes that can be thrown by WAM
const USER_INTERACTION_REQUIRED = "USER_INTERACTION_REQUIRED";
const USER_CANCEL = "USER_CANCEL";
const NO_NETWORK = "NO_NETWORK";
const PERSISTENT_ERROR = "PERSISTENT_ERROR";
const DISABLED = "DISABLED";
const ACCOUNT_UNAVAILABLE = "ACCOUNT_UNAVAILABLE";
const UX_NOT_ALLOWED = "UX_NOT_ALLOWED";

export { ACCOUNT_UNAVAILABLE, DISABLED, NO_NETWORK, PERSISTENT_ERROR, USER_CANCEL, USER_INTERACTION_REQUIRED, UX_NOT_ALLOWED };
//# sourceMappingURL=NativeStatusCodes.mjs.map

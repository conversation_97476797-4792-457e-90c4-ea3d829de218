import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Package,
  Factory,
  Calendar,
  BarChart3
} from 'lucide-react';
import { 
  Card, 
  CardContent, 
  Typography, 
  Grid, 
  Box,
  LinearProgress,
  Chip
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';

const Dashboard = ({ selectedLocation }) => {
  const [dashboardData, setDashboardData] = useState(null);
  const [rcData, setRcData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalRCs: 0,
    pendingRCs: 0,
    closedRCs: 0,
    avgAgingDays: 0
  });

  useEffect(() => {
    fetchDashboardData();
    
    // Listen for refresh events
    const handleRefresh = () => fetchDashboardData();
    window.addEventListener('refreshData', handleRefresh);
    
    return () => window.removeEventListener('refreshData', handleRefresh);
  }, [selectedLocation]);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      // Fetch dashboard stats
      const statsResponse = await fetch(
        `http://localhost:5000/api/dashboard-stats?locationId=${selectedLocation || '%'}`
      );
      const statsData = await statsResponse.json();
      
      // Fetch RC data
      const rcResponse = await fetch(
        `http://localhost:5000/api/rc-pending?locationId=${selectedLocation || '%'}`
      );
      const rcData = await rcResponse.json();

      if (statsData.success) {
        setStats(statsData.data);
      }
      
      if (rcData.success) {
        setRcData(rcData.data);
        processChartData(rcData.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Mock data for development
      setStats({
        totalRCs: 156,
        pendingRCs: 89,
        closedRCs: 67,
        avgAgingDays: 12.5
      });
      generateMockChartData();
    } finally {
      setLoading(false);
    }
  };

  const processChartData = (data) => {
    // Process data for charts
    const locationData = data.reduce((acc, item) => {
      const location = item.LocationID || 'Unknown';
      if (!acc[location]) {
        acc[location] = { location, pending: 0, closed: 0, total: 0 };
      }
      if (item.Status === 'RC Pending') {
        acc[location].pending++;
      } else {
        acc[location].closed++;
      }
      acc[location].total++;
      return acc;
    }, {});

    setDashboardData({
      locationChart: Object.values(locationData),
      agingChart: generateAgingData(data),
      statusPie: [
        { name: 'Pending', value: stats.pendingRCs, color: '#ef4444' },
        { name: 'Closed', value: stats.closedRCs, color: '#22c55e' }
      ]
    });
  };

  const generateAgingData = (data) => {
    const agingBuckets = {
      '0-7 days': 0,
      '8-15 days': 0,
      '16-30 days': 0,
      '31+ days': 0
    };

    data.forEach(item => {
      const aging = item.AgingInDays || 0;
      if (aging <= 7) agingBuckets['0-7 days']++;
      else if (aging <= 15) agingBuckets['8-15 days']++;
      else if (aging <= 30) agingBuckets['16-30 days']++;
      else agingBuckets['31+ days']++;
    });

    return Object.entries(agingBuckets).map(([range, count]) => ({
      range,
      count,
      percentage: ((count / data.length) * 100).toFixed(1)
    }));
  };

  const generateMockChartData = () => {
    setDashboardData({
      locationChart: [
        { location: 'LOC001', pending: 25, closed: 15, total: 40 },
        { location: 'LOC002', pending: 18, closed: 22, total: 40 },
        { location: 'MAIN', pending: 32, closed: 18, total: 50 },
        { location: 'WAREHOUSE', pending: 14, closed: 12, total: 26 }
      ],
      agingChart: [
        { range: '0-7 days', count: 35, percentage: '39.3' },
        { range: '8-15 days', count: 28, percentage: '31.5' },
        { range: '16-30 days', count: 18, percentage: '20.2' },
        { range: '31+ days', count: 8, percentage: '9.0' }
      ],
      statusPie: [
        { name: 'Pending', value: 89, color: '#ef4444' },
        { name: 'Closed', value: 67, color: '#22c55e' }
      ]
    });
  };

  const StatCard = ({ title, value, subtitle, icon: Icon, color, trend, trendValue }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ y: -4 }}
    >
      <Card className="card-hover h-full">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <Typography variant="body2" color="textSecondary" className="mb-2">
                {title}
              </Typography>
              <Typography variant="h3" className={`font-bold ${color} mb-1`}>
                {value}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {subtitle}
              </Typography>
              {trend && (
                <div className="flex items-center mt-2">
                  {trend === 'up' ? (
                    <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
                  )}
                  <Typography variant="caption" className={trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                    {trendValue}
                  </Typography>
                </div>
              )}
            </div>
            <div className={`p-3 rounded-xl ${color.replace('text-', 'bg-').replace('-600', '-100')}`}>
              <Icon className={`w-6 h-6 ${color}`} />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );

  const COLORS = ['#ef4444', '#22c55e', '#3b82f6', '#f59e0b'];

  if (loading) {
    return (
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="h-32">
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 overflow-y-auto h-full bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Stats Cards */}
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="Total Route Cards"
            value={stats.totalRCs}
            subtitle="All time"
            icon={Package}
            color="text-blue-600"
            trend="up"
            trendValue="+12% from last month"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="Pending RCs"
            value={stats.pendingRCs}
            subtitle="Awaiting completion"
            icon={Clock}
            color="text-orange-600"
            trend="down"
            trendValue="-5% from last week"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="Completed RCs"
            value={stats.closedRCs}
            subtitle="Successfully closed"
            icon={CheckCircle}
            color="text-green-600"
            trend="up"
            trendValue="+8% from last week"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="Avg Aging"
            value={`${stats.avgAgingDays?.toFixed(1) || 0} days`}
            subtitle="Pending RCs"
            icon={AlertCircle}
            color="text-red-600"
            trend="down"
            trendValue="-2.3 days improved"
          />
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3}>
        {/* Location-wise Distribution */}
        <Grid item xs={12} lg={8}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card className="h-96">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <Typography variant="h6" className="font-semibold">
                    Location-wise RC Distribution
                  </Typography>
                  <Chip 
                    label={selectedLocation || "All Locations"} 
                    color="primary" 
                    size="small" 
                  />
                </div>
                <ResponsiveContainer width="100%" height={280}>
                  <BarChart data={dashboardData?.locationChart || []}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="location" stroke="#6b7280" />
                    <YAxis stroke="#6b7280" />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: '#ffffff',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Bar dataKey="pending" fill="#ef4444" name="Pending" radius={[4, 4, 0, 0]} />
                    <Bar dataKey="closed" fill="#22c55e" name="Closed" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Status Pie Chart */}
        <Grid item xs={12} lg={4}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card className="h-96">
              <CardContent className="p-6">
                <Typography variant="h6" className="font-semibold mb-4">
                  RC Status Overview
                </Typography>
                <ResponsiveContainer width="100%" height={280}>
                  <PieChart>
                    <Pie
                      data={dashboardData?.statusPie || []}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {dashboardData?.statusPie?.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                <div className="flex justify-center space-x-4 mt-4">
                  {dashboardData?.statusPie?.map((entry, index) => (
                    <div key={index} className="flex items-center">
                      <div 
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: entry.color }}
                      ></div>
                      <Typography variant="caption">
                        {entry.name}: {entry.value}
                      </Typography>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Aging Analysis */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7 }}
      >
        <Card>
          <CardContent className="p-6">
            <Typography variant="h6" className="font-semibold mb-4">
              RC Aging Analysis
            </Typography>
            <Grid container spacing={3}>
              {dashboardData?.agingChart?.map((item, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <Typography variant="h4" className="font-bold text-gray-900 mb-1">
                      {item.count}
                    </Typography>
                    <Typography variant="body2" color="textSecondary" className="mb-2">
                      {item.range}
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={parseFloat(item.percentage)} 
                      className="mb-1"
                      sx={{
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: '#e5e7eb',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: COLORS[index % COLORS.length],
                          borderRadius: 3,
                        },
                      }}
                    />
                    <Typography variant="caption" color="textSecondary">
                      {item.percentage}%
                    </Typography>
                  </div>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default Dashboard;

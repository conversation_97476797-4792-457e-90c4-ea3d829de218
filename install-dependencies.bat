@echo off
echo ========================================
echo    RC Dashboard Dependencies Installer
echo ========================================
echo.

echo [1/4] Installing Backend Dependencies...
cd backend
echo Installing backend packages...
npm install
if %errorlevel% neq 0 (
    echo ERROR: Backend dependency installation failed!
    pause
    exit /b 1
)

echo.
echo [2/4] Installing Frontend Dependencies...
cd ..\rc-dashboard
echo Installing frontend packages...
npm install
if %errorlevel% neq 0 (
    echo ERROR: Frontend dependency installation failed!
    pause
    exit /b 1
)

echo.
echo [3/4] Verifying installations...
echo Checking backend...
cd ..\backend
npm list --depth=0 | findstr "express"
echo Checking frontend...
cd ..\rc-dashboard
npm list --depth=0 | findstr "react"

echo.
echo [4/4] Installation Complete!
cd ..

echo.
echo ========================================
echo    Dependencies Installed Successfully!
echo ========================================
echo.
echo You can now run: start-dashboard.bat
echo.
echo Press any key to close this window...
pause >nul

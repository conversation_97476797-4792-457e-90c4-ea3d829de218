{"version": 3, "file": "keyVaultKeyPoller.js", "sourceRoot": "", "sources": ["../../../src/lro/keyVaultKeyPoller.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AAwBzC;;GAEG;AACH,MAAM,OAAgB,iBAGpB,SAAQ,MAAuB;IAHjC;;QAIE;;WAEG;QACI,iBAAY,GAAW,IAAI,CAAC;IAQrC,CAAC;IANC;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAClC,CAAC;CACF;AASD;;GAEG;AACH,MAAM,OAAO,wBAAwB;IAGnC,YACS,KAAa,EACpB,UAA2C,EAAE;QADtC,UAAK,GAAL,KAAK,CAAQ;QAHd,kBAAa,GAAW,EAAE,CAAC;QAMjC,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,MAAM;QACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,MAAM;QACjB,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OperationOptions } from \"@azure-rest/core-client\";\nimport { delay } from \"@azure/core-util\";\nimport type { PollOperation, PollOperationState } from \"@azure/core-lro\";\nimport { Poller } from \"@azure/core-lro\";\nimport type { KeyVaultClient } from \"../generated/keyVaultClient.js\";\n\n/**\n * Common parameters to a Key Vault Key Poller.\n */\nexport interface KeyVaultKeyPollerOptions {\n  client: KeyVaultClient;\n  name: string;\n  operationOptions?: OperationOptions;\n  intervalInMs?: number;\n  resumeFrom?: string;\n}\n\n/**\n * An interface representing the state of a Key Vault Key Poller's operation.\n */\nexport interface KeyVaultKeyPollOperationState<TResult> extends PollOperationState<TResult> {\n  /**\n   * The name of the key.\n   */\n  name: string;\n}\n\n/**\n * Common properties and methods of the Key Vault Key Pollers.\n */\nexport abstract class KeyVaultKeyPoller<\n  TState extends KeyVaultKeyPollOperationState<TResult>,\n  TResult,\n> extends Poller<TState, TResult> {\n  /**\n   * Defines how much time the poller is going to wait before making a new request to the service.\n   */\n  public intervalInMs: number = 2000;\n\n  /**\n   * The method used by the poller to wait before attempting to update its operation.\n   */\n  async delay(): Promise<void> {\n    return delay(this.intervalInMs);\n  }\n}\n\n/**\n * Optional parameters to the KeyVaultKeyPollOperation\n */\nexport interface KeyVaultKeyPollOperationOptions {\n  cancelMessage?: string;\n}\n\n/**\n * Common properties and methods of the Key Vault Key Poller operations.\n */\nexport class KeyVaultKeyPollOperation<TState, TResult> implements PollOperation<TState, TResult> {\n  private cancelMessage: string = \"\";\n\n  constructor(\n    public state: TState,\n    options: KeyVaultKeyPollOperationOptions = {},\n  ) {\n    if (options.cancelMessage) {\n      this.cancelMessage = options.cancelMessage;\n    }\n  }\n\n  /**\n   * Meant to reach to the service and update the Poller operation.\n   */\n  public async update(): Promise<PollOperation<TState, TResult>> {\n    throw new Error(\"Operation not supported.\");\n  }\n\n  /**\n   * Meant to reach to the service and cancel the Poller operation.\n   */\n  public async cancel(): Promise<PollOperation<TState, TResult>> {\n    throw new Error(this.cancelMessage);\n  }\n\n  /**\n   * Serializes the Poller operation.\n   */\n  public toString(): string {\n    return JSON.stringify({\n      state: this.state,\n    });\n  }\n}\n"]}
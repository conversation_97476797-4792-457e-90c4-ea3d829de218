import type { AccessToken, TokenCredential } from "@azure/core-auth";
/**
 * Enables authentication to Microsoft Entra ID using the [On Behalf Of flow](https://learn.microsoft.com/entra/identity-platform/v2-oauth2-on-behalf-of-flow).
 */
export declare class OnBehalfOfCredential implements TokenCredential {
    /**
     * Only available in Node.js
     */
    constructor();
    getToken(): Promise<AccessToken | null>;
}
//# sourceMappingURL=onBehalfOfCredential-browser.d.mts.map
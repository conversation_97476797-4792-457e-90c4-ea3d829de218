<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Table Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 12px;
        }
        th {
            background: #4a90e2;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        tr:nth-child(even) {
            background: #f9f9f9;
        }
        tr:hover {
            background: #e3f2fd;
        }
        .btn {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #357abd;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .table-container {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Simple Table Test</h1>
        
        <div class="status info" id="status">Ready to load data...</div>
        
        <button class="btn" onclick="loadData()">Load RC Data</button>
        <button class="btn" onclick="clearTable()">Clear Table</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }
        
        async function loadData() {
            try {
                setStatus('Loading data...', 'info');
                
                const response = await fetch(`${API_BASE}/rc-pending`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error('API returned success=false: ' + (data.error || 'Unknown error'));
                }
                
                if (!data.data || !Array.isArray(data.data)) {
                    throw new Error('No data array in response');
                }
                
                setStatus(`✅ Loaded ${data.data.length} records successfully`, 'success');
                displayTable(data.data);
                
            } catch (error) {
                setStatus('❌ Error: ' + error.message, 'error');
                console.error('Full error:', error);
            }
        }
        
        function displayTable(records) {
            const resultsDiv = document.getElementById('results');
            
            if (records.length === 0) {
                resultsDiv.innerHTML = '<div class="status error">No records to display</div>';
                return;
            }
            
            let tableHTML = `
                <h3>RC Pending Records (${records.length} total)</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Work Order</th>
                                <th>Product Name</th>
                                <th>WO Date</th>
                                <th>WO Qty</th>
                                <th>OK Qty</th>
                                <th>Aging (Days)</th>
                                <th>Location</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            records.forEach((record, index) => {
                try {
                    // Extract product name from PartNoAndProductName
                    const fullProductName = record.PartNoAndProductName || 'Unknown';
                    const productName = fullProductName.includes(' | ') 
                        ? fullProductName.split(' | ')[1] 
                        : fullProductName;
                    
                    tableHTML += `
                        <tr>
                            <td>${record.WoNo || 'N/A'}</td>
                            <td title="${fullProductName}">${productName}</td>
                            <td>${record.WODate || 'N/A'}</td>
                            <td style="text-align: right;">${(record.WO_Qty || 0).toLocaleString()}</td>
                            <td style="text-align: right;">${(record.TotalOkQty || 0).toLocaleString()}</td>
                            <td style="text-align: center;">${record.AgingInDays || 0}</td>
                            <td>${record.Location || 'Unknown'}</td>
                            <td>${record.Status || 'N/A'}</td>
                        </tr>
                    `;
                } catch (err) {
                    console.error(`Error processing record ${index}:`, err, record);
                }
            });
            
            tableHTML += `
                        </tbody>
                    </table>
                </div>
            `;
            
            resultsDiv.innerHTML = tableHTML;
        }
        
        function clearTable() {
            document.getElementById('results').innerHTML = '';
            setStatus('Table cleared', 'info');
        }
        
        // Auto-load on page load
        window.addEventListener('load', () => {
            setTimeout(loadData, 1000);
        });
    </script>
</body>
</html>

{"version": 3, "file": "tokenExchangeMsi.js", "sourceRoot": "", "sources": ["../../../../src/credentials/managedIdentityCredential/tokenExchangeMsi.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,0BAA0B,EAAE,MAAM,kCAAkC,CAAC;AAC9E,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAGzD,MAAM,OAAO,GAAG,4CAA4C,CAAC;AAC7D,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAEzC;;;;;GAKG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG;IAC9B,IAAI,EAAE,kBAAkB;IACxB,KAAK,CAAC,WAAW,CAAC,QAAiB;QACjC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACxB,MAAM,MAAM,GAAG,OAAO,CACpB,CAAC,QAAQ,IAAI,GAAG,CAAC,eAAe,CAAC;YAC/B,GAAG,CAAC,eAAe;YACnB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CACzC,CAAC;QACF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,CAAC,IAAI,CACT,GAAG,OAAO,qKAAqK,CAChL,CAAC;QACJ,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,KAAK,CAAC,QAAQ,CACZ,aAA+B,EAC/B,kBAAmC,EAAE;QAErC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,aAAa,CAAC;QAC3C,MAAM,oCAAoC,GAAG,EAAE,CAAC;QAChD,MAAM,0BAA0B,GAAG,IAAI,0BAA0B,CAAC,8BAChE,QAAQ,EACR,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,EACrC,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAClD,oCAAoC,KACvC,wBAAwB,EAAE,IAAI,GACM,CAAC,CAAC;QACxC,OAAO,0BAA0B,CAAC,QAAQ,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;IACtE,CAAC;CACF,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport type { MSIConfiguration } from \"./models.js\";\nimport { WorkloadIdentityCredential } from \"../workloadIdentityCredential.js\";\nimport { credentialLogger } from \"../../util/logging.js\";\nimport type { WorkloadIdentityCredentialOptions } from \"../workloadIdentityCredentialOptions.js\";\n\nconst msiName = \"ManagedIdentityCredential - Token Exchange\";\nconst logger = credentialLogger(msiName);\n\n/**\n * Defines how to determine whether the token exchange MSI is available, and also how to retrieve a token from the token exchange MSI.\n *\n * Token exchange MSI (used by AKS) is the only MSI implementation handled entirely by Azure Identity.\n * The rest have been migrated to MSAL.\n */\nexport const tokenExchangeMsi = {\n  name: \"tokenExchangeMsi\",\n  async isAvailable(clientId?: string): Promise<boolean> {\n    const env = process.env;\n    const result = Boolean(\n      (clientId || env.AZURE_CLIENT_ID) &&\n        env.AZURE_TENANT_ID &&\n        process.env.AZURE_FEDERATED_TOKEN_FILE,\n    );\n    if (!result) {\n      logger.info(\n        `${msiName}: Unavailable. The environment variables needed are: AZURE_CLIENT_ID (or the client ID sent through the parameters), AZURE_TENANT_ID and AZURE_FEDERATED_TOKEN_FILE`,\n      );\n    }\n    return result;\n  },\n  async getToken(\n    configuration: MSIConfiguration,\n    getTokenOptions: GetTokenOptions = {},\n  ): Promise<AccessToken | null> {\n    const { scopes, clientId } = configuration;\n    const identityClientTokenCredentialOptions = {};\n    const workloadIdentityCredential = new WorkloadIdentityCredential({\n      clientId,\n      tenantId: process.env.AZURE_TENANT_ID,\n      tokenFilePath: process.env.AZURE_FEDERATED_TOKEN_FILE,\n      ...identityClientTokenCredentialOptions,\n      disableInstanceDiscovery: true,\n    } as WorkloadIdentityCredentialOptions);\n    return workloadIdentityCredential.getToken(scopes, getTokenOptions);\n  },\n};\n"]}
{"version": 3, "file": "symmetric-key-cache.js", "names": ["_<PERSON><PERSON><PERSON>", "_interopRequireDefault", "require", "_lru<PERSON>ache", "obj", "__esModule", "default", "cache", "LRU", "<PERSON><PERSON><PERSON>", "keyInfo", "options", "trustedServerNameAE", "Error", "serverName", "keyLookupValue", "<PERSON><PERSON><PERSON>", "from", "encrypted<PERSON>ey", "toString", "keyStoreName", "has", "get", "provider", "encryptionKeyStoreProviders", "plaintextKey", "decryptColumnEncryptionKey", "keyP<PERSON>", "algorithmName", "<PERSON><PERSON><PERSON>", "SymmetricKey", "columnEncryptionKeyCacheTTL", "set", "exports"], "sources": ["../../src/always-encrypted/symmetric-key-cache.ts"], "sourcesContent": ["// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.\n// Copyright (c) 2019 Microsoft Corporation\n\nimport { type EncryptionKeyInfo } from './types';\nimport SymmetricKey from './symmetric-key';\nimport { type InternalConnectionOptions as ConnectionOptions } from '../connection';\nimport LRU from 'lru-cache';\n\nconst cache = new LRU<string, SymmetricKey>(0);\n\nexport const getKey = async (keyInfo: EncryptionKeyInfo, options: ConnectionOptions): Promise<SymmetricKey> => {\n  if (!options.trustedServerNameAE) {\n    throw new Error('Server name should not be null in getKey');\n  }\n\n  const serverName: string = options.trustedServerNameAE;\n\n  const keyLookupValue = `${serverName}:${Buffer.from(keyInfo.encryptedKey).toString('base64')}:${keyInfo.keyStoreName}`;\n\n  if (cache.has(keyLookupValue)) {\n    return cache.get(keyLookupValue) as SymmetricKey;\n  } else {\n    const provider = options.encryptionKeyStoreProviders && options.encryptionKeyStoreProviders[keyInfo.keyStoreName];\n    if (!provider) {\n      throw new Error(`Failed to decrypt a column encryption key. Invalid key store provider name: ${keyInfo.keyStoreName}. A key store provider name must denote either a system key store provider or a registered custom key store provider. Valid (currently registered) custom key store provider names are: ${options.encryptionKeyStoreProviders}. Please verify key store provider information in column master key definitions in the database, and verify all custom key store providers used in your application are registered properly.`);\n    }\n\n    const plaintextKey: Buffer = await provider.decryptColumnEncryptionKey(keyInfo.keyPath, keyInfo.algorithmName, keyInfo.encryptedKey);\n\n    const encryptionKey = new SymmetricKey(plaintextKey);\n\n    if (options.columnEncryptionKeyCacheTTL > 0) {\n      cache.set(keyLookupValue, encryptionKey, options.columnEncryptionKeyCacheTTL);\n    }\n\n    return encryptionKey;\n  }\n};\n"], "mappings": ";;;;;;AAIA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AAA4B,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAN5B;AACA;;AAOA,MAAMG,KAAK,GAAG,IAAIC,iBAAG,CAAuB,CAAC,CAAC;AAEvC,MAAMC,MAAM,GAAG,MAAAA,CAAOC,OAA0B,EAAEC,OAA0B,KAA4B;EAC7G,IAAI,CAACA,OAAO,CAACC,mBAAmB,EAAE;IAChC,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;EAC7D;EAEA,MAAMC,UAAkB,GAAGH,OAAO,CAACC,mBAAmB;EAEtD,MAAMG,cAAc,GAAI,GAAED,UAAW,IAAGE,MAAM,CAACC,IAAI,CAACP,OAAO,CAACQ,YAAY,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAE,IAAGT,OAAO,CAACU,YAAa,EAAC;EAEtH,IAAIb,KAAK,CAACc,GAAG,CAACN,cAAc,CAAC,EAAE;IAC7B,OAAOR,KAAK,CAACe,GAAG,CAACP,cAAc,CAAC;EAClC,CAAC,MAAM;IACL,MAAMQ,QAAQ,GAAGZ,OAAO,CAACa,2BAA2B,IAAIb,OAAO,CAACa,2BAA2B,CAACd,OAAO,CAACU,YAAY,CAAC;IACjH,IAAI,CAACG,QAAQ,EAAE;MACb,MAAM,IAAIV,KAAK,CAAE,+EAA8EH,OAAO,CAACU,YAAa,2LAA0LT,OAAO,CAACa,2BAA4B,8LAA6L,CAAC;IAClhB;IAEA,MAAMC,YAAoB,GAAG,MAAMF,QAAQ,CAACG,0BAA0B,CAAChB,OAAO,CAACiB,OAAO,EAAEjB,OAAO,CAACkB,aAAa,EAAElB,OAAO,CAACQ,YAAY,CAAC;IAEpI,MAAMW,aAAa,GAAG,IAAIC,qBAAY,CAACL,YAAY,CAAC;IAEpD,IAAId,OAAO,CAACoB,2BAA2B,GAAG,CAAC,EAAE;MAC3CxB,KAAK,CAACyB,GAAG,CAACjB,cAAc,EAAEc,aAAa,EAAElB,OAAO,CAACoB,2BAA2B,CAAC;IAC/E;IAEA,OAAOF,aAAa;EACtB;AACF,CAAC;AAACI,OAAA,CAAAxB,MAAA,GAAAA,MAAA"}
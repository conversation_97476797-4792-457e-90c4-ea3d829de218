@echo off
echo ========================================
echo    QUICK START - RC DASHBOARD
echo ========================================
echo.

echo Starting backend server...
cd /d "d:\RC pending Dashboard\backend"
start "RC Dashboard Backend" cmd /k "echo RC Dashboard Backend Starting... && npm start"

echo Waiting for backend to initialize...
timeout /t 15 /nobreak >nul

echo Opening dashboard...
cd /d "d:\RC pending Dashboard"
start simple-dashboard.html

echo.
echo ✅ Dashboard system started!
echo ✅ Backend running in separate window
echo ✅ Dashboard opened in browser
echo.
echo Features:
echo - Auto-slide with vertical scrolling
echo - Auto-refresh every 5 minutes
echo - Real-time SQL Server data
echo.
echo Keep backend window open!
pause

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug RC Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 12px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        th {
            background: #4a90e2;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        tr:nth-child(even) {
            background: #f9f9f9;
        }
        tr:hover {
            background: #e3f2fd;
        }
        .btn {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #357abd;
        }
        .table-container {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .qty-cell {
            text-align: right;
            font-weight: 500;
        }
        .aging-cell {
            text-align: center;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
        }
        .aging-15 { background: #27ae60; }
        .aging-45 { background: #f39c12; }
        .aging-90 { background: #e74c3c; }
        .aging-over { background: #8e44ad; }
        #log {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug RC Dashboard</h1>
        
        <div class="status info" id="status">Ready to test...</div>
        
        <button class="btn" onclick="testAPI()">Test API</button>
        <button class="btn" onclick="loadData()">Load Data</button>
        <button class="btn" onclick="clearLog()">Clear Log</button>
        
        <div id="log"></div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }
        
        async function testAPI() {
            try {
                log('Testing API endpoints...');
                setStatus('Testing API...', 'info');
                
                // Test health
                const healthResponse = await fetch(`${API_BASE}/health`);
                const healthData = await healthResponse.json();
                log('✅ Health endpoint: ' + JSON.stringify(healthData));
                
                // Test locations
                const locResponse = await fetch(`${API_BASE}/locations`);
                const locData = await locResponse.json();
                log('✅ Locations endpoint: ' + locData.data?.length + ' locations');
                
                // Test dashboard stats
                const statsResponse = await fetch(`${API_BASE}/dashboard-stats`);
                const statsData = await statsResponse.json();
                log('✅ Dashboard stats: ' + JSON.stringify(statsData.data));
                
                setStatus('API tests completed', 'success');
                
            } catch (error) {
                log('❌ API test failed: ' + error.message);
                setStatus('API test failed: ' + error.message, 'error');
            }
        }
        
        async function loadData() {
            try {
                log('Loading RC pending data...');
                setStatus('Loading data...', 'info');
                
                const response = await fetch(`${API_BASE}/rc-pending`);
                log('Response status: ' + response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log('Response received, parsing...');
                
                if (!data.success) {
                    throw new Error('API returned success=false: ' + (data.error || 'Unknown error'));
                }
                
                if (!data.data || !Array.isArray(data.data)) {
                    throw new Error('No data array in response');
                }
                
                log(`✅ Data loaded successfully: ${data.data.length} records`);
                setStatus(`Data loaded: ${data.data.length} records`, 'success');
                
                // Display data in table
                displayTable(data.data);
                
            } catch (error) {
                log('❌ Error loading data: ' + error.message);
                setStatus('Error: ' + error.message, 'error');
                console.error('Full error:', error);
            }
        }
        
        function displayTable(records) {
            log('Rendering table with ' + records.length + ' records...');
            
            const resultsDiv = document.getElementById('results');
            
            if (records.length === 0) {
                resultsDiv.innerHTML = '<div class="status error">No records to display</div>';
                return;
            }
            
            // Show first 100 records for performance
            const displayRecords = records.slice(0, 100);
            
            let tableHTML = `
                <h3>RC Pending Records (showing first 100 of ${records.length} total)</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Work Order</th>
                                <th>Product Name</th>
                                <th>WO Date</th>
                                <th>WO Qty</th>
                                <th>OK Qty</th>
                                <th>Stock Qty</th>
                                <th>Rej Qty</th>
                                <th>Desp Qty</th>
                                <th>Aging</th>
                                <th>Location</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            displayRecords.forEach((record, index) => {
                try {
                    const productName = (record.PartNoAndProductName || 'Unknown').split(' | ')[1] || 'Unknown';
                    const agingClass = getAgingClass(record.AgingInDays);
                    
                    tableHTML += `
                        <tr>
                            <td>${record.WoNo || 'N/A'}</td>
                            <td title="${record.PartNoAndProductName || 'Unknown'}">${productName}</td>
                            <td>${record.WODate || 'N/A'}</td>
                            <td class="qty-cell">${(record.WO_Qty || 0).toLocaleString()}</td>
                            <td class="qty-cell">${(record.TotalOkQty || 0).toLocaleString()}</td>
                            <td class="qty-cell">${(record.TotalStockQty || 0).toLocaleString()}</td>
                            <td class="qty-cell">${(record.TotalRejQty || 0).toLocaleString()}</td>
                            <td class="qty-cell">${(record.DespQty || 0).toLocaleString()}</td>
                            <td class="aging-cell ${agingClass}">${record.AgingInDays || 0}</td>
                            <td>${record.Location || 'Unknown'}</td>
                        </tr>
                    `;
                } catch (err) {
                    log(`Error processing record ${index}: ${err.message}`);
                }
            });
            
            tableHTML += `
                        </tbody>
                    </table>
                </div>
            `;
            
            resultsDiv.innerHTML = tableHTML;
            log('✅ Table rendered successfully');
        }
        
        function getAgingClass(days) {
            if (days <= 15) return 'aging-15';
            if (days <= 45) return 'aging-45';
            if (days <= 90) return 'aging-90';
            return 'aging-over';
        }
        
        // Auto-load on page load
        window.addEventListener('load', () => {
            log('Page loaded, ready for testing');
            setTimeout(() => {
                testAPI();
                setTimeout(() => {
                    loadData();
                }, 2000);
            }, 1000);
        });
    </script>
</body>
</html>

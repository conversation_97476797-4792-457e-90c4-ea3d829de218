import type { AccessToken, TokenCredential } from "@azure/core-auth";
/**
 * WorkloadIdentityCredential supports Microsoft Entra Workload ID authentication on Kubernetes.
 * Refer to <a href="https://learn.microsoft.com/azure/aks/workload-identity-overview">Microsoft Entra Workload ID</a>
 * for more information.
 */
export declare class WorkloadIdentityCredential implements TokenCredential {
    /**
     * Only available in Node.js
     */
    constructor();
    /**
     * Only available in Node.js
     */
    getToken(): Promise<AccessToken | null>;
}
//# sourceMappingURL=workloadIdentityCredential-browser.d.mts.map
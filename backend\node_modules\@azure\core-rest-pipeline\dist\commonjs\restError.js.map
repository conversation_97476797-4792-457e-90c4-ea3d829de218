{"version": 3, "file": "restError.js", "sourceRoot": "", "sources": ["../../src/restError.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAgGlC,kCAEC;AA9FD,+DAGmC;AA+EnC;;GAEG;AACH,2DAA2D;AAC9C,QAAA,SAAS,GAAyB,2BAAoC,CAAC;AAEpF;;;GAGG;AACH,SAAgB,WAAW,CAAC,CAAU;IACpC,OAAO,IAAA,6BAAc,EAAC,CAAC,CAAC,CAAC;AAC3B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineRequest, PipelineResponse } from \"./interfaces.js\";\n\nimport {\n  RestError as TspRestError,\n  isRestError as tspIsRestError,\n} from \"@typespec/ts-http-runtime\";\n\n/**\n * The options supported by RestError.\n */\nexport interface RestErrorOptions {\n  /**\n   * The code of the error itself (use statics on RestError if possible.)\n   */\n  code?: string;\n  /**\n   * The HTTP status code of the request (if applicable.)\n   */\n  statusCode?: number;\n  /**\n   * The request that was made.\n   */\n  request?: PipelineRequest;\n  /**\n   * The response received (if any.)\n   */\n  response?: PipelineResponse;\n}\n\n/**\n * A custom error type for failed pipeline requests.\n */\nexport interface RestErrorConstructor {\n  /**\n   * Something went wrong when making the request.\n   * This means the actual request failed for some reason,\n   * such as a DNS issue or the connection being lost.\n   */\n  readonly REQUEST_SEND_ERROR: string;\n  /**\n   * This means that parsing the response from the server failed.\n   * It may have been malformed.\n   */\n  readonly PARSE_ERROR: string;\n\n  /**\n   * Prototype of RestError\n   */\n  readonly prototype: RestError;\n\n  /**\n   * Construct a new RestError.\n   */\n  new (message: string, options?: RestErrorOptions): RestError;\n}\n\n/**\n * A custom error type for failed pipeline requests.\n */\nexport interface RestError extends Error {\n  /**\n   * The code of the error itself (use statics on RestError if possible.)\n   */\n  code?: string;\n  /**\n   * The HTTP status code of the request (if applicable.)\n   */\n  statusCode?: number;\n  /**\n   * The request that was made.\n   * This property is non-enumerable.\n   */\n  request?: PipelineRequest;\n  /**\n   * The response received (if any.)\n   * This property is non-enumerable.\n   */\n  response?: PipelineResponse;\n  /**\n   * Bonus property set by the throw site.\n   */\n  details?: unknown;\n}\n\n/**\n * A custom error type for failed pipeline requests.\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const RestError: RestErrorConstructor = TspRestError as RestErrorConstructor;\n\n/**\n * Typeguard for RestError\n * @param e - Something caught by a catch clause.\n */\nexport function isRestError(e: unknown): e is RestError {\n  return tspIsRestError(e);\n}\n"]}